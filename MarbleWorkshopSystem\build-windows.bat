@echo off
chcp 65001 > nul
title بناء نظام إدارة ورشة الرخام للويندوز

echo ========================================
echo    بناء نظام إدارة ورشة الرخام
echo    Building Marble Workshop System
echo ========================================
echo.

REM التحقق من وجود .NET SDK
echo جاري التحقق من .NET SDK...
echo Checking .NET SDK...

dotnet --version > nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo [خطأ] .NET SDK غير مثبت
    echo [ERROR] .NET SDK is not installed
    echo.
    echo يرجى تحميل وتثبيت .NET 8.0 SDK من:
    echo Please download and install .NET 8.0 SDK from:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    pause
    exit /b 1
)

REM عرض إصدار .NET
for /f "tokens=1" %%i in ('dotnet --version') do set dotnet_version=%%i
echo تم العثور على .NET SDK إصدار: %dotnet_version%
echo Found .NET SDK version: %dotnet_version%

REM تنظيف المشروع
echo.
echo جاري تنظيف المشروع...
echo Cleaning project...
dotnet clean --configuration Release

REM استعادة الحزم
echo.
echo جاري استعادة الحزم...
echo Restoring packages...
dotnet restore

REM بناء المشروع
echo.
echo جاري بناء المشروع...
echo Building project...
dotnet build --configuration Release --no-restore

if %errorlevel% neq 0 (
    echo.
    echo [خطأ] فشل في بناء المشروع
    echo [ERROR] Build failed
    pause
    exit /b 1
)

REM نشر التطبيق للويندوز
echo.
echo جاري نشر التطبيق للويندوز...
echo Publishing application for Windows...

REM نشر نسخة مستقلة (Self-contained)
dotnet publish --configuration Release --runtime win-x64 --self-contained true --output "bin\Release\net8.0\publish\win-x64-standalone" /p:PublishSingleFile=true /p:IncludeNativeLibrariesForSelfExtract=true

if %errorlevel% neq 0 (
    echo.
    echo [خطأ] فشل في نشر النسخة المستقلة
    echo [ERROR] Self-contained publish failed
    pause
    exit /b 1
)

REM نشر نسخة تعتمد على Framework
dotnet publish --configuration Release --runtime win-x64 --self-contained false --output "bin\Release\net8.0\publish\win-x64" /p:PublishSingleFile=true

if %errorlevel% neq 0 (
    echo.
    echo [خطأ] فشل في نشر النسخة المعتمدة على Framework
    echo [ERROR] Framework-dependent publish failed
    pause
    exit /b 1
)

REM نسخ ملفات التشغيل
echo.
echo جاري نسخ ملفات التشغيل...
echo Copying runtime files...

copy "start-marble-workshop.bat" "bin\Release\net8.0\publish\win-x64\"
copy "install-service.bat" "bin\Release\net8.0\publish\win-x64\"
copy "uninstall-service.bat" "bin\Release\net8.0\publish\win-x64\"
copy "README-Windows.md" "bin\Release\net8.0\publish\win-x64\"

copy "start-marble-workshop.bat" "bin\Release\net8.0\publish\win-x64-standalone\"
copy "install-service.bat" "bin\Release\net8.0\publish\win-x64-standalone\"
copy "uninstall-service.bat" "bin\Release\net8.0\publish\win-x64-standalone\"
copy "README-Windows.md" "bin\Release\net8.0\publish\win-x64-standalone\"

REM إنشاء ملف معلومات الإصدار
echo.
echo جاري إنشاء ملف معلومات الإصدار...
echo Creating version info file...

echo نظام إدارة ورشة الرخام > "bin\Release\net8.0\publish\win-x64\VERSION.txt"
echo Marble Workshop Management System >> "bin\Release\net8.0\publish\win-x64\VERSION.txt"
echo. >> "bin\Release\net8.0\publish\win-x64\VERSION.txt"
echo الإصدار / Version: 1.0.0 >> "bin\Release\net8.0\publish\win-x64\VERSION.txt"
echo تاريخ البناء / Build Date: %date% %time% >> "bin\Release\net8.0\publish\win-x64\VERSION.txt"
echo .NET Version: %dotnet_version% >> "bin\Release\net8.0\publish\win-x64\VERSION.txt"
echo Target: Windows 7/8/10/11 (x64) >> "bin\Release\net8.0\publish\win-x64\VERSION.txt"

copy "bin\Release\net8.0\publish\win-x64\VERSION.txt" "bin\Release\net8.0\publish\win-x64-standalone\"

echo.
echo ✓ تم بناء المشروع بنجاح!
echo ✓ Project built successfully!
echo.
echo المجلدات المنشورة:
echo Published folders:
echo.
echo 1. النسخة المعتمدة على Framework (تتطلب .NET Runtime):
echo    Framework-dependent (requires .NET Runtime):
echo    bin\Release\net8.0\publish\win-x64\
echo.
echo 2. النسخة المستقلة (لا تتطلب .NET Runtime):
echo    Self-contained (no .NET Runtime required):
echo    bin\Release\net8.0\publish\win-x64-standalone\
echo.
echo للتشغيل:
echo To run:
echo 1. انسخ أحد المجلدين إلى الكمبيوتر المستهدف
echo    Copy one of the folders to the target computer
echo 2. شغل start-marble-workshop.bat
echo    Run start-marble-workshop.bat
echo.
pause
