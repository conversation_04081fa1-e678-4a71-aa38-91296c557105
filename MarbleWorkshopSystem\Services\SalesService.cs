using Microsoft.EntityFrameworkCore;
using MarbleWorkshopSystem.Data;
using MarbleWorkshopSystem.Models;

namespace MarbleWorkshopSystem.Services
{
    public class SalesService : ISalesService
    {
        private readonly ApplicationDbContext _context;
        private readonly IInventoryService _inventoryService;
        private readonly ICustomerService _customerService;

        public SalesService(ApplicationDbContext context, IInventoryService inventoryService, ICustomerService customerService)
        {
            _context = context;
            _inventoryService = inventoryService;
            _customerService = customerService;
        }

        public async Task<IEnumerable<SalesInvoice>> GetAllSalesInvoicesAsync()
        {
            return await _context.SalesInvoices
                .Include(s => s.Customer)
                .Include(s => s.Items)
                .ThenInclude(i => i.Product)
                .OrderByDescending(s => s.InvoiceDate)
                .ToListAsync();
        }

        public async Task<SalesInvoice?> GetSalesInvoiceByIdAsync(int id)
        {
            return await _context.SalesInvoices
                .Include(s => s.Customer)
                .Include(s => s.Items)
                .ThenInclude(i => i.Product)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<IEnumerable<SalesInvoice>> GetSalesInvoicesByCustomerAsync(int customerId)
        {
            return await _context.SalesInvoices
                .Include(s => s.Customer)
                .Include(s => s.Items)
                .ThenInclude(i => i.Product)
                .Where(s => s.CustomerId == customerId)
                .OrderByDescending(s => s.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<SalesInvoice>> GetSalesInvoicesByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            return await _context.SalesInvoices
                .Include(s => s.Customer)
                .Include(s => s.Items)
                .ThenInclude(i => i.Product)
                .Where(s => s.InvoiceDate >= fromDate && s.InvoiceDate <= toDate)
                .OrderByDescending(s => s.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<SalesInvoice>> GetSalesInvoicesByStatusAsync(string status)
        {
            return await _context.SalesInvoices
                .Include(s => s.Customer)
                .Include(s => s.Items)
                .ThenInclude(i => i.Product)
                .Where(s => s.Status == status)
                .OrderByDescending(s => s.InvoiceDate)
                .ToListAsync();
        }

        public async Task<SalesInvoice> CreateSalesInvoiceAsync(SalesInvoice invoice)
        {
            // توليد رقم الفاتورة
            invoice.InvoiceNumber = await GenerateInvoiceNumberAsync();
            invoice.InvoiceDate = DateTime.Now;
            invoice.Status = "مسودة";

            // حساب المجاميع
            await CalculateInvoiceTotalsAsync(invoice);

            _context.SalesInvoices.Add(invoice);
            await _context.SaveChangesAsync();

            return invoice;
        }

        public async Task<SalesInvoice> UpdateSalesInvoiceAsync(SalesInvoice invoice)
        {
            // حساب المجاميع
            await CalculateInvoiceTotalsAsync(invoice);

            _context.SalesInvoices.Update(invoice);
            await _context.SaveChangesAsync();

            return invoice;
        }

        public async Task DeleteSalesInvoiceAsync(int id)
        {
            var invoice = await GetSalesInvoiceByIdAsync(id);
            if (invoice != null)
            {
                // إذا كانت الفاتورة مؤكدة، يجب إرجاع الكميات للمخزون
                if (invoice.Status == "مؤكدة")
                {
                    foreach (var item in invoice.Items)
                    {
                        await _inventoryService.AddStockAsync(item.ProductId, item.Quantity);
                    }
                }

                _context.SalesInvoices.Remove(invoice);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<SalesInvoice> ConfirmSalesInvoiceAsync(int id)
        {
            var invoice = await GetSalesInvoiceByIdAsync(id);
            if (invoice == null)
                throw new InvalidOperationException("الفاتورة غير موجودة");

            if (invoice.Status == "مؤكدة")
                throw new InvalidOperationException("الفاتورة مؤكدة مسبقاً");

            // التحقق من توفر المخزون
            if (!await HasSufficientStockAsync(invoice))
                throw new InvalidOperationException("المخزون غير كافي لبعض المنتجات");

            // خصم الكميات من المخزون
            foreach (var item in invoice.Items)
            {
                await _inventoryService.ReduceStockAsync(item.ProductId, item.Quantity);
            }

            // تحديث رصيد العميل
            await _customerService.UpdateCustomerBalanceAsync(invoice.CustomerId, invoice.TotalAmount);

            invoice.Status = "مؤكدة";
            await UpdateSalesInvoiceAsync(invoice);

            return invoice;
        }

        public async Task<SalesInvoice> CancelSalesInvoiceAsync(int id)
        {
            var invoice = await GetSalesInvoiceByIdAsync(id);
            if (invoice == null)
                throw new InvalidOperationException("الفاتورة غير موجودة");

            if (invoice.Status == "ملغية")
                throw new InvalidOperationException("الفاتورة ملغية مسبقاً");

            // إذا كانت مؤكدة، يجب إرجاع الكميات والرصيد
            if (invoice.Status == "مؤكدة")
            {
                foreach (var item in invoice.Items)
                {
                    await _inventoryService.AddStockAsync(item.ProductId, item.Quantity);
                }
                await _customerService.UpdateCustomerBalanceAsync(invoice.CustomerId, -invoice.TotalAmount);
            }

            invoice.Status = "ملغية";
            await UpdateSalesInvoiceAsync(invoice);

            return invoice;
        }

        public async Task<string> GenerateInvoiceNumberAsync()
        {
            var lastInvoice = await _context.SalesInvoices
                .OrderByDescending(s => s.Id)
                .FirstOrDefaultAsync();

            var nextNumber = (lastInvoice?.Id ?? 0) + 1;
            return $"INV-{DateTime.Now:yyyyMM}-{nextNumber:D4}";
        }

        public async Task<decimal> CalculateInvoiceTotalAsync(SalesInvoice invoice)
        {
            await CalculateInvoiceTotalsAsync(invoice);
            return invoice.TotalAmount;
        }

        public async Task<bool> HasSufficientStockAsync(SalesInvoice invoice)
        {
            foreach (var item in invoice.Items)
            {
                var availableStock = await _inventoryService.GetAvailableStockAsync(item.ProductId);
                if (availableStock < item.Quantity)
                    return false;
            }
            return true;
        }

        public async Task<IEnumerable<SalesInvoice>> SearchInvoicesAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllSalesInvoicesAsync();

            return await _context.SalesInvoices
                .Include(s => s.Customer)
                .Include(s => s.Items)
                .ThenInclude(i => i.Product)
                .Where(s => s.InvoiceNumber.Contains(searchTerm) ||
                           s.Customer.Name.Contains(searchTerm) ||
                           s.Notes!.Contains(searchTerm))
                .OrderByDescending(s => s.InvoiceDate)
                .ToListAsync();
        }

        public async Task<decimal> GetTotalSalesAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.SalesInvoices.Where(s => s.Status == "مؤكدة");

            if (fromDate.HasValue)
                query = query.Where(s => s.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(s => s.InvoiceDate <= toDate.Value);

            return await query.SumAsync(s => s.TotalAmount);
        }

        public async Task<int> GetInvoiceCountAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.SalesInvoices.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(s => s.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(s => s.InvoiceDate <= toDate.Value);

            return await query.CountAsync();
        }

        public async Task<decimal> GetAverageSaleAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.SalesInvoices.Where(s => s.Status == "مؤكدة");

            if (fromDate.HasValue)
                query = query.Where(s => s.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(s => s.InvoiceDate <= toDate.Value);

            return await query.AnyAsync() ? await query.AverageAsync(s => s.TotalAmount) : 0;
        }

        private async Task CalculateInvoiceTotalsAsync(SalesInvoice invoice)
        {
            // حساب إجمالي كل عنصر
            foreach (var item in invoice.Items)
            {
                // حساب الكمية من الطول والعرض إذا كانت متوفرة
                if (item.Length > 0 && item.Width > 0)
                {
                    item.Quantity = item.Length * item.Width;
                }

                item.TotalPrice = item.Quantity * item.UnitPrice;
            }

            // حساب المجموع الفرعي
            invoice.SubTotal = invoice.Items.Sum(i => i.TotalPrice);

            // حساب قيمة الخصم
            var discountAmount = invoice.DiscountType switch
            {
                DiscountType.Percentage => invoice.SubTotal * (invoice.DiscountValue / 100),
                DiscountType.FixedAmount => invoice.DiscountValue,
                _ => 0
            };

            // حساب المجموع النهائي
            invoice.TotalAmount = invoice.SubTotal - discountAmount;

            await Task.CompletedTask;
        }
    }
}
