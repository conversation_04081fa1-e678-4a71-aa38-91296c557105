@model MarbleWorkshopSystem.Models.Inventory

@{
    ViewData["Title"] = "حركة المخزون";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exchange-alt me-2"></i>
                        حركة مخزون المنتج
                    </h4>
                </div>

                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }

                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }

                    <!-- معلومات المنتج -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-cube me-2"></i>
                                        معلومات المنتج
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3">
                                            @if (!string.IsNullOrEmpty(Model.Product.ImagePath))
                                            {
                                                <img src="@Model.Product.ImagePath" alt="@Model.Product.Name" 
                                                     class="img-thumbnail" style="max-height: 150px; width: 100%; object-fit: cover;">
                                            }
                                            else
                                            {
                                                <div class="bg-secondary text-white d-flex align-items-center justify-content-center" 
                                                     style="height: 150px; border-radius: 8px;">
                                                    <i class="fas fa-image fa-3x"></i>
                                                </div>
                                            }
                                        </div>
                                        <div class="col-md-9">
                                            <h5 class="text-primary">@Model.Product.Name</h5>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p class="mb-1"><strong>الفئة:</strong> @Model.Product.Category</p>
                                                    <p class="mb-1"><strong>المقاس:</strong> @Model.Product.Length × @Model.Product.Width</p>
                                                    <p class="mb-1"><strong>المساحة:</strong> @Model.Product.Area متر مربع</p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p class="mb-1"><strong>سعر الوحدة:</strong> @Model.Product.UnitPrice.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</p>
                                                    <p class="mb-1"><strong>آخر تحديث:</strong> @Model.LastUpdated.ToString("dd/MM/yyyy HH:mm")</p>
                                                </div>
                                            </div>
                                            @if (!string.IsNullOrEmpty(Model.Product.Description))
                                            {
                                                <p class="text-muted mt-2">@Model.Product.Description</p>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- حالة المخزون الحالية -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Quantity.ToString("F2")</h4>
                                    <p class="mb-0">الكمية الحالية</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.AvailableQuantity.ToString("F2")</h4>
                                    <p class="mb-0">الكمية المتاحة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.WasteQuantity.ToString("F2")</h4>
                                    <p class="mb-0">كمية الهالك</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card @(Model.IsLowStock ? "bg-danger" : "bg-info") text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.MinimumStock.ToString("F2")</h4>
                                    <p class="mb-0">الحد الأدنى</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- عمليات المخزون -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-plus me-2"></i>
                                        إضافة مخزون
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form asp-action="AddStock" method="post">
                                        <input type="hidden" name="productId" value="@Model.ProductId" />
                                        
                                        <div class="mb-3">
                                            <label class="form-label">
                                                <i class="fas fa-cubes me-1"></i>
                                                الكمية المراد إضافتها <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="quantity" 
                                                       step="0.01" min="0.01" required placeholder="أدخل الكمية">
                                                <span class="input-group-text">وحدة</span>
                                            </div>
                                        </div>
                                        
                                        <button type="submit" class="btn btn-success w-100">
                                            <i class="fas fa-plus me-1"></i>
                                            إضافة للمخزون
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-minus me-2"></i>
                                        خصم مخزون
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form asp-action="ReduceStock" method="post">
                                        <input type="hidden" name="productId" value="@Model.ProductId" />
                                        
                                        <div class="mb-3">
                                            <label class="form-label">
                                                <i class="fas fa-cubes me-1"></i>
                                                الكمية المراد خصمها <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="quantity" 
                                                       step="0.01" min="0.01" max="@Model.AvailableQuantity" 
                                                       required placeholder="أدخل الكمية">
                                                <span class="input-group-text">وحدة</span>
                                            </div>
                                            <div class="form-text">
                                                الحد الأقصى: @Model.AvailableQuantity.ToString("F2") وحدة
                                            </div>
                                        </div>
                                        
                                        <button type="submit" class="btn btn-warning w-100">
                                            <i class="fas fa-minus me-1"></i>
                                            خصم من المخزون
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-trash me-2"></i>
                                        إضافة هالك
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form asp-action="AddWaste" method="post">
                                        <input type="hidden" name="productId" value="@Model.ProductId" />
                                        
                                        <div class="mb-3">
                                            <label class="form-label">
                                                <i class="fas fa-trash me-1"></i>
                                                كمية الهالك <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="wasteQuantity" 
                                                       step="0.01" min="0.01" max="@Model.Quantity" 
                                                       required placeholder="أدخل كمية الهالك">
                                                <span class="input-group-text">وحدة</span>
                                            </div>
                                            <div class="form-text">
                                                الحد الأقصى: @Model.Quantity.ToString("F2") وحدة
                                            </div>
                                        </div>
                                        
                                        <button type="submit" class="btn btn-danger w-100">
                                            <i class="fas fa-trash me-1"></i>
                                            إضافة هالك
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات مهمة -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-info-circle me-2"></i>
                                        ملاحظات مهمة
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <ul class="mb-0">
                                                <li><strong>إضافة المخزون:</strong> يزيد الكمية الإجمالية</li>
                                                <li><strong>خصم المخزون:</strong> يقلل من الكمية المتاحة</li>
                                                <li><strong>إضافة الهالك:</strong> يقلل من الكمية المتاحة دون تقليل الإجمالية</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <ul class="mb-0">
                                                <li><strong>الكمية المتاحة</strong> = الكمية الحالية - كمية الهالك</li>
                                                <li><strong>تنبيه المخزون المنخفض</strong> يظهر عند الوصول للحد الأدنى</li>
                                                <li><strong>جميع العمليات</strong> يتم تسجيلها مع التاريخ والوقت</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <div>
                                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                                        <i class="fas fa-edit me-1"></i>
                                        تعديل المخزون
                                    </a>
                                    <a asp-action="Index" class="btn btn-primary">
                                        <i class="fas fa-list me-1"></i>
                                        جميع المنتجات
                                    </a>
                                </div>
                                <div>
                                    <a asp-controller="Products" asp-action="Details" asp-route-id="@Model.ProductId" class="btn btn-info">
                                        <i class="fas fa-eye me-1"></i>
                                        تفاصيل المنتج
                                    </a>
                                    @if (Model.IsLowStock)
                                    {
                                        <a asp-action="LowStock" class="btn btn-danger">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            المخزون المنخفض
                                        </a>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // التحقق من صحة البيانات
            $('input[name="quantity"]').on('input', function() {
                const value = parseFloat($(this).val()) || 0;
                const max = parseFloat($(this).attr('max')) || Infinity;
                
                if (value > max) {
                    $(this).addClass('is-invalid');
                    $(this).next('.input-group-text').after('<div class="invalid-feedback">الكمية تتجاوز الحد المسموح</div>');
                } else {
                    $(this).removeClass('is-invalid');
                    $(this).siblings('.invalid-feedback').remove();
                }
            });
        });
    </script>
}
