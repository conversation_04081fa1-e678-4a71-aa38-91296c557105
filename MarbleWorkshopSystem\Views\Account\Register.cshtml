@model MarbleWorkshopSystem.ViewModels.RegisterViewModel

@{
    ViewData["Title"] = "إنشاء حساب جديد";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-lg">
                <div class="card-header text-center">
                    <h3 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        إنشاء حساب جديد
                    </h3>
                </div>
                <div class="card-body">
                    <form asp-action="Register" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label asp-for="FullName" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    الاسم الكامل *
                                </label>
                                <input asp-for="FullName" class="form-control" placeholder="أدخل الاسم الكامل" />
                                <span asp-validation-for="FullName" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    البريد الإلكتروني *
                                </label>
                                <input asp-for="Email" class="form-control" placeholder="أدخل البريد الإلكتروني" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="PhoneNumber" class="form-label">
                                    <i class="fas fa-phone me-1"></i>
                                    رقم الهاتف
                                </label>
                                <input asp-for="PhoneNumber" class="form-control" placeholder="أدخل رقم الهاتف" />
                                <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label asp-for="Address" class="form-label">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    العنوان
                                </label>
                                <input asp-for="Address" class="form-control" placeholder="أدخل العنوان" />
                                <span asp-validation-for="Address" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label asp-for="NationalId" class="form-label">
                                    <i class="fas fa-id-card me-1"></i>
                                    الرقم القومي
                                </label>
                                <input asp-for="NationalId" class="form-control" placeholder="الرقم القومي" />
                                <span asp-validation-for="NationalId" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    كلمة المرور *
                                </label>
                                <input asp-for="Password" class="form-control" placeholder="أدخل كلمة المرور" />
                                <span asp-validation-for="Password" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="ConfirmPassword" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    تأكيد كلمة المرور *
                                </label>
                                <input asp-for="ConfirmPassword" class="form-control" placeholder="أعد إدخال كلمة المرور" />
                                <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-user-plus me-1"></i>
                                إنشاء الحساب
                            </button>
                        </div>
                    </form>

                    <hr>
                    
                    <div class="text-center">
                        <p class="mb-0">
                            لديك حساب بالفعل؟ 
                            <a asp-action="Login" class="text-decoration-none">
                                تسجيل الدخول
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
