using MarbleWorkshopSystem.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;

namespace MarbleWorkshopSystem.Data
{
    public static class SeedData
    {
        public static async Task InitializeAsync(ApplicationDbContext context, UserManager<ApplicationUser>? userManager = null, RoleManager<IdentityRole>? roleManager = null)
        {
            // التأكد من إنشاء قاعدة البيانات
            await context.Database.EnsureCreatedAsync();

            // إنشاء الأدوار الافتراضية
            if (roleManager != null)
            {
                await CreateRolesAsync(roleManager);
            }

            // تم تعطيل إنشاء المستخدم الافتراضي - سيتم إنشاؤه من صفحة CreateFirstUser
            /*
            if (userManager != null)
            {
                await CreateDefaultUserAsync(userManager);
            }
            */

            // التحقق من وجود بيانات مسبقة
            if (await context.Products.AnyAsync())
            {
                return; // البيانات موجودة مسبقاً
            }

            // إنشاء منتجات تجريبية
            var products = new List<Product>
            {
                new Product
                {
                    Name = "رخام كرارا أبيض",
                    Description = "رخام إيطالي فاخر بجودة عالية",
                    Category = "رخام طبيعي",
                    Length = 3.0m,
                    Width = 2.0m,
                    Area = 6.0m,
                    UnitPrice = 1500.00m,
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    IsActive = true
                },
                new Product
                {
                    Name = "جرانيت أسود مصري",
                    Description = "جرانيت محلي عالي الجودة",
                    Category = "جرانيت",
                    Length = 2.5m,
                    Width = 1.5m,
                    Area = 3.75m,
                    UnitPrice = 800.00m,
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    IsActive = true
                },
                new Product
                {
                    Name = "رخام أحمر أسوان",
                    Description = "رخام مصري تقليدي",
                    Category = "رخام طبيعي",
                    Length = 2.0m,
                    Width = 1.0m,
                    Area = 2.0m,
                    UnitPrice = 600.00m,
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    IsActive = true
                },
                new Product
                {
                    Name = "بورسلين إسباني",
                    Description = "بلاط بورسلين مستورد",
                    Category = "بورسلين",
                    Length = 1.2m,
                    Width = 0.6m,
                    Area = 0.72m,
                    UnitPrice = 250.00m,
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    IsActive = true
                },
                new Product
                {
                    Name = "ترافرتين تركي",
                    Description = "حجر طبيعي تركي",
                    Category = "حجر طبيعي",
                    Length = 4.0m,
                    Width = 2.0m,
                    Area = 8.0m,
                    UnitPrice = 900.00m,
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    IsActive = true
                }
            };

            await context.Products.AddRangeAsync(products);
            await context.SaveChangesAsync();

            // إنشاء سجلات مخزون للمنتجات
            var inventories = new List<Inventory>();
            foreach (var product in products)
            {
                inventories.Add(new Inventory
                {
                    ProductId = product.Id,
                    Quantity = new Random().Next(10, 100),
                    MinimumStock = 5,
                    WasteQuantity = 0,
                    LastUpdated = DateTime.Now
                });
            }

            await context.Inventories.AddRangeAsync(inventories);
            await context.SaveChangesAsync();

            // إنشاء عملاء تجريبيين
            var customers = new List<Customer>
            {
                new Customer
                {
                    Name = "أحمد محمد علي",
                    Phone = "0*********0",
                    Email = "<EMAIL>",
                    Address = "القاهرة، مصر الجديدة",
                    TaxNumber = "*********",
                    Balance = 0,
                    CreatedDate = DateTime.Now,
                    IsActive = true
                },
                new Customer
                {
                    Name = "شركة الإنشاءات الحديثة",
                    Phone = "01098765432",
                    Email = "<EMAIL>",
                    Address = "الجيزة، المهندسين",
                    TaxNumber = "*********",
                    Balance = 0,
                    CreatedDate = DateTime.Now,
                    IsActive = true
                },
                new Customer
                {
                    Name = "فاطمة حسن",
                    Phone = "01555123456",
                    Email = "<EMAIL>",
                    Address = "الإسكندرية، سيدي جابر",
                    TaxNumber = "*********",
                    Balance = 0,
                    CreatedDate = DateTime.Now,
                    IsActive = true
                }
            };

            await context.Customers.AddRangeAsync(customers);
            await context.SaveChangesAsync();

            // إنشاء موردين تجريبيين
            var suppliers = new List<Supplier>
            {
                new Supplier
                {
                    Name = "شركة الرخام الإيطالي",
                    Phone = "0*********0",
                    Email = "<EMAIL>",
                    Address = "القاهرة، وسط البلد",
                    TaxNumber = "*********",
                    Balance = 0,
                    CreatedDate = DateTime.Now,
                    IsActive = true
                },
                new Supplier
                {
                    Name = "مصانع الجرانيت المصري",
                    Phone = "01*********",
                    Email = "<EMAIL>",
                    Address = "أسوان، الصعيد",
                    TaxNumber = "*********",
                    Balance = 0,
                    CreatedDate = DateTime.Now,
                    IsActive = true
                }
            };

            await context.Suppliers.AddRangeAsync(suppliers);
            await context.SaveChangesAsync();

            // إنشاء مصروفات تجريبية
            var expenses = new List<Expense>
            {
                new Expense
                {
                    Description = "فاتورة كهرباء الورشة",
                    Amount = 1200.00m,
                    Category = "مرافق",
                    ExpenseDate = DateTime.Now.AddDays(-10),
                    Notes = "فاتورة شهر ديسمبر"
                },
                new Expense
                {
                    Description = "صيانة المعدات",
                    Amount = 800.00m,
                    Category = "صيانة",
                    ExpenseDate = DateTime.Now.AddDays(-5),
                    Notes = "صيانة دورية للمعدات"
                },
                new Expense
                {
                    Description = "مواد تنظيف",
                    Amount = 150.00m,
                    Category = "مستلزمات",
                    ExpenseDate = DateTime.Now.AddDays(-3),
                    Notes = "مواد تنظيف الورشة"
                },
                new Expense
                {
                    Description = "وقود النقل",
                    Amount = 500.00m,
                    Category = "نقل",
                    ExpenseDate = DateTime.Now.AddDays(-1),
                    Notes = "وقود سيارة التوصيل"
                }
            };

            await context.Expenses.AddRangeAsync(expenses);
            await context.SaveChangesAsync();

            // إنشاء عمليات تقطيع تجريبية
            var cuttingOperations = new List<CuttingOperation>();
            foreach (var product in products.Take(3))
            {
                cuttingOperations.Add(new CuttingOperation
                {
                    ProductId = product.Id,
                    OriginalQuantity = 10.0m,
                    UsedQuantity = 8.5m,
                    WasteQuantity = 1.5m,
                    WastePercentage = 15.0m,
                    CuttingDate = DateTime.Now.AddDays(-new Random().Next(1, 30)),
                    Notes = $"عملية تقطيع {product.Name}"
                });
            }

            await context.CuttingOperations.AddRangeAsync(cuttingOperations);
            await context.SaveChangesAsync();

            // إنشاء فواتير مبيعات تجريبية مع الطول والعرض
            var salesInvoices = new List<SalesInvoice>();
            for (int i = 1; i <= 3; i++)
            {
                var invoice = new SalesInvoice
                {
                    InvoiceNumber = $"INV-{DateTime.Now:yyyyMM}-{i:D4}",
                    CustomerId = customers[i - 1].Id,
                    InvoiceDate = DateTime.Now.AddDays(-i * 5),
                    Status = i == 1 ? "مؤكدة" : "مسودة",
                    DiscountType = DiscountType.Percentage,
                    DiscountValue = 5,
                    Notes = $"فاتورة مبيعات تجريبية رقم {i}",
                    Items = new List<SalesInvoiceItem>
                    {
                        new SalesInvoiceItem
                        {
                            ProductId = products[0].Id,
                            Length = 2.5m,
                            Width = 1.2m,
                            Quantity = 2.5m * 1.2m, // 3.0 م²
                            UnitPrice = products[0].UnitPrice,
                            TotalPrice = (2.5m * 1.2m) * products[0].UnitPrice,
                            Notes = "قطعة رخام للمطبخ - أبعاد مخصصة"
                        },
                        new SalesInvoiceItem
                        {
                            ProductId = products[1].Id,
                            Length = 1.8m,
                            Width = 0.6m,
                            Quantity = 1.8m * 0.6m, // 1.08 م²
                            UnitPrice = products[1].UnitPrice,
                            TotalPrice = (1.8m * 0.6m) * products[1].UnitPrice,
                            Notes = "قطعة جرانيت للحمام"
                        }
                    }
                };

                // حساب المجاميع
                invoice.SubTotal = invoice.Items.Sum(item => item.TotalPrice);
                var discountAmount = invoice.SubTotal * (invoice.DiscountValue / 100);
                invoice.TotalAmount = invoice.SubTotal - discountAmount;

                salesInvoices.Add(invoice);
            }

            await context.SalesInvoices.AddRangeAsync(salesInvoices);
            await context.SaveChangesAsync();

            // إنشاء فواتير مشتريات تجريبية مع الطول والعرض
            var purchaseInvoices = new List<PurchaseInvoice>();
            for (int i = 1; i <= 2; i++)
            {
                var invoice = new PurchaseInvoice
                {
                    InvoiceNumber = $"PUR-{DateTime.Now:yyyyMM}-{i:D4}",
                    SupplierId = suppliers[i - 1].Id,
                    InvoiceDate = DateTime.Now.AddDays(-i * 7),
                    Status = "مؤكدة",
                    DiscountType = DiscountType.FixedAmount,
                    DiscountValue = 100,
                    Notes = $"فاتورة مشتريات تجريبية رقم {i}",
                    Items = new List<PurchaseInvoiceItem>
                    {
                        new PurchaseInvoiceItem
                        {
                            ProductId = products[i - 1].Id,
                            Length = 3.0m,
                            Width = 2.0m,
                            Quantity = 3.0m * 2.0m, // 6.0 م²
                            UnitPrice = products[i - 1].UnitPrice * 0.8m, // سعر الشراء أقل من البيع
                            TotalPrice = (3.0m * 2.0m) * (products[i - 1].UnitPrice * 0.8m),
                            Notes = "لوح كامل من المورد"
                        }
                    }
                };

                // حساب المجاميع
                invoice.SubTotal = invoice.Items.Sum(item => item.TotalPrice);
                invoice.TotalAmount = invoice.SubTotal - invoice.DiscountValue;

                purchaseInvoices.Add(invoice);
            }

            await context.PurchaseInvoices.AddRangeAsync(purchaseInvoices);
            await context.SaveChangesAsync();
        }

        private static async Task CreateRolesAsync(RoleManager<IdentityRole> roleManager)
        {
            string[] roleNames = { "مدير", "مستخدم", "محاسب", "مشرف مخزن" };

            foreach (var roleName in roleNames)
            {
                if (!await roleManager.RoleExistsAsync(roleName))
                {
                    await roleManager.CreateAsync(new IdentityRole(roleName));
                }
            }
        }

        private static async Task CreateDefaultUserAsync(UserManager<ApplicationUser> userManager)
        {
            // إنشاء مستخدم بسيط
            var simpleUser = await userManager.FindByNameAsync("admin");
            if (simpleUser == null)
            {
                simpleUser = new ApplicationUser
                {
                    UserName = "admin",
                    Email = "<EMAIL>",
                    FullName = "مدير النظام",
                    PhoneNumber = "0*********0",
                    Address = "القاهرة، مصر",
                    NationalId = "*********01234",
                    EmailConfirmed = true,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };

                var result = await userManager.CreateAsync(simpleUser, "123456");

                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(simpleUser, "مدير");
                    Console.WriteLine("تم إنشاء المستخدم admin بنجاح - اسم المستخدم: admin، كلمة المرور: 123456");
                }
                else
                {
                    Console.WriteLine("فشل في إنشاء المستخدم admin:");
                    foreach (var error in result.Errors)
                    {
                        Console.WriteLine($"- {error.Description}");
                    }
                }
            }
            else
            {
                Console.WriteLine("المستخدم admin موجود بالفعل");
            }

            // إنشاء مستخدم بالبريد الإلكتروني أيضاً
            var adminEmail = "<EMAIL>";
            var adminUser = await userManager.FindByEmailAsync(adminEmail);

            if (adminUser == null && simpleUser?.Email != adminEmail)
            {
                adminUser = new ApplicationUser
                {
                    UserName = adminEmail,
                    Email = adminEmail,
                    FullName = "مدير النظام - البريد",
                    PhoneNumber = "0*********0",
                    Address = "القاهرة، مصر",
                    NationalId = "*********01235",
                    EmailConfirmed = true,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };

                var result = await userManager.CreateAsync(adminUser, "admin123");

                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(adminUser, "مدير");
                    Console.WriteLine("تم إنشاء المستخدم بالبريد الإلكتروني بنجاح");
                }
            }
        }
    }
}
