using Microsoft.AspNetCore.Mvc;
using MarbleWorkshopSystem.Models;
using MarbleWorkshopSystem.Services;
using MarbleWorkshopSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace MarbleWorkshopSystem.Controllers
{
    public class ReceiptsController : Controller
    {
        private readonly IRepository<Receipt> _receiptRepository;
        private readonly ICustomerService _customerService;
        private readonly ApplicationDbContext _context;

        public ReceiptsController(
            IRepository<Receipt> receiptRepository,
            ICustomerService customerService,
            ApplicationDbContext context)
        {
            _receiptRepository = receiptRepository;
            _customerService = customerService;
            _context = context;
        }

        // GET: Receipts
        public async Task<IActionResult> Index(int? customerId, DateTime? fromDate, DateTime? toDate)
        {
            var receipts = await _context.Receipts
                .Include(r => r.Customer)
                .ToListAsync();

            // تطبيق الفلاتر
            if (customerId.HasValue)
            {
                receipts = receipts.Where(r => r.CustomerId == customerId.Value).ToList();
            }

            if (fromDate.HasValue)
            {
                receipts = receipts.Where(r => r.ReceiptDate >= fromDate.Value).ToList();
            }

            if (toDate.HasValue)
            {
                receipts = receipts.Where(r => r.ReceiptDate <= toDate.Value).ToList();
            }

            ViewBag.Customers = await _customerService.GetAllCustomersAsync();
            ViewBag.SelectedCustomerId = customerId;
            ViewBag.FromDate = fromDate?.ToString("yyyy-MM-dd");
            ViewBag.ToDate = toDate?.ToString("yyyy-MM-dd");

            return View(receipts.OrderByDescending(r => r.ReceiptDate));
        }

        // GET: Receipts/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var receipt = await _context.Receipts
                .Include(r => r.Customer)
                .FirstOrDefaultAsync(r => r.Id == id);

            if (receipt == null)
            {
                return NotFound();
            }

            return View(receipt);
        }

        // GET: Receipts/Create
        public async Task<IActionResult> Create()
        {
            ViewBag.Customers = await _customerService.GetAllCustomersAsync();
            
            var receipt = new Receipt
            {
                ReceiptDate = DateTime.Now,
                ReceiptNumber = await GenerateReceiptNumberAsync()
            };

            return View(receipt);
        }

        // POST: Receipts/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Receipt receipt)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    receipt.ReceiptNumber = await GenerateReceiptNumberAsync();
                    
                    await _receiptRepository.AddAsync(receipt);
                    await _receiptRepository.SaveChangesAsync();

                    // تحديث رصيد العميل
                    await _customerService.UpdateCustomerBalanceAsync(receipt.CustomerId, -receipt.Amount);

                    TempData["SuccessMessage"] = "تم إنشاء سند القبض بنجاح";
                    return RedirectToAction(nameof(Details), new { id = receipt.Id });
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "حدث خطأ أثناء إنشاء سند القبض: " + ex.Message);
                }
            }

            ViewBag.Customers = await _customerService.GetAllCustomersAsync();
            return View(receipt);
        }

        // GET: Receipts/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var receipt = await _context.Receipts
                .Include(r => r.Customer)
                .FirstOrDefaultAsync(r => r.Id == id);

            if (receipt == null)
            {
                return NotFound();
            }

            ViewBag.Customers = await _customerService.GetAllCustomersAsync();
            return View(receipt);
        }

        // POST: Receipts/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Receipt receipt)
        {
            if (id != receipt.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // الحصول على السند الأصلي لمعرفة المبلغ القديم
                    var originalReceipt = await _receiptRepository.GetByIdAsync(id);
                    if (originalReceipt != null)
                    {
                        // إرجاع المبلغ القديم
                        await _customerService.UpdateCustomerBalanceAsync(originalReceipt.CustomerId, originalReceipt.Amount);
                        
                        // خصم المبلغ الجديد
                        await _customerService.UpdateCustomerBalanceAsync(receipt.CustomerId, -receipt.Amount);
                    }

                    await _receiptRepository.UpdateAsync(receipt);
                    await _receiptRepository.SaveChangesAsync();
                    
                    TempData["SuccessMessage"] = "تم تحديث سند القبض بنجاح";
                    return RedirectToAction(nameof(Details), new { id });
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "حدث خطأ أثناء تحديث سند القبض: " + ex.Message);
                }
            }

            ViewBag.Customers = await _customerService.GetAllCustomersAsync();
            return View(receipt);
        }

        // GET: Receipts/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var receipt = await _context.Receipts
                .Include(r => r.Customer)
                .FirstOrDefaultAsync(r => r.Id == id);

            if (receipt == null)
            {
                return NotFound();
            }

            return View(receipt);
        }

        // POST: Receipts/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var receipt = await _receiptRepository.GetByIdAsync(id);
                if (receipt != null)
                {
                    // إرجاع المبلغ لرصيد العميل
                    await _customerService.UpdateCustomerBalanceAsync(receipt.CustomerId, receipt.Amount);
                    
                    await _receiptRepository.DeleteAsync(id);
                    await _receiptRepository.SaveChangesAsync();
                }
                
                TempData["SuccessMessage"] = "تم حذف سند القبض بنجاح";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف سند القبض: " + ex.Message;
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: Receipts/Print/5
        public async Task<IActionResult> Print(int id, string format = "A4")
        {
            var receipt = await _context.Receipts
                .Include(r => r.Customer)
                .FirstOrDefaultAsync(r => r.Id == id);

            if (receipt == null)
            {
                return NotFound();
            }

            ViewBag.PrintFormat = format;
            return View(receipt);
        }

        // AJAX: Get Customer Balance
        [HttpGet]
        public async Task<IActionResult> GetCustomerBalance(int customerId)
        {
            var balance = await _customerService.GetCustomerBalanceAsync(customerId);
            return Json(new { balance = balance });
        }

        private async Task<string> GenerateReceiptNumberAsync()
        {
            var lastReceipt = await _context.Receipts
                .OrderByDescending(r => r.Id)
                .FirstOrDefaultAsync();

            var nextNumber = (lastReceipt?.Id ?? 0) + 1;
            return $"REC-{DateTime.Now:yyyyMM}-{nextNumber:D4}";
        }
    }
}
