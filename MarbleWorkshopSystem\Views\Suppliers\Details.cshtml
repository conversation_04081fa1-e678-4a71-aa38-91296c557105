@model MarbleWorkshopSystem.Models.Supplier

@{
    ViewData["Title"] = "تفاصيل المورد";
    var invoices = ViewBag.Invoices as IEnumerable<MarbleWorkshopSystem.Models.PurchaseInvoice>;
    var payments = ViewBag.Payments as IEnumerable<MarbleWorkshopSystem.Models.Payment>;
    var totalPurchases = ViewBag.TotalPurchases as decimal? ?? 0;
    var totalPayments = ViewBag.TotalPayments as decimal? ?? 0;
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-truck me-2"></i>
                        تفاصيل المورد: @Model.Name
                    </h4>
                </div>

                <div class="card-body">
                    <!-- معلومات المورد -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        المعلومات الأساسية
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>اسم المورد:</strong></td>
                                            <td>@Model.Name</td>
                                        </tr>
                                        <tr>
                                            <td><strong>رقم الهاتف:</strong></td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(Model.Phone))
                                                {
                                                    <i class="fas fa-phone me-1"></i>@Model.Phone
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>البريد الإلكتروني:</strong></td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(Model.Email))
                                                {
                                                    <i class="fas fa-envelope me-1"></i>@Model.Email
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>العنوان:</strong></td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(Model.Address))
                                                {
                                                    <i class="fas fa-map-marker-alt me-1"></i>@Model.Address
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>الرقم الضريبي:</strong></td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(Model.TaxNumber))
                                                {
                                                    @Model.TaxNumber
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ الإنشاء:</strong></td>
                                            <td>@Model.CreatedDate.ToString("dd/MM/yyyy")</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الحالة:</strong></td>
                                            <td>
                                                @if (Model.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                }
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <!-- الملخص المالي -->
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-chart-line me-2"></i>
                                        الملخص المالي
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="card bg-light">
                                                <div class="card-body">
                                                    <h5 class="text-primary">@totalPurchases.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h5>
                                                    <small class="text-muted">إجمالي المشتريات</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="card bg-light">
                                                <div class="card-body">
                                                    <h5 class="text-success">@totalPayments.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h5>
                                                    <small class="text-muted">إجمالي المدفوعات</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="text-center">
                                        <h4 class="@(Model.Balance >= 0 ? "text-success" : "text-danger")">
                                            @Model.Balance.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                                        </h4>
                                        <small class="text-muted">الرصيد الحالي</small>
                                    </div>
                                </div>
                            </div>

                            <!-- الرصيد الافتتاحي -->
                            <div class="card border-info mt-3">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-balance-scale me-2"></i>
                                        الرصيد الافتتاحي
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <strong>المبلغ:</strong>
                                            <br>
                                            <span class="h5">@Model.OpeningBalance.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</span>
                                        </div>
                                        <div class="col-6">
                                            <strong>النوع:</strong>
                                            <br>
                                            <span class="badge @(Model.OpeningBalanceType == MarbleWorkshopSystem.Models.BalanceType.Debit ? "bg-warning" : "bg-info")">
                                                @(Model.OpeningBalanceType == MarbleWorkshopSystem.Models.BalanceType.Debit ? "مدين (له)" : "دائن (عليه)")
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                                        <i class="fas fa-edit me-1"></i>
                                        تعديل البيانات
                                    </a>
                                    <a asp-action="Statement" asp-route-id="@Model.Id" class="btn btn-primary">
                                        <i class="fas fa-file-invoice me-1"></i>
                                        كشف حساب
                                    </a>
                                    <a asp-controller="Purchases" asp-action="Create" asp-route-supplierId="@Model.Id" class="btn btn-success">
                                        <i class="fas fa-plus me-1"></i>
                                        فاتورة شراء جديدة
                                    </a>
                                </div>
                                <div>
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        العودة للقائمة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- فواتير المشتريات الأخيرة -->
                    @if (invoices != null && invoices.Any())
                    {
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-file-invoice me-2"></i>
                                            فواتير المشتريات الأخيرة (آخر 5 فواتير)
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>رقم الفاتورة</th>
                                                        <th>التاريخ</th>
                                                        <th>المبلغ</th>
                                                        <th>الحالة</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach (var invoice in invoices.Take(5))
                                                    {
                                                        <tr>
                                                            <td>@invoice.InvoiceNumber</td>
                                                            <td>@invoice.InvoiceDate.ToString("dd/MM/yyyy")</td>
                                                            <td>@invoice.TotalAmount.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</td>
                                                            <td>
                                                                <span class="badge bg-info">@invoice.Status</span>
                                                            </td>
                                                            <td>
                                                                <a asp-controller="Purchases" asp-action="Details" asp-route-id="@invoice.Id" 
                                                                   class="btn btn-sm btn-outline-info">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- المدفوعات الأخيرة -->
                    @if (payments != null && payments.Any())
                    {
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-money-bill me-2"></i>
                                            المدفوعات الأخيرة (آخر 5 مدفوعات)
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>رقم السند</th>
                                                        <th>التاريخ</th>
                                                        <th>المبلغ</th>
                                                        <th>البيان</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach (var payment in payments.Take(5))
                                                    {
                                                        <tr>
                                                            <td>@payment.PaymentNumber</td>
                                                            <td>@payment.PaymentDate.ToString("dd/MM/yyyy")</td>
                                                            <td>@payment.Amount.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</td>
                                                            <td>@payment.Notes</td>
                                                            <td>
                                                                <a asp-controller="Payments" asp-action="Details" asp-route-id="@payment.Id" 
                                                                   class="btn btn-sm btn-outline-info">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
