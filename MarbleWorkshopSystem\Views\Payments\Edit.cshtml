@model MarbleWorkshopSystem.Models.Payment

@{
    ViewData["Title"] = "تعديل سند الدفع";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>
                        تعديل سند الدفع - @Model.PaymentNumber
                    </h3>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post">
                        <input asp-for="Id" type="hidden" />
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="PaymentNumber" class="form-label">رقم السند</label>
                                    <input asp-for="PaymentNumber" class="form-control" readonly />
                                    <span asp-validation-for="PaymentNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="PaymentDate" class="form-label">تاريخ السند</label>
                                    <input asp-for="PaymentDate" type="date" class="form-control" />
                                    <span asp-validation-for="PaymentDate" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="SupplierId" class="form-label">المورد</label>
                                    <select asp-for="SupplierId" class="form-select" id="supplierSelect">
                                        <option value="">اختر المورد</option>
                                        @foreach (var supplier in ViewBag.Suppliers as IEnumerable<MarbleWorkshopSystem.Models.Supplier>)
                                        {
                                            <option value="@supplier.Id" selected="@(Model.SupplierId == supplier.Id)">@supplier.Name</option>
                                        }
                                    </select>
                                    <span asp-validation-for="SupplierId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رصيد المورد الحالي</label>
                                    <div class="input-group">
                                        <input type="text" id="supplierBalance" class="form-control" readonly />
                                        <span class="input-group-text">ج.م</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Amount" class="form-label">المبلغ</label>
                                    <div class="input-group">
                                        <input asp-for="Amount" type="number" step="0.01" class="form-control" />
                                        <span class="input-group-text">ج.م</span>
                                    </div>
                                    <span asp-validation-for="Amount" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="PaymentMethod" class="form-label">طريقة الدفع</label>
                                    <select asp-for="PaymentMethod" class="form-select">
                                        <option value="">اختر طريقة الدفع</option>
                                        <option value="نقدي">نقدي</option>
                                        <option value="شيك">شيك</option>
                                        <option value="تحويل بنكي">تحويل بنكي</option>
                                        <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                                    </select>
                                    <span asp-validation-for="PaymentMethod" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Notes" class="form-label">الملاحظات</label>
                            <textarea asp-for="Notes" class="form-control" rows="3" placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                            <span asp-validation-for="Notes" class="text-danger"></span>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                العودة للقائمة
                            </a>
                            <div>
                                <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info me-2">
                                    <i class="fas fa-eye me-1"></i>
                                    عرض التفاصيل
                                </a>
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save me-1"></i>
                                    حفظ التعديلات
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // تحديث رصيد المورد عند تحميل الصفحة
            updateSupplierBalance();
            
            // تحديث رصيد المورد عند تغيير المورد
            $('#supplierSelect').change(function() {
                updateSupplierBalance();
            });
            
            function updateSupplierBalance() {
                var supplierId = $('#supplierSelect').val();
                if (supplierId) {
                    $.get('@Url.Action("GetSupplierBalance")', { supplierId: supplierId })
                        .done(function(data) {
                            $('#supplierBalance').val(data.balance.toLocaleString('ar-EG') + ' ج.م');
                        })
                        .fail(function() {
                            $('#supplierBalance').val('خطأ في تحميل الرصيد');
                        });
                } else {
                    $('#supplierBalance').val('');
                }
            }
        });
    </script>
}
