@model MarbleWorkshopSystem.Models.Customer

@{
    ViewData["Title"] = "حذف العميل";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد حذف العميل
                    </h4>
                </div>

                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هل أنت متأكد من رغبتك في حذف هذا العميل؟ هذا الإجراء لا يمكن التراجع عنه.
                    </div>

                    <!-- معلومات العميل -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        معلومات العميل المراد حذفه
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>اسم العميل:</strong></td>
                                            <td>@Model.Name</td>
                                        </tr>
                                        <tr>
                                            <td><strong>رقم الهاتف:</strong></td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(Model.Phone))
                                                {
                                                    <i class="fas fa-phone me-1"></i>@Model.Phone
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>البريد الإلكتروني:</strong></td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(Model.Email))
                                                {
                                                    <i class="fas fa-envelope me-1"></i>@Model.Email
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>العنوان:</strong></td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(Model.Address))
                                                {
                                                    <i class="fas fa-map-marker-alt me-1"></i>@Model.Address
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>الرقم الضريبي:</strong></td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(Model.TaxNumber))
                                                {
                                                    @Model.TaxNumber
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>الرصيد الحالي:</strong></td>
                                            <td>
                                                <span class="badge @(Model.Balance >= 0 ? "bg-success" : "bg-danger") fs-6">
                                                    @Model.Balance.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ الإنشاء:</strong></td>
                                            <td>@Model.CreatedDate.ToString("dd/MM/yyyy")</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الحالة:</strong></td>
                                            <td>
                                                @if (Model.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                }
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        ملاحظات مهمة
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success me-2"></i>
                                            سيتم إلغاء تفعيل العميل بدلاً من حذفه نهائياً
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success me-2"></i>
                                            ستبقى الفواتير والمعاملات السابقة محفوظة
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success me-2"></i>
                                            يمكن إعادة تفعيل العميل لاحقاً
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                            لن يظهر العميل في القوائم النشطة
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <div>
                                    <form asp-action="Delete" method="post" class="d-inline">
                                        <input type="hidden" asp-for="Id" />
                                        <button type="submit" class="btn btn-danger" 
                                                onclick="return confirm('هل أنت متأكد من رغبتك في حذف هذا العميل؟')">
                                            <i class="fas fa-trash me-1"></i>
                                            تأكيد الحذف
                                        </button>
                                    </form>
                                </div>
                                <div>
                                    <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info">
                                        <i class="fas fa-eye me-1"></i>
                                        عرض التفاصيل
                                    </a>
                                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                                        <i class="fas fa-edit me-1"></i>
                                        تعديل بدلاً من الحذف
                                    </a>
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        إلغاء والعودة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
