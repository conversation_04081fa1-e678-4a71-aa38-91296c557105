# نظام إدارة ورشة الرخام - سكريبت تشغيل ويندوز 10
# Marble Workshop Management System - Windows 10 Startup Script

param(
    [string]$Port = "5000",
    [string]$Environment = "Production",
    [switch]$OpenBrowser = $true,
    [switch]$CheckDependencies = $true,
    [switch]$CreateDesktopShortcut = $false
)

# إعداد الترميز للعربية
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$Host.UI.RawUI.WindowTitle = "نظام إدارة ورشة الرخام - Marble Workshop System"

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    نظام إدارة ورشة الرخام" -ForegroundColor Green
Write-Host "    Marble Workshop Management System" -ForegroundColor Green
Write-Host "    Windows 10 Compatible Version" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# التحقق من إصدار ويندوز
$osVersion = [System.Environment]::OSVersion.Version
Write-Host "إصدار ويندوز: $($osVersion.Major).$($osVersion.Minor)" -ForegroundColor Blue

if ($osVersion.Major -lt 10) {
    Write-Warning "تحذير: هذا النظام مُحسَّن لويندوز 10 أو أحدث"
}

# التحقق من المتطلبات
if ($CheckDependencies) {
    Write-Host "جاري التحقق من المتطلبات..." -ForegroundColor Yellow
    
    # التحقق من .NET
    try {
        $dotnetVersion = dotnet --version 2>$null
        if ($dotnetVersion) {
            Write-Host "✓ .NET Runtime متوفر: $dotnetVersion" -ForegroundColor Green
        } else {
            throw "غير متوفر"
        }
    } catch {
        Write-Warning ".NET Runtime غير مثبت"
        Write-Host "يرجى تحميل وتثبيت .NET 8.0 من:" -ForegroundColor Red
        Write-Host "https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Blue
        
        $response = Read-Host "هل تريد المتابعة؟ (y/n)"
        if ($response -ne 'y' -and $response -ne 'Y') {
            exit 1
        }
    }
    
    # التحقق من المنفذ
    $portInUse = Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue
    if ($portInUse) {
        Write-Warning "المنفذ $Port مستخدم بالفعل"
        $Port = "5001"
        Write-Host "سيتم استخدام المنفذ البديل: $Port" -ForegroundColor Yellow
    } else {
        Write-Host "✓ المنفذ $Port متاح" -ForegroundColor Green
    }
}

# إنشاء المجلدات المطلوبة
Write-Host "جاري إنشاء المجلدات المطلوبة..." -ForegroundColor Yellow

$folders = @(
    "$env:LOCALAPPDATA\MarbleWorkshop",
    "$env:LOCALAPPDATA\MarbleWorkshop\Logs",
    "$env:USERPROFILE\Documents\MarbleWorkshop",
    "$env:USERPROFILE\Documents\MarbleWorkshop\Backups"
)

foreach ($folder in $folders) {
    if (!(Test-Path $folder)) {
        New-Item -ItemType Directory -Path $folder -Force | Out-Null
        Write-Host "✓ تم إنشاء: $folder" -ForegroundColor Green
    }
}

# تعيين متغيرات البيئة
$env:ASPNETCORE_ENVIRONMENT = $Environment
$env:ASPNETCORE_URLS = "http://localhost:$Port"
$env:DOTNET_SYSTEM_GLOBALIZATION_INVARIANT = "false"

Write-Host ""
Write-Host "جاري بدء تشغيل النظام..." -ForegroundColor Yellow
Write-Host "البيئة: $Environment" -ForegroundColor Blue
Write-Host "الرابط: http://localhost:$Port" -ForegroundColor Blue
Write-Host ""

# البحث عن ملف التشغيل
$exePaths = @(
    ".\MarbleWorkshopSystem.exe",
    ".\bin\Release\net8.0\win-x64\publish\MarbleWorkshopSystem.exe",
    ".\bin\Release\net8.0\win-x64\MarbleWorkshopSystem.exe"
)

$exeFound = $false
foreach ($path in $exePaths) {
    if (Test-Path $path) {
        Write-Host "تشغيل النسخة المبنية: $path" -ForegroundColor Green
        Start-Process -FilePath $path -ArgumentList "--urls", "http://localhost:$Port", "--environment", $Environment
        $exeFound = $true
        break
    }
}

if (!$exeFound) {
    Write-Host "تشغيل باستخدام dotnet..." -ForegroundColor Yellow
    Start-Process -FilePath "dotnet" -ArgumentList "run", "--configuration", "Release", "--environment", $Environment, "--urls", "http://localhost:$Port" -NoNewWindow
}

# انتظار بدء الخدمة
Write-Host "انتظار بدء الخدمة..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# التحقق من تشغيل الخدمة
try {
    $response = Invoke-WebRequest -Uri "http://localhost:$Port" -TimeoutSec 10 -ErrorAction Stop
    Write-Host "✓ النظام يعمل بنجاح!" -ForegroundColor Green
} catch {
    Write-Warning "تعذر التحقق من حالة النظام"
}

# فتح المتصفح
if ($OpenBrowser) {
    Write-Host "جاري فتح المتصفح..." -ForegroundColor Yellow
    Start-Process "http://localhost:$Port"
}

# إنشاء اختصار على سطح المكتب
if ($CreateDesktopShortcut) {
    $desktopPath = [Environment]::GetFolderPath("Desktop")
    $shortcutPath = "$desktopPath\نظام إدارة ورشة الرخام.lnk"
    
    $shell = New-Object -ComObject WScript.Shell
    $shortcut = $shell.CreateShortcut($shortcutPath)
    $shortcut.TargetPath = "http://localhost:$Port"
    $shortcut.Description = "نظام إدارة ورشة الرخام"
    $shortcut.Save()
    
    Write-Host "✓ تم إنشاء اختصار على سطح المكتب" -ForegroundColor Green
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "النظام يعمل الآن!" -ForegroundColor Green
Write-Host ""
Write-Host "للوصول للنظام:" -ForegroundColor White
Write-Host "http://localhost:$Port" -ForegroundColor Blue
Write-Host ""
Write-Host "لإيقاف النظام: اضغط Ctrl+C" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# إبقاء النافذة مفتوحة
Write-Host "اضغط أي مفتاح للخروج..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
