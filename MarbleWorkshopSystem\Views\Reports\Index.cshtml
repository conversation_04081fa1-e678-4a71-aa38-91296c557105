@{
    ViewData["Title"] = "التقارير";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        التقارير المالية والإدارية
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- التقرير المالي -->
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card h-100 border-primary">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-chart-line fa-3x text-primary"></i>
                                    </div>
                                    <h5 class="card-title">التقرير المالي</h5>
                                    <p class="card-text">تقرير شامل للمبيعات والمشتريات والأرباح</p>
                                    <a asp-action="Financial" class="btn btn-primary">
                                        <i class="fas fa-eye me-1"></i>
                                        عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- تقرير المبيعات -->
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card h-100 border-success">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-shopping-cart fa-3x text-success"></i>
                                    </div>
                                    <h5 class="card-title">تقرير المبيعات</h5>
                                    <p class="card-text">تفاصيل المبيعات والعملاء والمنتجات الأكثر مبيعاً</p>
                                    <div class="d-grid gap-2">
                                        <a asp-action="Sales" class="btn btn-success">
                                            <i class="fas fa-eye me-1"></i>
                                            عرض التقرير
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" onclick="generateSalesReport()">
                                            <i class="fas fa-file-pdf me-1"></i>
                                            تحميل PDF
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقرير المشتريات -->
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card h-100 border-secondary">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-shopping-bag fa-3x text-secondary"></i>
                                    </div>
                                    <h5 class="card-title">تقرير المشتريات</h5>
                                    <p class="card-text">تفاصيل المشتريات والموردين والتكاليف</p>
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-secondary" onclick="alert('قريباً')">
                                            <i class="fas fa-eye me-1"></i>
                                            عرض التقرير
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" onclick="generatePurchasesReport()">
                                            <i class="fas fa-file-pdf me-1"></i>
                                            تحميل PDF
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقرير المخزون -->
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card h-100 border-warning">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-warehouse fa-3x text-warning"></i>
                                    </div>
                                    <h5 class="card-title">تقرير المخزون</h5>
                                    <p class="card-text">حالة المخزون والمنتجات المنخفضة والهالك</p>
                                    <div class="d-grid gap-2">
                                        <a asp-action="Inventory" class="btn btn-warning">
                                            <i class="fas fa-eye me-1"></i>
                                            عرض التقرير
                                        </a>
                                        <a asp-action="GenerateInventoryReport" class="btn btn-outline-danger">
                                            <i class="fas fa-file-pdf me-1"></i>
                                            تحميل PDF
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقرير العملاء -->
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card h-100 border-info">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-users fa-3x text-info"></i>
                                    </div>
                                    <h5 class="card-title">تقرير العملاء</h5>
                                    <p class="card-text">تفاصيل العملاء وأرصدتهم ومبيعاتهم</p>
                                    <a asp-action="Customers" class="btn btn-info">
                                        <i class="fas fa-eye me-1"></i>
                                        عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- تقرير الموردين -->
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="card h-100 border-dark">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-truck fa-3x text-dark"></i>
                                    </div>
                                    <h5 class="card-title">تقرير الموردين</h5>
                                    <p class="card-text">تفاصيل الموردين وأرصدتهم ومشترياتهم</p>
                                    <a asp-action="Suppliers" class="btn btn-dark">
                                        <i class="fas fa-eye me-1"></i>
                                        عرض التقرير
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- خيارات الطباعة والتصدير -->
                    <div class="row">
                        <div class="col-12">
                            <h5 class="mb-3">
                                <i class="fas fa-print me-2"></i>
                                خيارات الطباعة والتصدير
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">مقاسات الطباعة</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-4">
                                                    <div class="text-center">
                                                        <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                                                        <p class="mb-0"><strong>A4</strong></p>
                                                        <small class="text-muted">210×297 مم</small>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="text-center">
                                                        <i class="fas fa-file fa-2x text-success mb-2"></i>
                                                        <p class="mb-0"><strong>A5</strong></p>
                                                        <small class="text-muted">148×210 مم</small>
                                                    </div>
                                                </div>
                                                <div class="col-4">
                                                    <div class="text-center">
                                                        <i class="fas fa-receipt fa-2x text-warning mb-2"></i>
                                                        <p class="mb-0"><strong>POS</strong></p>
                                                        <small class="text-muted">80 مم</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">تصدير التقارير</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-6">
                                                    <button class="btn btn-outline-success w-100" onclick="exportReport('excel')">
                                                        <i class="fas fa-file-excel me-1"></i>
                                                        Excel
                                                    </button>
                                                </div>
                                                <div class="col-6">
                                                    <button class="btn btn-outline-danger w-100" onclick="exportReport('pdf')">
                                                        <i class="fas fa-file-pdf me-1"></i>
                                                        PDF
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="row mt-2">
                                                <div class="col-12">
                                                    <a href="@Url.Action("TestPdf")" class="btn btn-outline-primary w-100" target="_blank">
                                                        <i class="fas fa-vial me-1"></i>
                                                        اختبار PDF
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function exportReport(format) {
            // هنا يمكن إضافة منطق التصدير
            toastr.info('ميزة التصدير إلى ' + format.toUpperCase() + ' ستكون متاحة قريباً');
        }

        // عرض رسائل النجاح والخطأ
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
                toastr.success('@TempData["SuccessMessage"]');
            </text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>
                toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
        @if (TempData["InfoMessage"] != null)
        {
            <text>
                toastr.info('@TempData["InfoMessage"]');
            </text>
        }

        // دالة لتوليد تقرير المبيعات PDF
        function generateSalesReport() {
            // عرض نافذة لاختيار التواريخ
            const fromDate = prompt('من تاريخ (yyyy-mm-dd):', new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0]);
            if (!fromDate) return;

            const toDate = prompt('إلى تاريخ (yyyy-mm-dd):', new Date().toISOString().split('T')[0]);
            if (!toDate) return;

            // التحقق من صحة التواريخ
            if (new Date(fromDate) > new Date(toDate)) {
                alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
                return;
            }

            // إنشاء نموذج وإرساله
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '@Url.Action("GenerateSalesReport")';

            const fromDateInput = document.createElement('input');
            fromDateInput.type = 'hidden';
            fromDateInput.name = 'fromDate';
            fromDateInput.value = fromDate;
            form.appendChild(fromDateInput);

            const toDateInput = document.createElement('input');
            toDateInput.type = 'hidden';
            toDateInput.name = 'toDate';
            toDateInput.value = toDate;
            form.appendChild(toDateInput);

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        // دالة لتوليد تقرير المشتريات PDF
        function generatePurchasesReport() {
            // عرض نافذة لاختيار التواريخ
            const fromDate = prompt('من تاريخ (yyyy-mm-dd):', new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0]);
            if (!fromDate) return;

            const toDate = prompt('إلى تاريخ (yyyy-mm-dd):', new Date().toISOString().split('T')[0]);
            if (!toDate) return;

            // التحقق من صحة التواريخ
            if (new Date(fromDate) > new Date(toDate)) {
                alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
                return;
            }

            // إنشاء نموذج وإرساله
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '@Url.Action("GeneratePurchasesReport")';

            const fromDateInput = document.createElement('input');
            fromDateInput.type = 'hidden';
            fromDateInput.name = 'fromDate';
            fromDateInput.value = fromDate;
            form.appendChild(fromDateInput);

            const toDateInput = document.createElement('input');
            toDateInput.type = 'hidden';
            toDateInput.name = 'toDate';
            toDateInput.value = toDate;
            form.appendChild(toDateInput);

            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }

        // دالة لتصدير التقارير
        function exportReport(format) {
            alert(`ميزة التصدير إلى ${format} ستكون متاحة قريباً`);
        }

        // دالة للطباعة
        function printReport() {
            window.print();
        }
    </script>
}
