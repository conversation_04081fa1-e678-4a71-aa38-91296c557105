@model MarbleWorkshopSystem.Models.CuttingOperation

@{
    ViewData["Title"] = "تعديل عملية التقطيع";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        تعديل عملية التقطيع
                    </h4>
                </div>

                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }

                    <form asp-action="Edit" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <input type="hidden" asp-for="Id" />

                        <div class="row">
                            <!-- معلومات المنتج -->
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-cube me-2"></i>
                                            معلومات المنتج
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="ProductId" class="form-label">
                                                <i class="fas fa-box me-1"></i>
                                                المنتج <span class="text-danger">*</span>
                                            </label>
                                            <select asp-for="ProductId" class="form-select" id="productSelect" onchange="updateProductInfo()">
                                                <option value="">اختر المنتج</option>
                                                @foreach (var product in ViewBag.Products as List<MarbleWorkshopSystem.Models.Product>)
                                                {
                                                    <option value="@product.Id" 
                                                            data-name="@product.Name"
                                                            data-category="@product.Category"
                                                            data-price="@product.UnitPrice"
                                                            data-image="@product.ImagePath"
                                                            selected="@(product.Id == Model.ProductId)">
                                                        @product.Name - @product.Category
                                                    </option>
                                                }
                                            </select>
                                            <span asp-validation-for="ProductId" class="text-danger"></span>
                                        </div>

                                        <!-- معلومات المنتج المختار -->
                                        <div id="productInfo" class="card bg-light">
                                            <div class="card-body p-3">
                                                <div class="row">
                                                    <div class="col-4">
                                                        @if (!string.IsNullOrEmpty(Model.Product?.ImagePath))
                                                        {
                                                            <img id="productImage" src="@Model.Product.ImagePath" alt="@Model.Product.Name" 
                                                                 class="img-thumbnail" style="width: 100%; max-height: 80px; object-fit: cover;">
                                                        }
                                                        else
                                                        {
                                                            <img id="productImage" src="/images/no-image.png" alt="لا توجد صورة" 
                                                                 class="img-thumbnail" style="width: 100%; max-height: 80px; object-fit: cover;">
                                                        }
                                                    </div>
                                                    <div class="col-8">
                                                        <h6 id="productName" class="text-primary mb-1">@Model.Product?.Name</h6>
                                                        <p id="productCategory" class="text-muted mb-1">الفئة: @Model.Product?.Category</p>
                                                        <p id="productPrice" class="text-success mb-0">السعر: @Model.Product?.UnitPrice.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="CuttingDate" class="form-label">
                                                <i class="fas fa-calendar me-1"></i>
                                                تاريخ التقطيع <span class="text-danger">*</span>
                                            </label>
                                            <input asp-for="CuttingDate" type="date" class="form-control" />
                                            <span asp-validation-for="CuttingDate" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- بيانات التقطيع -->
                            <div class="col-md-6">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-calculator me-2"></i>
                                            بيانات التقطيع
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="OriginalQuantity" class="form-label">
                                                <i class="fas fa-cubes me-1"></i>
                                                الكمية الأصلية <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input asp-for="OriginalQuantity" class="form-control" 
                                                       placeholder="أدخل الكمية الأصلية" onchange="calculateWaste()" />
                                                <span class="input-group-text">وحدة</span>
                                            </div>
                                            <span asp-validation-for="OriginalQuantity" class="text-danger"></span>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="UsedQuantity" class="form-label">
                                                <i class="fas fa-check-circle me-1"></i>
                                                الكمية المستخدمة <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input asp-for="UsedQuantity" class="form-control" 
                                                       placeholder="أدخل الكمية المستخدمة" onchange="calculateWaste()" />
                                                <span class="input-group-text">وحدة</span>
                                            </div>
                                            <span asp-validation-for="UsedQuantity" class="text-danger"></span>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="WasteQuantity" class="form-label">
                                                <i class="fas fa-trash me-1"></i>
                                                كمية الهالك
                                            </label>
                                            <div class="input-group">
                                                <input asp-for="WasteQuantity" class="form-control" 
                                                       placeholder="سيتم حسابها تلقائياً" readonly />
                                                <span class="input-group-text">وحدة</span>
                                            </div>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                كمية الهالك = الكمية الأصلية - الكمية المستخدمة
                                            </div>
                                        </div>

                                        <!-- نسبة الهالك -->
                                        <div class="mb-3">
                                            <label class="form-label">
                                                <i class="fas fa-percentage me-1"></i>
                                                نسبة الهالك
                                            </label>
                                            <div class="input-group">
                                                <input type="text" id="wastePercentageDisplay" class="form-control" 
                                                       value="@Model.WastePercentage.ToString("F2")" readonly />
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- الملاحظات -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card border-secondary">
                                    <div class="card-header bg-secondary text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-sticky-note me-2"></i>
                                            ملاحظات إضافية
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="Notes" class="form-label">
                                                <i class="fas fa-comment me-1"></i>
                                                الملاحظات
                                            </label>
                                            <textarea asp-for="Notes" class="form-control" rows="3" 
                                                      placeholder="أدخل أي ملاحظات حول عملية التقطيع (اختياري)"></textarea>
                                            <span asp-validation-for="Notes" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تحذير مهم -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>تحذير مهم:</h6>
                                    <ul class="mb-0">
                                        <li>تعديل عملية التقطيع لن يؤثر على المخزون تلقائياً</li>
                                        <li>إذا كنت تريد تعديل تأثير العملية على المخزون، يرجى مراجعة إدارة المخزون</li>
                                        <li>التعديل سيؤثر فقط على سجل العملية وتقارير الهالك</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-save me-1"></i>
                                            حفظ التعديلات
                                        </button>
                                        <button type="reset" class="btn btn-secondary">
                                            <i class="fas fa-undo me-1"></i>
                                            إعادة تعيين
                                        </button>
                                    </div>
                                    <div>
                                        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info">
                                            <i class="fas fa-eye me-1"></i>
                                            عرض التفاصيل
                                        </a>
                                        <a asp-action="Index" class="btn btn-outline-primary">
                                            <i class="fas fa-list me-1"></i>
                                            العودة للقائمة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        function updateProductInfo() {
            const select = document.getElementById('productSelect');
            const selectedOption = select.options[select.selectedIndex];
            
            if (selectedOption.value) {
                // عرض معلومات المنتج
                document.getElementById('productName').textContent = selectedOption.dataset.name;
                document.getElementById('productCategory').textContent = 'الفئة: ' + selectedOption.dataset.category;
                document.getElementById('productPrice').textContent = 'السعر: ' + parseFloat(selectedOption.dataset.price).toLocaleString('ar-EG', {style: 'currency', currency: 'EGP'});
                
                // عرض الصورة
                const image = document.getElementById('productImage');
                if (selectedOption.dataset.image) {
                    image.src = selectedOption.dataset.image;
                    image.alt = selectedOption.dataset.name;
                } else {
                    image.src = '/images/no-image.png';
                    image.alt = 'لا توجد صورة';
                }
            }
        }
        
        function calculateWaste() {
            const originalQuantity = parseFloat(document.getElementById('OriginalQuantity').value) || 0;
            const usedQuantity = parseFloat(document.getElementById('UsedQuantity').value) || 0;
            
            if (originalQuantity > 0 && usedQuantity >= 0) {
                const wasteQuantity = originalQuantity - usedQuantity;
                const wastePercentage = (wasteQuantity / originalQuantity) * 100;
                
                // تحديث حقل كمية الهالك
                document.getElementById('WasteQuantity').value = wasteQuantity.toFixed(2);
                
                // تحديث نسبة الهالك
                document.getElementById('wastePercentageDisplay').value = wastePercentage.toFixed(2);
                
                // تلوين الحقول حسب نسبة الهالك
                const wastePercentageField = document.getElementById('wastePercentageDisplay');
                if (wastePercentage > 20) {
                    wastePercentageField.className = 'form-control bg-danger text-white';
                } else if (wastePercentage > 10) {
                    wastePercentageField.className = 'form-control bg-warning';
                } else {
                    wastePercentageField.className = 'form-control bg-success text-white';
                }
            }
        }
        
        // التحقق من صحة البيانات
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            
            // حساب الهالك عند تحميل الصفحة
            calculateWaste();
            
            form.addEventListener('submit', function(e) {
                const originalQuantity = parseFloat(document.getElementById('OriginalQuantity').value) || 0;
                const usedQuantity = parseFloat(document.getElementById('UsedQuantity').value) || 0;
                
                if (usedQuantity > originalQuantity) {
                    e.preventDefault();
                    alert('الكمية المستخدمة لا يمكن أن تكون أكبر من الكمية الأصلية');
                    return false;
                }
                
                if (originalQuantity <= 0) {
                    e.preventDefault();
                    alert('يجب إدخال كمية أصلية صحيحة');
                    return false;
                }
            });
        });
    </script>
}
