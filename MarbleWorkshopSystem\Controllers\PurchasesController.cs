using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MarbleWorkshopSystem.Models;
using MarbleWorkshopSystem.Services;
using MarbleWorkshopSystem.ViewModels;

namespace MarbleWorkshopSystem.Controllers
{
    [Authorize]
    public class PurchasesController : Controller
    {
        private readonly IPurchaseService _purchaseService;
        private readonly ISupplierService _supplierService;
        private readonly IProductService _productService;

        public PurchasesController(
            IPurchaseService purchaseService,
            ISupplierService supplierService,
            IProductService productService)
        {
            _purchaseService = purchaseService;
            _supplierService = supplierService;
            _productService = productService;
        }

        // GET: Purchases
        public async Task<IActionResult> Index(string status, DateTime? fromDate, DateTime? toDate, string searchTerm)
        {
            IEnumerable<PurchaseInvoice> invoices;

            if (!string.IsNullOrEmpty(searchTerm))
            {
                invoices = await _purchaseService.SearchInvoicesAsync(searchTerm);
            }
            else if (!string.IsNullOrEmpty(status))
            {
                invoices = await _purchaseService.GetPurchaseInvoicesByStatusAsync(status);
            }
            else if (fromDate.HasValue && toDate.HasValue)
            {
                invoices = await _purchaseService.GetPurchaseInvoicesByDateRangeAsync(fromDate.Value, toDate.Value);
            }
            else
            {
                invoices = await _purchaseService.GetAllPurchaseInvoicesAsync();
            }

            ViewBag.Status = status;
            ViewBag.FromDate = fromDate?.ToString("yyyy-MM-dd");
            ViewBag.ToDate = toDate?.ToString("yyyy-MM-dd");
            ViewBag.SearchTerm = searchTerm;

            return View(invoices);
        }

        // GET: Purchases/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var invoice = await _purchaseService.GetPurchaseInvoiceByIdAsync(id);
            if (invoice == null)
            {
                return NotFound();
            }

            return View(invoice);
        }

        // GET: Purchases/Create
        public async Task<IActionResult> Create()
        {
            var viewModel = new PurchaseInvoiceViewModel
            {
                Invoice = new PurchaseInvoice
                {
                    InvoiceDate = DateTime.Now,
                    Items = new List<PurchaseInvoiceItem>()
                },
                Suppliers = await _supplierService.GetAllSuppliersAsync(),
                Products = await _productService.GetAllProductsAsync()
            };

            return View(viewModel);
        }

        // POST: Purchases/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(PurchaseInvoiceViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var invoice = await _purchaseService.CreatePurchaseInvoiceAsync(viewModel.Invoice);
                    TempData["SuccessMessage"] = "تم إنشاء فاتورة المشتريات بنجاح";
                    return RedirectToAction(nameof(Details), new { id = invoice.Id });
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "حدث خطأ أثناء إنشاء الفاتورة: " + ex.Message);
                }
            }

            await LoadViewModelDataAsync(viewModel);
            return View(viewModel);
        }

        // GET: Purchases/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var invoice = await _purchaseService.GetPurchaseInvoiceByIdAsync(id);
            if (invoice == null)
            {
                return NotFound();
            }

            if (invoice.Status == "مؤكدة")
            {
                TempData["ErrorMessage"] = "لا يمكن تعديل فاتورة مؤكدة";
                return RedirectToAction(nameof(Details), new { id });
            }

            var viewModel = new PurchaseInvoiceViewModel
            {
                Invoice = invoice,
                Suppliers = await _supplierService.GetAllSuppliersAsync(),
                Products = await _productService.GetAllProductsAsync()
            };

            return View(viewModel);
        }

        // POST: Purchases/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, PurchaseInvoiceViewModel viewModel)
        {
            if (id != viewModel.Invoice.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    await _purchaseService.UpdatePurchaseInvoiceAsync(viewModel.Invoice);
                    TempData["SuccessMessage"] = "تم تحديث الفاتورة بنجاح";
                    return RedirectToAction(nameof(Details), new { id });
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "حدث خطأ أثناء تحديث الفاتورة: " + ex.Message);
                }
            }

            await LoadViewModelDataAsync(viewModel);
            return View(viewModel);
        }

        // POST: Purchases/Confirm/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Confirm(int id)
        {
            try
            {
                await _purchaseService.ConfirmPurchaseInvoiceAsync(id);
                TempData["SuccessMessage"] = "تم تأكيد الفاتورة بنجاح";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء تأكيد الفاتورة: " + ex.Message;
            }

            return RedirectToAction(nameof(Details), new { id });
        }

        // POST: Purchases/Cancel/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Cancel(int id)
        {
            try
            {
                await _purchaseService.CancelPurchaseInvoiceAsync(id);
                TempData["SuccessMessage"] = "تم إلغاء الفاتورة بنجاح";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء إلغاء الفاتورة: " + ex.Message;
            }

            return RedirectToAction(nameof(Details), new { id });
        }

        // GET: Purchases/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var invoice = await _purchaseService.GetPurchaseInvoiceByIdAsync(id);
            if (invoice == null)
            {
                return NotFound();
            }

            return View(invoice);
        }

        // POST: Purchases/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                await _purchaseService.DeletePurchaseInvoiceAsync(id);
                TempData["SuccessMessage"] = "تم حذف الفاتورة بنجاح";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف الفاتورة: " + ex.Message;
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: Purchases/Print/5
        public async Task<IActionResult> Print(int id, string format = "A4")
        {
            var invoice = await _purchaseService.GetPurchaseInvoiceByIdAsync(id);
            if (invoice == null)
            {
                return NotFound();
            }

            ViewBag.PrintFormat = format;
            return View(invoice);
        }

        private async Task LoadViewModelDataAsync(PurchaseInvoiceViewModel viewModel)
        {
            viewModel.Suppliers = await _supplierService.GetAllSuppliersAsync();
            viewModel.Products = await _productService.GetAllProductsAsync();
        }
    }
}
