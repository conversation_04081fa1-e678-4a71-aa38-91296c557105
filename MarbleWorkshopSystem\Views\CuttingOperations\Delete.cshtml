@model MarbleWorkshopSystem.Models.CuttingOperation

@{
    ViewData["Title"] = "حذف عملية التقطيع";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-trash me-2"></i>
                        حذف عملية التقطيع
                    </h4>
                </div>

                <div class="card-body">
                    <!-- تحذير -->
                    <div class="alert alert-danger">
                        <h5>
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            تحذير: هل أنت متأكد من حذف هذه العملية؟
                        </h5>
                        <p class="mb-0">
                            سيتم حذف عملية التقطيع نهائياً ولن يمكن استرجاعها. 
                            لن يؤثر الحذف على المخزون الحالي.
                        </p>
                    </div>

                    <!-- تفاصيل العملية المراد حذفها -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        معلومات العملية
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-2">
                                        <div class="col-4"><strong>رقم العملية:</strong></div>
                                        <div class="col-8">
                                            <span class="badge bg-primary fs-6">#@Model.Id</span>
                                        </div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-4"><strong>تاريخ التقطيع:</strong></div>
                                        <div class="col-8">
                                            <i class="fas fa-calendar me-1"></i>
                                            @Model.CuttingDate.ToString("dd/MM/yyyy")
                                        </div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-4"><strong>وقت التسجيل:</strong></div>
                                        <div class="col-8">
                                            <i class="fas fa-clock me-1"></i>
                                            @Model.CuttingDate.ToString("HH:mm")
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-cube me-2"></i>
                                        معلومات المنتج
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-4">
                                            @if (!string.IsNullOrEmpty(Model.Product.ImagePath))
                                            {
                                                <img src="@Model.Product.ImagePath" alt="@Model.Product.Name" 
                                                     class="img-thumbnail" style="width: 100%; max-height: 80px; object-fit: cover;">
                                            }
                                            else
                                            {
                                                <div class="bg-secondary text-white d-flex align-items-center justify-content-center" 
                                                     style="height: 80px; border-radius: 8px;">
                                                    <i class="fas fa-image fa-2x"></i>
                                                </div>
                                            }
                                        </div>
                                        <div class="col-8">
                                            <h6 class="text-primary">@Model.Product.Name</h6>
                                            <p class="mb-1"><strong>الفئة:</strong> @Model.Product.Category</p>
                                            <p class="mb-0"><strong>السعر:</strong> @Model.Product.UnitPrice.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- بيانات التقطيع -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.OriginalQuantity.ToString("F2")</h4>
                                    <p class="mb-0">الكمية الأصلية</p>
                                    <small>وحدة</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.UsedQuantity.ToString("F2")</h4>
                                    <p class="mb-0">الكمية المستخدمة</p>
                                    <small>وحدة</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.WasteQuantity.ToString("F2")</h4>
                                    <p class="mb-0">كمية الهالك</p>
                                    <small>وحدة</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card @(Model.WastePercentage > 20 ? "bg-danger" : Model.WastePercentage > 10 ? "bg-warning" : "bg-info") text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.WastePercentage.ToString("F1")%</h4>
                                    <p class="mb-0">نسبة الهالك</p>
                                    <small>من الإجمالي</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الملاحظات -->
                    @if (!string.IsNullOrEmpty(Model.Notes))
                    {
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card border-secondary">
                                    <div class="card-header bg-secondary text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-sticky-note me-2"></i>
                                            الملاحظات
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="mb-0">@Model.Notes</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- التأثير المالي -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-calculator me-2"></i>
                                        التأثير المالي للعملية
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="text-center">
                                                <h5 class="text-primary">@((Model.OriginalQuantity * Model.Product.UnitPrice).ToString("C", new System.Globalization.CultureInfo("ar-EG")))</h5>
                                                <p class="mb-0">قيمة المواد الأصلية</p>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="text-center">
                                                <h5 class="text-success">@((Model.UsedQuantity * Model.Product.UnitPrice).ToString("C", new System.Globalization.CultureInfo("ar-EG")))</h5>
                                                <p class="mb-0">قيمة المواد المستخدمة</p>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="text-center">
                                                <h5 class="text-danger">@((Model.WasteQuantity * Model.Product.UnitPrice).ToString("C", new System.Globalization.CultureInfo("ar-EG")))</h5>
                                                <p class="mb-0">قيمة الهالك</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تحذير إضافي -->
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-info-circle me-2"></i>ملاحظات مهمة:</h6>
                        <ul class="mb-0">
                            <li>حذف العملية لن يؤثر على المخزون الحالي</li>
                            <li>ستختفي العملية من تقارير الهالك</li>
                            <li>لا يمكن التراجع عن عملية الحذف</li>
                            <li>إذا كنت تريد تعديل البيانات فقط، استخدم خيار "تعديل" بدلاً من الحذف</li>
                        </ul>
                    </div>

                    <!-- نموذج الحذف -->
                    <form asp-action="Delete" method="post">
                        <input type="hidden" asp-for="Id" />
                        
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-danger" onclick="return confirmDelete()">
                                            <i class="fas fa-trash me-1"></i>
                                            تأكيد الحذف
                                        </button>
                                    </div>
                                    <div>
                                        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info">
                                            <i class="fas fa-eye me-1"></i>
                                            عرض التفاصيل
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                                            <i class="fas fa-edit me-1"></i>
                                            تعديل بدلاً من الحذف
                                        </a>
                                        <a asp-action="Index" class="btn btn-primary">
                                            <i class="fas fa-list me-1"></i>
                                            العودة للقائمة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete() {
            return confirm('هل أنت متأكد من حذف عملية التقطيع رقم @Model.Id؟\n\nلن يمكن التراجع عن هذا الإجراء.');
        }
        
        // تحذير إضافي عند محاولة مغادرة الصفحة بعد النقر على حذف
        let deleteClicked = false;
        
        document.querySelector('form').addEventListener('submit', function() {
            deleteClicked = true;
        });
        
        window.addEventListener('beforeunload', function(e) {
            if (deleteClicked) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    </script>
}
