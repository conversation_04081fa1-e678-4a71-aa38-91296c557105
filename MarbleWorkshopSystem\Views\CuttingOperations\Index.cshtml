@model IEnumerable<MarbleWorkshopSystem.Models.CuttingOperation>

@{
    ViewData["Title"] = "عمليات التقطيع";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-cut me-2"></i>
                        عمليات التقطيع
                    </h3>
                    <div>
                        <a asp-action="Report" class="btn btn-info me-2">
                            <i class="fas fa-chart-line me-1"></i>
                            تقرير الهالك
                        </a>
                        <a asp-action="Create" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            تسجيل عملية تقطيع
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filter Section -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <form asp-action="Index" method="get" class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" name="fromDate" class="form-control" value="@ViewBag.FromDate">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" name="toDate" class="form-control" value="@ViewBag.ToDate">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-outline-primary">
                                            <i class="fas fa-filter me-1"></i>
                                            تصفية
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count()</h4>
                                    <p class="mb-0">إجمالي العمليات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Sum(o => o.UsedQuantity).ToString("F2")</h4>
                                    <p class="mb-0">إجمالي المستخدم</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Sum(o => o.WasteQuantity).ToString("F2")</h4>
                                    <p class="mb-0">إجمالي الهالك</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>@(Model.Any() ? Model.Average(o => o.WastePercentage).ToString("F1") + "%" : "0%")</h4>
                                    <p class="mb-0">متوسط نسبة الهالك</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Operations Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المنتج</th>
                                    <th>الكمية الأصلية</th>
                                    <th>الكمية المستخدمة</th>
                                    <th>كمية الهالك</th>
                                    <th>نسبة الهالك</th>
                                    <th>الملاحظات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            <i class="fas fa-calendar me-1"></i>
                                            @item.CuttingDate.ToString("dd/MM/yyyy")
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if (!string.IsNullOrEmpty(item.Product.ImagePath))
                                                {
                                                    <img src="@item.Product.ImagePath" alt="@item.Product.Name" 
                                                         class="img-thumbnail me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                                }
                                                <div>
                                                    <strong>@item.Product.Name</strong>
                                                    <br><small class="text-muted">@item.Product.Category</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="text-info fw-bold">@item.OriginalQuantity.ToString("F2")</span>
                                        </td>
                                        <td>
                                            <span class="text-success fw-bold">@item.UsedQuantity.ToString("F2")</span>
                                        </td>
                                        <td>
                                            <span class="text-danger fw-bold">@item.WasteQuantity.ToString("F2")</span>
                                        </td>
                                        <td>
                                            <span class="badge @(item.WastePercentage > 10 ? "bg-danger" : item.WastePercentage > 5 ? "bg-warning" : "bg-success")">
                                                @item.WastePercentage.ToString("F1")%
                                            </span>
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.Notes))
                                            {
                                                <span title="@item.Notes">
                                                    @(item.Notes.Length > 30 ? item.Notes.Substring(0, 30) + "..." : item.Notes)
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">لا توجد ملاحظات</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a asp-action="Delete" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if (!Model.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-cut fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد عمليات تقطيع</h5>
                            <p class="text-muted">ابدأ بتسجيل عمليات التقطيع لتتبع الهالك</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                تسجيل أول عملية
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // عرض رسائل النجاح والخطأ
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
                toastr.success('@TempData["SuccessMessage"]');
            </text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>
                toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
    </script>
}
