using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MarbleWorkshopSystem.Services;
using MarbleWorkshopSystem.ViewModels;
using MarbleWorkshopSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace MarbleWorkshopSystem.Controllers
{
    [Authorize]
    public class ReportsController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly ISalesService _salesService;
        private readonly IPurchaseService _purchaseService;
        private readonly IInventoryService _inventoryService;
        private readonly ICustomerService _customerService;
        private readonly ISupplierService _supplierService;
        private readonly SimpleReportService _reportService;

        public ReportsController(
            ApplicationDbContext context,
            ISalesService salesService,
            IPurchaseService purchaseService,
            IInventoryService inventoryService,
            ICustomerService customerService,
            ISupplierService supplierService,
            SimpleReportService reportService)
        {
            _context = context;
            _salesService = salesService;
            _purchaseService = purchaseService;
            _inventoryService = inventoryService;
            _customerService = customerService;
            _supplierService = supplierService;
            _reportService = reportService;
        }

        // GET: Reports
        public IActionResult Index()
        {
            return View();
        }

        // GET: Reports/Financial
        public async Task<IActionResult> Financial(DateTime? fromDate, DateTime? toDate)
        {
            var from = fromDate ?? DateTime.Now.AddMonths(-1);
            var to = toDate ?? DateTime.Now;

            var viewModel = new FinancialReportViewModel
            {
                FromDate = from,
                ToDate = to,
                TotalSales = await _salesService.GetTotalSalesAsync(from, to),
                TotalPurchases = await _purchaseService.GetTotalPurchasesAsync(from, to),
                SalesCount = await _salesService.GetInvoiceCountAsync(from, to),
                PurchasesCount = await _purchaseService.GetInvoiceCountAsync(from, to),
                AverageSale = await _salesService.GetAverageSaleAsync(from, to),
                AveragePurchase = await _purchaseService.GetAveragePurchaseAsync(from, to)
            };

            // حساب الأرباح
            viewModel.NetProfit = viewModel.TotalSales - viewModel.TotalPurchases;

            // إحصائيات المصروفات
            var expenses = await _context.Expenses
                .Where(e => e.ExpenseDate >= from && e.ExpenseDate <= to)
                .ToListAsync();

            viewModel.TotalExpenses = expenses.Sum(e => e.Amount);
            viewModel.ExpensesByCategory = expenses
                .GroupBy(e => e.Category)
                .Select(g => new CategoryExpenseViewModel
                {
                    Category = g.Key,
                    Amount = g.Sum(e => e.Amount),
                    Count = g.Count()
                })
                .OrderByDescending(x => x.Amount)
                .ToList();

            return View(viewModel);
        }

        // GET: Reports/Sales
        public async Task<IActionResult> Sales(DateTime? fromDate, DateTime? toDate)
        {
            var from = fromDate ?? DateTime.Now.AddMonths(-1);
            var to = toDate ?? DateTime.Now;

            var salesInvoices = await _context.SalesInvoices
                .Include(s => s.Customer)
                .Include(s => s.Items)
                .ThenInclude(i => i.Product)
                .Where(s => s.InvoiceDate >= from && s.InvoiceDate <= to && s.Status == "مؤكدة")
                .ToListAsync();

            var viewModel = new SalesReportViewModel
            {
                FromDate = from,
                ToDate = to,
                TotalSales = salesInvoices.Sum(s => s.TotalAmount),
                InvoiceCount = salesInvoices.Count,
                AverageInvoice = salesInvoices.Any() ? salesInvoices.Average(s => s.TotalAmount) : 0,
                TopCustomers = salesInvoices
                    .GroupBy(s => s.Customer)
                    .Select(g => new CustomerSalesViewModel
                    {
                        CustomerName = g.Key.Name,
                        TotalSales = g.Sum(s => s.TotalAmount),
                        InvoiceCount = g.Count()
                    })
                    .OrderByDescending(x => x.TotalSales)
                    .Take(10)
                    .ToList(),
                TopProducts = salesInvoices
                    .SelectMany(s => s.Items)
                    .GroupBy(i => i.Product)
                    .Select(g => new ProductSalesViewModel
                    {
                        ProductName = g.Key.Name,
                        TotalQuantity = g.Sum(i => i.Quantity),
                        TotalSales = g.Sum(i => i.TotalPrice)
                    })
                    .OrderByDescending(x => x.TotalSales)
                    .Take(10)
                    .ToList()
            };

            return View(viewModel);
        }

        // GET: Reports/Inventory
        public async Task<IActionResult> Inventory()
        {
            var inventory = await _inventoryService.GetAllInventoryAsync();
            var lowStockItems = await _inventoryService.GetLowStockItemsAsync();

            var viewModel = new InventoryReportViewModel
            {
                TotalProducts = inventory.Count(),
                LowStockCount = lowStockItems.Count(),
                TotalStockValue = inventory.Sum(i => i.Quantity * i.Product.UnitPrice),
                TotalWaste = inventory.Sum(i => i.WasteQuantity),
                InventoryItems = inventory.OrderBy(i => i.Product.Name).ToList(),
                LowStockItems = lowStockItems.ToList()
            };

            return View(viewModel);
        }

        // GET: Reports/Customers
        public async Task<IActionResult> Customers()
        {
            var customers = await _customerService.GetAllCustomersAsync();
            var salesData = await _context.SalesInvoices
                .Include(s => s.Customer)
                .Where(s => s.Status == "مؤكدة")
                .GroupBy(s => s.Customer)
                .Select(g => new
                {
                    Customer = g.Key,
                    TotalSales = g.Sum(s => s.TotalAmount),
                    InvoiceCount = g.Count(),
                    LastSale = g.Max(s => s.InvoiceDate)
                })
                .ToListAsync();

            var viewModel = new CustomersReportViewModel
            {
                TotalCustomers = customers.Count(),
                ActiveCustomers = salesData.Count,
                TotalBalance = customers.Sum(c => c.Balance),
                CustomerDetails = customers.Select(c =>
                {
                    var sales = salesData.FirstOrDefault(s => s.Customer.Id == c.Id);
                    return new CustomerDetailViewModel
                    {
                        CustomerName = c.Name,
                        Phone = c.Phone,
                        Balance = c.Balance,
                        TotalSales = sales?.TotalSales ?? 0,
                        InvoiceCount = sales?.InvoiceCount ?? 0,
                        LastSaleDate = sales?.LastSale
                    };
                }).OrderByDescending(x => x.TotalSales).ToList()
            };

            return View(viewModel);
        }

        // GET: Reports/Suppliers
        public async Task<IActionResult> Suppliers()
        {
            var suppliers = await _supplierService.GetAllSuppliersAsync();
            return View(suppliers);
        }

        // GET: Reports/Print
        public async Task<IActionResult> Print(string reportType, DateTime? fromDate, DateTime? toDate, string format = "A4")
        {
            ViewBag.PrintFormat = format;
            ViewBag.ReportType = reportType;

            switch (reportType?.ToLower())
            {
                case "financial":
                    return await Financial(fromDate, toDate);
                case "sales":
                    return await Sales(fromDate, toDate);
                case "inventory":
                    return await Inventory();
                case "customers":
                    return await Customers();
                default:
                    return RedirectToAction(nameof(Index));
            }
        }

        // GET: Reports/Export
        public async Task<IActionResult> Export(string reportType, DateTime? fromDate, DateTime? toDate, string format = "excel")
        {
            // هنا يمكن إضافة منطق التصدير إلى Excel أو PDF
            TempData["InfoMessage"] = "ميزة التصدير ستكون متاحة قريباً";
            return RedirectToAction(nameof(Index));
        }

        // طباعة فاتورة مبيعات بـ PDF
        [HttpGet]
        public async Task<IActionResult> PrintSalesInvoice(int id)
        {
            try
            {
                var pdfBytes = await _reportService.GenerateSalesInvoiceReportAsync(id);
                return File(pdfBytes, "application/pdf", $"فاتورة_مبيعات_{id}.pdf");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"خطأ في إنشاء التقرير: {ex.Message}";
                return RedirectToAction("Index", "Sales");
            }
        }

        // طباعة فاتورة مشتريات بـ PDF
        [HttpGet]
        public async Task<IActionResult> PrintPurchaseInvoice(int id)
        {
            try
            {
                var pdfBytes = await _reportService.GeneratePurchaseInvoiceReportAsync(id);
                return File(pdfBytes, "application/pdf", $"فاتورة_مشتريات_{id}.pdf");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"خطأ في إنشاء التقرير: {ex.Message}";
                return RedirectToAction("Index", "Purchases");
            }
        }

        // تقرير المبيعات الشامل
        [HttpPost]
        public async Task<IActionResult> GenerateSalesReport(DateTime fromDate, DateTime toDate, int? customerId = null)
        {
            try
            {
                var pdfBytes = await _reportService.GenerateSalesReportAsync(fromDate, toDate, customerId);
                var fileName = $"تقرير_المبيعات_{fromDate:yyyyMMdd}_{toDate:yyyyMMdd}.pdf";
                return File(pdfBytes, "application/pdf", fileName);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"خطأ في إنشاء التقرير: {ex.Message}";
                return RedirectToAction("Index");
            }
        }

        // تقرير المشتريات الشامل
        [HttpPost]
        public async Task<IActionResult> GeneratePurchasesReport(DateTime fromDate, DateTime toDate, int? supplierId = null)
        {
            try
            {
                var pdfBytes = await _reportService.GeneratePurchasesReportAsync(fromDate, toDate, supplierId);
                var fileName = $"تقرير_المشتريات_{fromDate:yyyyMMdd}_{toDate:yyyyMMdd}.pdf";
                return File(pdfBytes, "application/pdf", fileName);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"خطأ في إنشاء التقرير: {ex.Message}";
                return RedirectToAction("Index");
            }
        }

        // تقرير المخزون التفصيلي
        [HttpGet]
        public async Task<IActionResult> GenerateInventoryReport()
        {
            try
            {
                var pdfBytes = await _reportService.GenerateInventoryReportAsync();
                var fileName = $"تقرير_المخزون_{DateTime.Now:yyyyMMdd}.pdf";
                return File(pdfBytes, "application/pdf", fileName);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"خطأ في إنشاء التقرير: {ex.Message}";
                return RedirectToAction("Index");
            }
        }

        // اختبار PDF بسيط
        [HttpGet]
        public async Task<IActionResult> TestPdf()
        {
            try
            {
                var pdfBytes = await _reportService.GenerateTestReportAsync();
                return File(pdfBytes, "application/pdf", "test_report.pdf");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"خطأ في إنشاء التقرير: {ex.Message}";
                return RedirectToAction("Index");
            }
        }
    }
}
