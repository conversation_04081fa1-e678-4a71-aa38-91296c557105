using Microsoft.EntityFrameworkCore;
using MarbleWorkshopSystem.Data;
using MarbleWorkshopSystem.Models;

namespace MarbleWorkshopSystem.Services
{
    public class CustomerService : ICustomerService
    {
        private readonly IRepository<Customer> _customerRepository;
        private readonly ApplicationDbContext _context;

        public CustomerService(IRepository<Customer> customerRepository, ApplicationDbContext context)
        {
            _customerRepository = customerRepository;
            _context = context;
        }

        public async Task<IEnumerable<Customer>> GetAllCustomersAsync()
        {
            return await _context.Customers
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<Customer?> GetCustomerByIdAsync(int id)
        {
            return await _context.Customers
                .Include(c => c.SalesInvoices)
                .Include(c => c.Receipts)
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        public async Task<IEnumerable<Customer>> SearchCustomersAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllCustomersAsync();

            return await _context.Customers
                .Where(c => c.IsActive && 
                           (c.Name.Contains(searchTerm) || 
                            c.Phone!.Contains(searchTerm) ||
                            c.Email!.Contains(searchTerm)))
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<Customer> CreateCustomerAsync(Customer customer)
        {
            customer.CreatedDate = DateTime.Now;

            // تطبيق الرصيد الافتتاحي
            if (customer.OpeningBalance > 0)
            {
                // إذا كان مدين (له) فالرصيد سالب (نحن مدينون له)
                // إذا كان دائن (عليه) فالرصيد موجب (هو مدين لنا)
                customer.Balance = customer.OpeningBalanceType == BalanceType.Debit
                    ? -customer.OpeningBalance
                    : customer.OpeningBalance;
            }
            else
            {
                customer.Balance = 0;
            }

            var createdCustomer = await _customerRepository.AddAsync(customer);
            await _customerRepository.SaveChangesAsync();

            return createdCustomer;
        }

        public async Task<Customer> UpdateCustomerAsync(Customer customer)
        {
            var updatedCustomer = await _customerRepository.UpdateAsync(customer);
            await _customerRepository.SaveChangesAsync();
            
            return updatedCustomer;
        }

        public async Task DeleteCustomerAsync(int id)
        {
            var customer = await GetCustomerByIdAsync(id);
            if (customer != null)
            {
                customer.IsActive = false;
                await UpdateCustomerAsync(customer);
            }
        }

        public async Task<bool> CustomerExistsAsync(int id)
        {
            return await _customerRepository.ExistsAsync(id);
        }

        public async Task<decimal> GetCustomerBalanceAsync(int customerId)
        {
            var customer = await GetCustomerByIdAsync(customerId);
            return customer?.Balance ?? 0;
        }

        public async Task UpdateCustomerBalanceAsync(int customerId, decimal amount)
        {
            var customer = await GetCustomerByIdAsync(customerId);
            if (customer != null)
            {
                customer.Balance += amount;
                await UpdateCustomerAsync(customer);
            }
        }

        public async Task<IEnumerable<SalesInvoice>> GetCustomerInvoicesAsync(int customerId)
        {
            return await _context.SalesInvoices
                .Include(s => s.Items)
                .ThenInclude(i => i.Product)
                .Where(s => s.CustomerId == customerId)
                .OrderByDescending(s => s.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Receipt>> GetCustomerReceiptsAsync(int customerId)
        {
            return await _context.Receipts
                .Where(r => r.CustomerId == customerId)
                .OrderByDescending(r => r.ReceiptDate)
                .ToListAsync();
        }

        public async Task<decimal> GetCustomerTotalSalesAsync(int customerId)
        {
            return await _context.SalesInvoices
                .Where(s => s.CustomerId == customerId && s.Status == "مؤكدة")
                .SumAsync(s => s.TotalAmount);
        }

        public async Task<decimal> GetCustomerTotalPaymentsAsync(int customerId)
        {
            return await _context.Receipts
                .Where(r => r.CustomerId == customerId)
                .SumAsync(r => r.Amount);
        }
    }
}
