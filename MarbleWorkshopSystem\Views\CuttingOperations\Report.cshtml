@model IEnumerable<MarbleWorkshopSystem.Models.CuttingOperation>

@{
    ViewData["Title"] = "تقرير عمليات التقطيع";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-cut me-2"></i>
                            تقرير عمليات التقطيع والهالك
                        </h4>
                        <div>
                            <button type="button" class="btn btn-light btn-sm" onclick="printReport()">
                                <i class="fas fa-print me-1"></i>
                                طباعة
                            </button>
                            <a asp-action="Index" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left me-1"></i>
                                العودة
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count()</h4>
                                    <small>إجمالي العمليات</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Sum(c => c.UsedQuantity).ToString("N2")</h4>
                                    <small>الكمية المستخدمة (م²)</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Sum(c => c.WasteQuantity).ToString("N2")</h4>
                                    <small>كمية الهالك (م²)</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>@(Model.Any() ? Model.Average(c => c.WastePercentage).ToString("N1") : "0")%</h4>
                                    <small>متوسط نسبة الهالك</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول التقرير -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المنتج</th>
                                    <th>الكمية الأصلية</th>
                                    <th>الكمية المستخدمة</th>
                                    <th>كمية الهالك</th>
                                    <th>نسبة الهالك</th>
                                    <th>الملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.Any())
                                {
                                    @foreach (var operation in Model.OrderByDescending(c => c.CuttingDate))
                                    {
                                        <tr>
                                            <td>@operation.CuttingDate.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                <strong>@operation.Product.Name</strong>
                                                <br>
                                                <small class="text-muted">@operation.Product.Category</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@operation.OriginalQuantity.ToString("N2") م²</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">@operation.UsedQuantity.ToString("N2") م²</span>
                                            </td>
                                            <td>
                                                <span class="badge bg-danger">@operation.WasteQuantity.ToString("N2") م²</span>
                                            </td>
                                            <td>
                                                @{
                                                    var wasteClass = operation.WastePercentage <= 5 ? "success" : 
                                                                   operation.WastePercentage <= 10 ? "warning" : "danger";
                                                }
                                                <span class="badge bg-@wasteClass">@operation.WastePercentage.ToString("N1")%</span>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(operation.Notes))
                                                {
                                                    <small>@operation.Notes</small>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="7" class="text-center text-muted py-4">
                                            <i class="fas fa-info-circle me-2"></i>
                                            لا توجد عمليات تقطيع مسجلة
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if (Model.Any())
                    {
                        <!-- تحليل الهالك حسب المنتج -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">
                                            <i class="fas fa-chart-pie me-2"></i>
                                            تحليل الهالك حسب المنتج
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>المنتج</th>
                                                        <th>عدد العمليات</th>
                                                        <th>إجمالي الكمية الأصلية</th>
                                                        <th>إجمالي الهالك</th>
                                                        <th>متوسط نسبة الهالك</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach (var productGroup in Model.GroupBy(c => c.Product))
                                                    {
                                                        <tr>
                                                            <td>
                                                                <strong>@productGroup.Key.Name</strong>
                                                                <br>
                                                                <small class="text-muted">@productGroup.Key.Category</small>
                                                            </td>
                                                            <td>@productGroup.Count()</td>
                                                            <td>@productGroup.Sum(c => c.OriginalQuantity).ToString("N2") م²</td>
                                                            <td>@productGroup.Sum(c => c.WasteQuantity).ToString("N2") م²</td>
                                                            <td>
                                                                @{
                                                                    var avgWaste = productGroup.Average(c => c.WastePercentage);
                                                                    var wasteClass = avgWaste <= 5 ? "success" : 
                                                                                   avgWaste <= 10 ? "warning" : "danger";
                                                                }
                                                                <span class="badge bg-@wasteClass">@avgWaste.ToString("N1")%</span>
                                                            </td>
                                                        </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ملخص شهري -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">
                                            <i class="fas fa-calendar me-2"></i>
                                            الملخص الشهري
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>الشهر</th>
                                                        <th>عدد العمليات</th>
                                                        <th>إجمالي الكمية المستخدمة</th>
                                                        <th>إجمالي الهالك</th>
                                                        <th>متوسط نسبة الهالك</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach (var monthGroup in Model.GroupBy(c => new { c.CuttingDate.Year, c.CuttingDate.Month }).OrderByDescending(g => g.Key.Year).ThenByDescending(g => g.Key.Month))
                                                    {
                                                        <tr>
                                                            <td>@monthGroup.Key.Month/@monthGroup.Key.Year</td>
                                                            <td>@monthGroup.Count()</td>
                                                            <td>@monthGroup.Sum(c => c.UsedQuantity).ToString("N2") م²</td>
                                                            <td>@monthGroup.Sum(c => c.WasteQuantity).ToString("N2") م²</td>
                                                            <td>
                                                                @{
                                                                    var avgWaste = monthGroup.Average(c => c.WastePercentage);
                                                                    var wasteClass = avgWaste <= 5 ? "success" : 
                                                                                   avgWaste <= 10 ? "warning" : "danger";
                                                                }
                                                                <span class="badge bg-@wasteClass">@avgWaste.ToString("N1")%</span>
                                                            </td>
                                                        </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function printReport() {
            window.print();
        }
    </script>
}
