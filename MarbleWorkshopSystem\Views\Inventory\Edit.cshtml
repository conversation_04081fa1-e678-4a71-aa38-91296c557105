@model MarbleWorkshopSystem.Models.Inventory

@{
    ViewData["Title"] = "تعديل المخزون";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        تعديل مخزون المنتج
                    </h4>
                </div>

                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }

                    <form asp-action="Edit" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <input type="hidden" asp-for="Id" />
                        <input type="hidden" asp-for="ProductId" />

                        <!-- معلومات المنتج -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-cube me-2"></i>
                                            معلومات المنتج
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                @if (!string.IsNullOrEmpty(Model.Product.ImagePath))
                                                {
                                                    <img src="@Model.Product.ImagePath" alt="@Model.Product.Name" 
                                                         class="img-thumbnail" style="max-height: 150px; width: 100%; object-fit: cover;">
                                                }
                                                else
                                                {
                                                    <div class="bg-secondary text-white d-flex align-items-center justify-content-center" 
                                                         style="height: 150px; border-radius: 8px;">
                                                        <i class="fas fa-image fa-3x"></i>
                                                    </div>
                                                }
                                            </div>
                                            <div class="col-md-9">
                                                <h5 class="text-primary">@Model.Product.Name</h5>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <p class="mb-1"><strong>الفئة:</strong> @Model.Product.Category</p>
                                                        <p class="mb-1"><strong>المقاس:</strong> @Model.Product.Length × @Model.Product.Width</p>
                                                        <p class="mb-1"><strong>المساحة:</strong> @Model.Product.Area متر مربع</p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <p class="mb-1"><strong>سعر الوحدة:</strong> @Model.Product.UnitPrice.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</p>
                                                        <p class="mb-1"><strong>آخر تحديث:</strong> @Model.LastUpdated.ToString("dd/MM/yyyy HH:mm")</p>
                                                    </div>
                                                </div>
                                                @if (!string.IsNullOrEmpty(Model.Product.Description))
                                                {
                                                    <p class="text-muted mt-2">@Model.Product.Description</p>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- بيانات المخزون -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-boxes me-2"></i>
                                            كميات المخزون
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="Quantity" class="form-label">
                                                <i class="fas fa-cubes me-1"></i>
                                                الكمية الحالية <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input asp-for="Quantity" class="form-control" placeholder="أدخل الكمية الحالية" />
                                                <span class="input-group-text">وحدة</span>
                                            </div>
                                            <span asp-validation-for="Quantity" class="text-danger"></span>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                الكمية الإجمالية المتوفرة في المخزون
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="WasteQuantity" class="form-label">
                                                <i class="fas fa-trash me-1"></i>
                                                كمية الهالك
                                            </label>
                                            <div class="input-group">
                                                <input asp-for="WasteQuantity" class="form-control" placeholder="أدخل كمية الهالك" />
                                                <span class="input-group-text">وحدة</span>
                                            </div>
                                            <span asp-validation-for="WasteQuantity" class="text-danger"></span>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                الكمية التالفة أو غير الصالحة للاستخدام
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="MinimumStock" class="form-label">
                                                <i class="fas fa-exclamation-triangle me-1"></i>
                                                الحد الأدنى للمخزون <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input asp-for="MinimumStock" class="form-control" placeholder="أدخل الحد الأدنى" />
                                                <span class="input-group-text">وحدة</span>
                                            </div>
                                            <span asp-validation-for="MinimumStock" class="text-danger"></span>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                عند الوصول لهذا الحد سيظهر تنبيه نقص المخزون
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-calculator me-2"></i>
                                            الحسابات التلقائية
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <div class="card bg-success text-white">
                                                    <div class="card-body text-center">
                                                        <h5 id="availableQuantity">@Model.AvailableQuantity.ToString("F2")</h5>
                                                        <small>الكمية المتاحة</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="card @(Model.IsLowStock ? "bg-danger" : "bg-primary") text-white">
                                                    <div class="card-body text-center">
                                                        <h5 id="stockStatus">@(Model.IsLowStock ? "نقص مخزون" : "مخزون جيد")</h5>
                                                        <small>حالة المخزون</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-lightbulb me-2"></i>معادلات الحساب:</h6>
                                            <ul class="mb-0">
                                                <li><strong>الكمية المتاحة</strong> = الكمية الحالية - كمية الهالك</li>
                                                <li><strong>حالة المخزون</strong> = مقارنة الكمية الحالية بالحد الأدنى</li>
                                            </ul>
                                        </div>

                                        <div class="alert alert-warning">
                                            <h6><i class="fas fa-exclamation-triangle me-2"></i>تنبيهات:</h6>
                                            <ul class="mb-0">
                                                <li>تأكد من دقة الكميات المدخلة</li>
                                                <li>كمية الهالك لا يمكن أن تزيد عن الكمية الحالية</li>
                                                <li>الحد الأدنى يساعد في تجنب نفاد المخزون</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-save me-1"></i>
                                            حفظ التعديلات
                                        </button>
                                        <button type="reset" class="btn btn-secondary">
                                            <i class="fas fa-undo me-1"></i>
                                            إعادة تعيين
                                        </button>
                                    </div>
                                    <div>
                                        <a asp-action="Index" class="btn btn-outline-primary">
                                            <i class="fas fa-list me-1"></i>
                                            العودة للقائمة
                                        </a>
                                        <a asp-controller="Products" asp-action="Details" asp-route-id="@Model.ProductId" class="btn btn-info">
                                            <i class="fas fa-eye me-1"></i>
                                            تفاصيل المنتج
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // تحديث الحسابات عند تغيير القيم
            function updateCalculations() {
                const quantity = parseFloat($('#Quantity').val()) || 0;
                const wasteQuantity = parseFloat($('#WasteQuantity').val()) || 0;
                const minimumStock = parseFloat($('#MinimumStock').val()) || 0;
                
                const availableQuantity = quantity - wasteQuantity;
                const isLowStock = quantity <= minimumStock;
                
                $('#availableQuantity').text(availableQuantity.toFixed(2));
                
                const statusCard = $('#stockStatus').closest('.card');
                if (isLowStock) {
                    statusCard.removeClass('bg-primary').addClass('bg-danger');
                    $('#stockStatus').text('نقص مخزون');
                } else {
                    statusCard.removeClass('bg-danger').addClass('bg-primary');
                    $('#stockStatus').text('مخزون جيد');
                }
            }
            
            // ربط الأحداث
            $('#Quantity, #WasteQuantity, #MinimumStock').on('input', updateCalculations);
            
            // التحقق من صحة البيانات
            $('#WasteQuantity').on('input', function() {
                const quantity = parseFloat($('#Quantity').val()) || 0;
                const wasteQuantity = parseFloat($(this).val()) || 0;
                
                if (wasteQuantity > quantity) {
                    $(this).addClass('is-invalid');
                    $(this).next('.text-danger').text('كمية الهالك لا يمكن أن تزيد عن الكمية الحالية');
                } else {
                    $(this).removeClass('is-invalid');
                    $(this).next('.text-danger').text('');
                }
            });
        });
    </script>
}
