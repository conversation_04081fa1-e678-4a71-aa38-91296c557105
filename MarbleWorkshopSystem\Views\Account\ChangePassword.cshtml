@model MarbleWorkshopSystem.ViewModels.ChangePasswordViewModel

@{
    ViewData["Title"] = "تغيير كلمة المرور";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-key me-2"></i>
                        تغيير كلمة المرور
                    </h4>
                </div>

                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }

                    <form asp-action="ChangePassword" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-lock me-2"></i>
                                            كلمة المرور الحالية
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="CurrentPassword" class="form-label">
                                                <i class="fas fa-unlock me-1"></i>
                                                كلمة المرور الحالية <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input asp-for="CurrentPassword" type="password" class="form-control" 
                                                       placeholder="أدخل كلمة المرور الحالية" id="currentPassword" />
                                                <button class="btn btn-outline-secondary" type="button" 
                                                        onclick="togglePassword('currentPassword', this)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <span asp-validation-for="CurrentPassword" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-key me-2"></i>
                                            كلمة المرور الجديدة
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="NewPassword" class="form-label">
                                                <i class="fas fa-lock me-1"></i>
                                                كلمة المرور الجديدة <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input asp-for="NewPassword" type="password" class="form-control" 
                                                       placeholder="أدخل كلمة المرور الجديدة" id="newPassword" />
                                                <button class="btn btn-outline-secondary" type="button" 
                                                        onclick="togglePassword('newPassword', this)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <span asp-validation-for="NewPassword" class="text-danger"></span>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                كلمة المرور يجب أن تكون على الأقل 4 أحرف
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="ConfirmPassword" class="form-label">
                                                <i class="fas fa-check-double me-1"></i>
                                                تأكيد كلمة المرور الجديدة <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input asp-for="ConfirmPassword" type="password" class="form-control" 
                                                       placeholder="أعد إدخال كلمة المرور الجديدة" id="confirmPassword" />
                                                <button class="btn btn-outline-secondary" type="button" 
                                                        onclick="togglePassword('confirmPassword', this)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- مؤشر قوة كلمة المرور -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-shield-alt me-2"></i>
                                            مؤشر قوة كلمة المرور
                                        </h6>
                                        <div class="progress mb-2" style="height: 10px;">
                                            <div class="progress-bar" role="progressbar" id="passwordStrength" 
                                                 style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <small class="text-muted" id="passwordStrengthText">أدخل كلمة المرور لتقييم قوتها</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- نصائح الأمان -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-lightbulb me-2"></i>
                                            نصائح لكلمة مرور قوية
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <ul class="list-unstyled">
                                                    <li class="mb-1">
                                                        <i class="fas fa-check text-success me-2"></i>
                                                        استخدم على الأقل 8 أحرف
                                                    </li>
                                                    <li class="mb-1">
                                                        <i class="fas fa-check text-success me-2"></i>
                                                        امزج بين الأحرف الكبيرة والصغيرة
                                                    </li>
                                                    <li class="mb-1">
                                                        <i class="fas fa-check text-success me-2"></i>
                                                        أضف أرقام ورموز خاصة
                                                    </li>
                                                </ul>
                                            </div>
                                            <div class="col-md-6">
                                                <ul class="list-unstyled">
                                                    <li class="mb-1">
                                                        <i class="fas fa-times text-danger me-2"></i>
                                                        تجنب المعلومات الشخصية
                                                    </li>
                                                    <li class="mb-1">
                                                        <i class="fas fa-times text-danger me-2"></i>
                                                        لا تستخدم كلمات شائعة
                                                    </li>
                                                    <li class="mb-1">
                                                        <i class="fas fa-times text-danger me-2"></i>
                                                        تجنب التسلسل (123456)
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-save me-1"></i>
                                            تغيير كلمة المرور
                                        </button>
                                    </div>
                                    <div>
                                        <a asp-action="Profile" class="btn btn-info">
                                            <i class="fas fa-user me-1"></i>
                                            الملف الشخصي
                                        </a>
                                        <a asp-controller="Home" asp-action="Index" class="btn btn-secondary">
                                            <i class="fas fa-home me-1"></i>
                                            العودة للرئيسية
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // إظهار/إخفاء كلمة المرور
        function togglePassword(inputId, button) {
            const input = document.getElementById(inputId);
            const icon = button.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // تقييم قوة كلمة المرور
        function checkPasswordStrength(password) {
            let strength = 0;
            let feedback = [];

            if (password.length >= 8) strength += 25;
            else feedback.push('على الأقل 8 أحرف');

            if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength += 25;
            else feedback.push('أحرف كبيرة وصغيرة');

            if (/\d/.test(password)) strength += 25;
            else feedback.push('أرقام');

            if (/[^A-Za-z0-9]/.test(password)) strength += 25;
            else feedback.push('رموز خاصة');

            return { strength, feedback };
        }

        $(document).ready(function() {
            // مراقبة تغيير كلمة المرور الجديدة
            $('#newPassword').on('input', function() {
                const password = $(this).val();
                const result = checkPasswordStrength(password);
                const progressBar = $('#passwordStrength');
                const strengthText = $('#passwordStrengthText');

                progressBar.css('width', result.strength + '%');
                progressBar.attr('aria-valuenow', result.strength);

                if (result.strength < 25) {
                    progressBar.removeClass().addClass('progress-bar bg-danger');
                    strengthText.text('ضعيف جداً - ' + result.feedback.join(', '));
                } else if (result.strength < 50) {
                    progressBar.removeClass().addClass('progress-bar bg-warning');
                    strengthText.text('ضعيف - يحتاج: ' + result.feedback.join(', '));
                } else if (result.strength < 75) {
                    progressBar.removeClass().addClass('progress-bar bg-info');
                    strengthText.text('متوسط - يحتاج: ' + result.feedback.join(', '));
                } else if (result.strength < 100) {
                    progressBar.removeClass().addClass('progress-bar bg-primary');
                    strengthText.text('جيد - يحتاج: ' + result.feedback.join(', '));
                } else {
                    progressBar.removeClass().addClass('progress-bar bg-success');
                    strengthText.text('ممتاز - كلمة مرور قوية');
                }
            });

            // التحقق من تطابق كلمة المرور
            $('#confirmPassword').on('input', function() {
                const newPassword = $('#newPassword').val();
                const confirmPassword = $(this).val();
                
                if (confirmPassword && newPassword !== confirmPassword) {
                    $(this).addClass('is-invalid');
                    $(this).next().next('.text-danger').text('كلمة المرور غير متطابقة');
                } else {
                    $(this).removeClass('is-invalid');
                    $(this).next().next('.text-danger').text('');
                }
            });
        });
    </script>
}
