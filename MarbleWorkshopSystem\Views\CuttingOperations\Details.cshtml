@model MarbleWorkshopSystem.Models.CuttingOperation

@{
    ViewData["Title"] = "تفاصيل عملية التقطيع";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-eye me-2"></i>
                        تفاصيل عملية التقطيع
                    </h4>
                </div>

                <div class="card-body">
                    <!-- معلومات العملية -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        معلومات العملية
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-2">
                                        <div class="col-4"><strong>رقم العملية:</strong></div>
                                        <div class="col-8">
                                            <span class="badge bg-primary fs-6">#@Model.Id</span>
                                        </div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-4"><strong>تاريخ التقطيع:</strong></div>
                                        <div class="col-8">
                                            <i class="fas fa-calendar me-1"></i>
                                            @Model.CuttingDate.ToString("dd/MM/yyyy")
                                        </div>
                                    </div>
                                    <div class="row mb-2">
                                        <div class="col-4"><strong>وقت التسجيل:</strong></div>
                                        <div class="col-8">
                                            <i class="fas fa-clock me-1"></i>
                                            @Model.CuttingDate.ToString("HH:mm")
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-cube me-2"></i>
                                        معلومات المنتج
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-4">
                                            @if (!string.IsNullOrEmpty(Model.Product.ImagePath))
                                            {
                                                <img src="@Model.Product.ImagePath" alt="@Model.Product.Name" 
                                                     class="img-thumbnail" style="width: 100%; max-height: 100px; object-fit: cover;">
                                            }
                                            else
                                            {
                                                <div class="bg-secondary text-white d-flex align-items-center justify-content-center" 
                                                     style="height: 100px; border-radius: 8px;">
                                                    <i class="fas fa-image fa-2x"></i>
                                                </div>
                                            }
                                        </div>
                                        <div class="col-8">
                                            <h6 class="text-primary">@Model.Product.Name</h6>
                                            <p class="mb-1"><strong>الفئة:</strong> @Model.Product.Category</p>
                                            <p class="mb-1"><strong>السعر:</strong> @Model.Product.UnitPrice.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</p>
                                            <a asp-controller="Products" asp-action="Details" asp-route-id="@Model.Product.Id" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-external-link-alt me-1"></i>
                                                تفاصيل المنتج
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- بيانات التقطيع -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3>@Model.OriginalQuantity.ToString("F2")</h3>
                                    <p class="mb-0">الكمية الأصلية</p>
                                    <small>وحدة</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3>@Model.UsedQuantity.ToString("F2")</h3>
                                    <p class="mb-0">الكمية المستخدمة</p>
                                    <small>وحدة</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card @(Model.WastePercentage > 20 ? "bg-danger" : Model.WastePercentage > 10 ? "bg-warning" : "bg-info") text-white">
                                <div class="card-body text-center">
                                    <h3>@Model.WasteQuantity.ToString("F2")</h3>
                                    <p class="mb-0">كمية الهالك</p>
                                    <small>وحدة</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- نسبة الهالك -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-percentage me-2"></i>
                                        تحليل نسبة الهالك
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="text-center">
                                                <h2 class="@(Model.WastePercentage > 20 ? "text-danger" : Model.WastePercentage > 10 ? "text-warning" : "text-success")">
                                                    @Model.WastePercentage.ToString("F2")%
                                                </h2>
                                                <p class="mb-0">نسبة الهالك</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="progress mb-2" style="height: 25px;">
                                                <div class="progress-bar @(Model.WastePercentage > 20 ? "bg-danger" : Model.WastePercentage > 10 ? "bg-warning" : "bg-success")" 
                                                     role="progressbar" style="width: @Math.Min(Model.WastePercentage, 100)%" 
                                                     aria-valuenow="@Model.WastePercentage" aria-valuemin="0" aria-valuemax="100">
                                                    @Model.WastePercentage.ToString("F1")%
                                                </div>
                                            </div>
                                            <div class="text-center">
                                                @if (Model.WastePercentage > 20)
                                                {
                                                    <span class="badge bg-danger">نسبة هالك عالية</span>
                                                }
                                                else if (Model.WastePercentage > 10)
                                                {
                                                    <span class="badge bg-warning">نسبة هالك متوسطة</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-success">نسبة هالك منخفضة</span>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الملاحظات -->
                    @if (!string.IsNullOrEmpty(Model.Notes))
                    {
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card border-secondary">
                                    <div class="card-header bg-secondary text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-sticky-note me-2"></i>
                                            الملاحظات
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="mb-0">@Model.Notes</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- تحليل مالي -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-calculator me-2"></i>
                                        التحليل المالي
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="text-center">
                                                <h5 class="text-primary">@((Model.OriginalQuantity * Model.Product.UnitPrice).ToString("C", new System.Globalization.CultureInfo("ar-EG")))</h5>
                                                <p class="mb-0">قيمة المواد الأصلية</p>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="text-center">
                                                <h5 class="text-success">@((Model.UsedQuantity * Model.Product.UnitPrice).ToString("C", new System.Globalization.CultureInfo("ar-EG")))</h5>
                                                <p class="mb-0">قيمة المواد المستخدمة</p>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="text-center">
                                                <h5 class="text-danger">@((Model.WasteQuantity * Model.Product.UnitPrice).ToString("C", new System.Globalization.CultureInfo("ar-EG")))</h5>
                                                <p class="mb-0">قيمة الهالك</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معادلات الحساب -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-formula me-2"></i>
                                        معادلات الحساب
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <ul class="mb-0">
                                                <li><strong>كمية الهالك</strong> = الكمية الأصلية - الكمية المستخدمة</li>
                                                <li><strong>نسبة الهالك</strong> = (كمية الهالك ÷ الكمية الأصلية) × 100</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <ul class="mb-0">
                                                <li><strong>قيمة الهالك</strong> = كمية الهالك × سعر الوحدة</li>
                                                <li><strong>كفاءة الاستخدام</strong> = @((Model.UsedQuantity / Model.OriginalQuantity * 100).ToString("F1"))%</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <div>
                                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning">
                                        <i class="fas fa-edit me-1"></i>
                                        تعديل العملية
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                                        <i class="fas fa-trash me-1"></i>
                                        حذف العملية
                                    </a>
                                </div>
                                <div>
                                    <a asp-action="Index" class="btn btn-primary">
                                        <i class="fas fa-list me-1"></i>
                                        العودة للقائمة
                                    </a>
                                    <a asp-action="Report" class="btn btn-info">
                                        <i class="fas fa-chart-line me-1"></i>
                                        تقرير الهالك
                                    </a>
                                    <button type="button" class="btn btn-secondary" onclick="window.print()">
                                        <i class="fas fa-print me-1"></i>
                                        طباعة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // تحسين عرض الصفحة للطباعة
        window.addEventListener('beforeprint', function() {
            document.querySelector('.btn-group').style.display = 'none';
        });
        
        window.addEventListener('afterprint', function() {
            document.querySelector('.btn-group').style.display = 'block';
        });
    </script>
}
