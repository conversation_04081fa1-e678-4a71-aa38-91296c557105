using Microsoft.AspNetCore.Mvc;
using MarbleWorkshopSystem.Services;
using MarbleWorkshopSystem.Models;

namespace MarbleWorkshopSystem.Controllers
{
    public class InventoryController : Controller
    {
        private readonly IInventoryService _inventoryService;
        private readonly IProductService _productService;

        public InventoryController(IInventoryService inventoryService, IProductService productService)
        {
            _inventoryService = inventoryService;
            _productService = productService;
        }

        // GET: Inventory
        public async Task<IActionResult> Index()
        {
            var inventory = await _inventoryService.GetAllInventoryAsync();
            return View(inventory);
        }

        // GET: Inventory/LowStock
        public async Task<IActionResult> LowStock()
        {
            var lowStockItems = await _inventoryService.GetLowStockItemsAsync();
            return View(lowStockItems);
        }

        // GET: Inventory/Alerts
        public async Task<IActionResult> Alerts()
        {
            var alerts = await _inventoryService.GetInventoryAlertsAsync();
            return View(alerts);
        }

        // GET: Inventory/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var inventory = await _inventoryService.GetInventoryByIdAsync(id);
            if (inventory == null)
            {
                return NotFound();
            }

            return View(inventory);
        }

        // POST: Inventory/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Inventory inventory)
        {
            if (id != inventory.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    await _inventoryService.UpdateInventoryAsync(inventory);
                    TempData["SuccessMessage"] = "تم تحديث المخزون بنجاح";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "حدث خطأ أثناء تحديث المخزون: " + ex.Message);
                }
            }

            return View(inventory);
        }

        // POST: Inventory/AddStock
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddStock(int productId, decimal quantity)
        {
            try
            {
                await _inventoryService.AddStockAsync(productId, quantity);
                TempData["SuccessMessage"] = "تم إضافة الكمية للمخزون بنجاح";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء إضافة الكمية: " + ex.Message;
            }

            return RedirectToAction(nameof(Index));
        }

        // POST: Inventory/ReduceStock
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ReduceStock(int productId, decimal quantity)
        {
            try
            {
                await _inventoryService.ReduceStockAsync(productId, quantity);
                TempData["SuccessMessage"] = "تم خصم الكمية من المخزون بنجاح";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء خصم الكمية: " + ex.Message;
            }

            return RedirectToAction(nameof(Index));
        }

        // POST: Inventory/AddWaste
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AddWaste(int productId, decimal wasteQuantity)
        {
            try
            {
                await _inventoryService.AddWasteAsync(productId, wasteQuantity);
                TempData["SuccessMessage"] = "تم إضافة كمية الهالك بنجاح";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء إضافة الهالك: " + ex.Message;
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: Inventory/StockMovement/5
        public async Task<IActionResult> StockMovement(int id)
        {
            var inventory = await _inventoryService.GetInventoryByIdAsync(id);
            if (inventory == null)
            {
                return NotFound();
            }

            return View(inventory);
        }
    }
}
