using Microsoft.AspNetCore.Mvc;
using MarbleWorkshopSystem.Models;
using MarbleWorkshopSystem.Services;
using MarbleWorkshopSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace MarbleWorkshopSystem.Controllers
{
    public class PaymentsController : Controller
    {
        private readonly IRepository<Payment> _paymentRepository;
        private readonly ISupplierService _supplierService;
        private readonly ApplicationDbContext _context;

        public PaymentsController(
            IRepository<Payment> paymentRepository,
            ISupplierService supplierService,
            ApplicationDbContext context)
        {
            _paymentRepository = paymentRepository;
            _supplierService = supplierService;
            _context = context;
        }

        // GET: Payments
        public async Task<IActionResult> Index(int? supplierId, DateTime? fromDate, DateTime? toDate)
        {
            var payments = await _context.Payments
                .Include(p => p.Supplier)
                .ToListAsync();

            // تطبيق الفلاتر
            if (supplierId.HasValue)
            {
                payments = payments.Where(p => p.SupplierId == supplierId.Value).ToList();
            }

            if (fromDate.HasValue)
            {
                payments = payments.Where(p => p.PaymentDate >= fromDate.Value).ToList();
            }

            if (toDate.HasValue)
            {
                payments = payments.Where(p => p.PaymentDate <= toDate.Value).ToList();
            }

            ViewBag.Suppliers = await _supplierService.GetAllSuppliersAsync();
            ViewBag.SelectedSupplierId = supplierId;
            ViewBag.FromDate = fromDate?.ToString("yyyy-MM-dd");
            ViewBag.ToDate = toDate?.ToString("yyyy-MM-dd");

            return View(payments.OrderByDescending(p => p.PaymentDate));
        }

        // GET: Payments/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var payment = await _context.Payments
                .Include(p => p.Supplier)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (payment == null)
            {
                return NotFound();
            }

            return View(payment);
        }

        // GET: Payments/Create
        public async Task<IActionResult> Create()
        {
            ViewBag.Suppliers = await _supplierService.GetAllSuppliersAsync();
            
            var payment = new Payment
            {
                PaymentDate = DateTime.Now,
                PaymentNumber = await GeneratePaymentNumberAsync()
            };

            return View(payment);
        }

        // POST: Payments/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Payment payment)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    payment.PaymentNumber = await GeneratePaymentNumberAsync();
                    
                    await _paymentRepository.AddAsync(payment);
                    await _paymentRepository.SaveChangesAsync();

                    // تحديث رصيد المورد
                    await _supplierService.UpdateSupplierBalanceAsync(payment.SupplierId, -payment.Amount);

                    TempData["SuccessMessage"] = "تم إنشاء سند الدفع بنجاح";
                    return RedirectToAction(nameof(Details), new { id = payment.Id });
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "حدث خطأ أثناء إنشاء سند الدفع: " + ex.Message);
                }
            }

            ViewBag.Suppliers = await _supplierService.GetAllSuppliersAsync();
            return View(payment);
        }

        // GET: Payments/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var payment = await _context.Payments
                .Include(p => p.Supplier)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (payment == null)
            {
                return NotFound();
            }

            ViewBag.Suppliers = await _supplierService.GetAllSuppliersAsync();
            return View(payment);
        }

        // POST: Payments/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Payment payment)
        {
            if (id != payment.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // الحصول على السند الأصلي لمعرفة المبلغ القديم
                    var originalPayment = await _paymentRepository.GetByIdAsync(id);
                    if (originalPayment != null)
                    {
                        // إرجاع المبلغ القديم
                        await _supplierService.UpdateSupplierBalanceAsync(originalPayment.SupplierId, originalPayment.Amount);
                        
                        // خصم المبلغ الجديد
                        await _supplierService.UpdateSupplierBalanceAsync(payment.SupplierId, -payment.Amount);
                    }

                    await _paymentRepository.UpdateAsync(payment);
                    await _paymentRepository.SaveChangesAsync();
                    
                    TempData["SuccessMessage"] = "تم تحديث سند الدفع بنجاح";
                    return RedirectToAction(nameof(Details), new { id });
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "حدث خطأ أثناء تحديث سند الدفع: " + ex.Message);
                }
            }

            ViewBag.Suppliers = await _supplierService.GetAllSuppliersAsync();
            return View(payment);
        }

        // GET: Payments/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var payment = await _context.Payments
                .Include(p => p.Supplier)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (payment == null)
            {
                return NotFound();
            }

            return View(payment);
        }

        // POST: Payments/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                var payment = await _paymentRepository.GetByIdAsync(id);
                if (payment != null)
                {
                    // إرجاع المبلغ لرصيد المورد
                    await _supplierService.UpdateSupplierBalanceAsync(payment.SupplierId, payment.Amount);
                    
                    await _paymentRepository.DeleteAsync(id);
                    await _paymentRepository.SaveChangesAsync();
                }
                
                TempData["SuccessMessage"] = "تم حذف سند الدفع بنجاح";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف سند الدفع: " + ex.Message;
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: Payments/Print/5
        public async Task<IActionResult> Print(int id, string format = "A4")
        {
            var payment = await _context.Payments
                .Include(p => p.Supplier)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (payment == null)
            {
                return NotFound();
            }

            ViewBag.PrintFormat = format;
            return View(payment);
        }

        // AJAX: Get Supplier Balance
        [HttpGet]
        public async Task<IActionResult> GetSupplierBalance(int supplierId)
        {
            var balance = await _supplierService.GetSupplierBalanceAsync(supplierId);
            return Json(new { balance = balance });
        }

        private async Task<string> GeneratePaymentNumberAsync()
        {
            var lastPayment = await _context.Payments
                .OrderByDescending(p => p.Id)
                .FirstOrDefaultAsync();

            var nextNumber = (lastPayment?.Id ?? 0) + 1;
            return $"PAY-{DateTime.Now:yyyyMM}-{nextNumber:D4}";
        }
    }
}
