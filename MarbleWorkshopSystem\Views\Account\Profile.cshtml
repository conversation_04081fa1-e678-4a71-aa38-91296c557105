@model MarbleWorkshopSystem.ViewModels.ProfileViewModel

@{
    ViewData["Title"] = "الملف الشخصي";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-edit me-2"></i>
                        الملف الشخصي
                    </h4>
                </div>

                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }

                    <form asp-action="Profile" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <div class="row">
                            <!-- المعلومات الأساسية -->
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-user me-2"></i>
                                            المعلومات الأساسية
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="FullName" class="form-label">
                                                <i class="fas fa-user me-1"></i>
                                                الاسم الكامل <span class="text-danger">*</span>
                                            </label>
                                            <input asp-for="FullName" class="form-control" placeholder="أدخل الاسم الكامل" />
                                            <span asp-validation-for="FullName" class="text-danger"></span>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="Email" class="form-label">
                                                <i class="fas fa-envelope me-1"></i>
                                                البريد الإلكتروني <span class="text-danger">*</span>
                                            </label>
                                            <input asp-for="Email" type="email" class="form-control" placeholder="أدخل البريد الإلكتروني" />
                                            <span asp-validation-for="Email" class="text-danger"></span>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                سيتم استخدام البريد الإلكتروني كاسم مستخدم لتسجيل الدخول
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="PhoneNumber" class="form-label">
                                                <i class="fas fa-phone me-1"></i>
                                                رقم الهاتف
                                            </label>
                                            <input asp-for="PhoneNumber" class="form-control" placeholder="أدخل رقم الهاتف" />
                                            <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات إضافية -->
                            <div class="col-md-6">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-info-circle me-2"></i>
                                            معلومات إضافية
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="Address" class="form-label">
                                                <i class="fas fa-map-marker-alt me-1"></i>
                                                العنوان
                                            </label>
                                            <textarea asp-for="Address" class="form-control" rows="3" placeholder="أدخل العنوان"></textarea>
                                            <span asp-validation-for="Address" class="text-danger"></span>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="NationalId" class="form-label">
                                                <i class="fas fa-id-card me-1"></i>
                                                الرقم القومي
                                            </label>
                                            <input asp-for="NationalId" class="form-control" placeholder="أدخل الرقم القومي" />
                                            <span asp-validation-for="NationalId" class="text-danger"></span>
                                        </div>

                                        <!-- معلومات الحساب -->
                                        <div class="card bg-light mt-3">
                                            <div class="card-body">
                                                <h6 class="card-title">
                                                    <i class="fas fa-shield-alt me-2"></i>
                                                    معلومات الحساب
                                                </h6>
                                                <div class="row">
                                                    <div class="col-12">
                                                        <small class="text-muted">
                                                            <i class="fas fa-calendar me-1"></i>
                                                            تاريخ إنشاء الحساب: @User.FindFirst("CreatedDate")?.Value ?? "غير محدد"
                                                        </small>
                                                    </div>
                                                </div>
                                                <div class="row mt-2">
                                                    <div class="col-12">
                                                        <small class="text-muted">
                                                            <i class="fas fa-user-tag me-1"></i>
                                                            الدور: @(User.IsInRole("مدير") ? "مدير النظام" : "مستخدم")
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ملاحظات مهمة -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                                            ملاحظات مهمة
                                        </h6>
                                        <ul class="mb-0">
                                            <li>الحقول المميزة بـ (*) مطلوبة</li>
                                            <li>تغيير البريد الإلكتروني سيؤثر على اسم المستخدم</li>
                                            <li>تأكد من صحة البيانات قبل الحفظ</li>
                                            <li>لتغيير كلمة المرور، استخدم الرابط المخصص لذلك</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>
                                            حفظ التعديلات
                                        </button>
                                        <a asp-action="ChangePassword" class="btn btn-warning">
                                            <i class="fas fa-key me-1"></i>
                                            تغيير كلمة المرور
                                        </a>
                                    </div>
                                    <div>
                                        <a asp-controller="Home" asp-action="Index" class="btn btn-secondary">
                                            <i class="fas fa-home me-1"></i>
                                            العودة للرئيسية
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
