@model IEnumerable<MarbleWorkshopSystem.Models.Expense>

@{
    ViewData["Title"] = "إدارة المصروفات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        إدارة المصروفات
                    </h3>
                    <div>
                        <a asp-action="Report" class="btn btn-info me-2">
                            <i class="fas fa-chart-bar me-1"></i>
                            تقرير المصروفات
                        </a>
                        <a asp-action="Create" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            إضافة مصروف جديد
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Filter Section -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <form asp-action="Index" method="get" class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label">الفئة</label>
                                    <select name="category" class="form-select">
                                        <option value="">جميع الفئات</option>
                                        @if (ViewBag.Categories != null)
                                        {
                                            @foreach (var category in ViewBag.Categories as List<string>)
                                            {
                                                <option value="@category" selected="@(ViewBag.SelectedCategory == category)">
                                                    @category
                                                </option>
                                            }
                                        }
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" name="fromDate" class="form-control" value="@ViewBag.FromDate">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" name="toDate" class="form-control" value="@ViewBag.ToDate">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-outline-primary">
                                            <i class="fas fa-filter me-1"></i>
                                            تصفية
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count()</h4>
                                    <p class="mb-0">إجمالي المصروفات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Sum(e => e.Amount).ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h4>
                                    <p class="mb-0">إجمالي المبلغ</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>@(Model.Any() ? Model.Average(e => e.Amount).ToString("C", new System.Globalization.CultureInfo("ar-EG")) : "0")</h4>
                                    <p class="mb-0">متوسط المصروف</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Select(e => e.Category).Distinct().Count()</h4>
                                    <p class="mb-0">عدد الفئات</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Expenses Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الوصف</th>
                                    <th>الفئة</th>
                                    <th>المبلغ</th>
                                    <th>الملاحظات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            <i class="fas fa-calendar me-1"></i>
                                            @item.ExpenseDate.ToString("dd/MM/yyyy")
                                        </td>
                                        <td>
                                            <strong>@item.Description</strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">@item.Category</span>
                                        </td>
                                        <td>
                                            <span class="text-danger fw-bold">
                                                @item.Amount.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                                            </span>
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.Notes))
                                            {
                                                <span title="@item.Notes">
                                                    @(item.Notes.Length > 50 ? item.Notes.Substring(0, 50) + "..." : item.Notes)
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">لا توجد ملاحظات</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a asp-action="Delete" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if (!Model.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد مصروفات</h5>
                            <p class="text-muted">ابدأ بتسجيل المصروفات لتتبع النفقات</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                إضافة أول مصروف
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // عرض رسائل النجاح والخطأ
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
                toastr.success('@TempData["SuccessMessage"]');
            </text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>
                toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
    </script>
}
