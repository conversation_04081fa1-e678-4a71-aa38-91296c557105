using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MarbleWorkshopSystem.Models
{
    public class PurchaseInvoiceItem
    {
        public int Id { get; set; }

        [Required]
        public int PurchaseInvoiceId { get; set; }

        [Required]
        public int ProductId { get; set; }

        [Range(0.01, double.MaxValue, ErrorMessage = "الطول يجب أن يكون أكبر من صفر")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Length { get; set; }

        [Range(0.01, double.MaxValue, ErrorMessage = "العرض يجب أن يكون أكبر من صفر")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Width { get; set; }

        [Required(ErrorMessage = "الكمية مطلوبة")]
        [Range(0.01, double.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من صفر")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Quantity { get; set; }

        [Required(ErrorMessage = "سعر الوحدة مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "سعر الوحدة يجب أن يكون أكبر من صفر")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPrice { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        // Calculated Properties
        public decimal CalculatedQuantity => Length * Width;

        // Navigation Properties
        [ForeignKey("PurchaseInvoiceId")]
        public virtual PurchaseInvoice PurchaseInvoice { get; set; } = null!;

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
    }
}
