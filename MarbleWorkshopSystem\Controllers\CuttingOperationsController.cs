using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MarbleWorkshopSystem.Models;
using MarbleWorkshopSystem.Services;
using MarbleWorkshopSystem.Data;

namespace MarbleWorkshopSystem.Controllers
{
    public class CuttingOperationsController : Controller
    {
        private readonly IRepository<CuttingOperation> _cuttingRepository;
        private readonly IProductService _productService;
        private readonly IInventoryService _inventoryService;
        private readonly ApplicationDbContext _context;

        public CuttingOperationsController(
            IRepository<CuttingOperation> cuttingRepository,
            IProductService productService,
            IInventoryService inventoryService,
            ApplicationDbContext context)
        {
            _cuttingRepository = cuttingRepository;
            _productService = productService;
            _inventoryService = inventoryService;
            _context = context;
        }

        // GET: CuttingOperations
        public async Task<IActionResult> Index(DateTime? fromDate, DateTime? toDate)
        {
            var operations = await _context.CuttingOperations
                .Include(c => c.Product)
                .ToListAsync();

            // تطبيق الفلاتر
            if (fromDate.HasValue)
            {
                operations = operations.Where(c => c.CuttingDate >= fromDate.Value).ToList();
            }

            if (toDate.HasValue)
            {
                operations = operations.Where(c => c.CuttingDate <= toDate.Value).ToList();
            }

            ViewBag.FromDate = fromDate?.ToString("yyyy-MM-dd");
            ViewBag.ToDate = toDate?.ToString("yyyy-MM-dd");

            return View(operations.OrderByDescending(c => c.CuttingDate));
        }

        // GET: CuttingOperations/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var operation = await _context.CuttingOperations
                .Include(c => c.Product)
                .FirstOrDefaultAsync(c => c.Id == id);

            if (operation == null)
            {
                return NotFound();
            }

            return View(operation);
        }

        // GET: CuttingOperations/Create
        public async Task<IActionResult> Create()
        {
            var products = await _productService.GetAllProductsAsync();
            ViewBag.Products = products.Where(p => p.IsActive).ToList();
            
            return View();
        }

        // POST: CuttingOperations/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CuttingOperation operation)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    // حساب نسبة الهالك
                    if (operation.OriginalQuantity > 0)
                    {
                        operation.WastePercentage = (operation.WasteQuantity / operation.OriginalQuantity) * 100;
                    }

                    // إضافة العملية
                    await _cuttingRepository.AddAsync(operation);
                    await _cuttingRepository.SaveChangesAsync();

                    // تحديث المخزون - خصم الكمية المستخدمة وإضافة الهالك
                    await _inventoryService.ReduceStockAsync(operation.ProductId, operation.UsedQuantity);
                    await _inventoryService.AddWasteAsync(operation.ProductId, operation.WasteQuantity);

                    TempData["SuccessMessage"] = "تم تسجيل عملية التقطيع بنجاح وتحديث المخزون";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "حدث خطأ أثناء تسجيل العملية: " + ex.Message);
                }
            }

            // إعادة تحميل المنتجات في حالة الخطأ
            var products = await _productService.GetAllProductsAsync();
            ViewBag.Products = products.Where(p => p.IsActive).ToList();
            
            return View(operation);
        }

        // GET: CuttingOperations/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var operation = await _context.CuttingOperations
                .Include(c => c.Product)
                .FirstOrDefaultAsync(c => c.Id == id);

            if (operation == null)
            {
                return NotFound();
            }

            var products = await _productService.GetAllProductsAsync();
            ViewBag.Products = products.Where(p => p.IsActive).ToList();

            return View(operation);
        }

        // POST: CuttingOperations/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, CuttingOperation operation)
        {
            if (id != operation.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // حساب نسبة الهالك
                    if (operation.OriginalQuantity > 0)
                    {
                        operation.WastePercentage = (operation.WasteQuantity / operation.OriginalQuantity) * 100;
                    }

                    await _cuttingRepository.UpdateAsync(operation);
                    await _cuttingRepository.SaveChangesAsync();
                    
                    TempData["SuccessMessage"] = "تم تحديث عملية التقطيع بنجاح";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "حدث خطأ أثناء تحديث العملية: " + ex.Message);
                }
            }

            var products = await _productService.GetAllProductsAsync();
            ViewBag.Products = products.Where(p => p.IsActive).ToList();

            return View(operation);
        }

        // GET: CuttingOperations/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var operation = await _context.CuttingOperations
                .Include(c => c.Product)
                .FirstOrDefaultAsync(c => c.Id == id);

            if (operation == null)
            {
                return NotFound();
            }

            return View(operation);
        }

        // POST: CuttingOperations/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                await _cuttingRepository.DeleteAsync(id);
                await _cuttingRepository.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم حذف عملية التقطيع بنجاح";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف العملية: " + ex.Message;
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: CuttingOperations/Report
        public async Task<IActionResult> Report(DateTime? fromDate, DateTime? toDate)
        {
            var operations = await _context.CuttingOperations
                .Include(c => c.Product)
                .ToListAsync();

            // تطبيق الفلاتر
            if (fromDate.HasValue)
            {
                operations = operations.Where(c => c.CuttingDate >= fromDate.Value).ToList();
            }

            if (toDate.HasValue)
            {
                operations = operations.Where(c => c.CuttingDate <= toDate.Value).ToList();
            }

            // إحصائيات
            ViewBag.TotalOperations = operations.Count;
            ViewBag.TotalOriginalQuantity = operations.Sum(o => o.OriginalQuantity);
            ViewBag.TotalUsedQuantity = operations.Sum(o => o.UsedQuantity);
            ViewBag.TotalWasteQuantity = operations.Sum(o => o.WasteQuantity);
            ViewBag.AverageWastePercentage = operations.Any() ? operations.Average(o => o.WastePercentage) : 0;

            // تجميع حسب المنتج
            ViewBag.ProductSummary = operations.GroupBy(o => o.Product.Name)
                                              .Select(g => new {
                                                  ProductName = g.Key,
                                                  OperationCount = g.Count(),
                                                  TotalOriginal = g.Sum(o => o.OriginalQuantity),
                                                  TotalUsed = g.Sum(o => o.UsedQuantity),
                                                  TotalWaste = g.Sum(o => o.WasteQuantity),
                                                  AvgWastePercentage = g.Average(o => o.WastePercentage)
                                              })
                                              .OrderByDescending(x => x.TotalWaste)
                                              .ToList();

            ViewBag.FromDate = fromDate?.ToString("yyyy-MM-dd");
            ViewBag.ToDate = toDate?.ToString("yyyy-MM-dd");

            return View(operations.OrderByDescending(c => c.CuttingDate));
        }

        // AJAX: Get Product Stock
        [HttpGet]
        public async Task<IActionResult> GetProductStock(int productId)
        {
            var inventory = await _inventoryService.GetInventoryByProductIdAsync(productId);
            if (inventory != null)
            {
                return Json(new { 
                    availableStock = inventory.AvailableQuantity,
                    totalStock = inventory.Quantity 
                });
            }
            
            return Json(new { availableStock = 0, totalStock = 0 });
        }
    }
}
