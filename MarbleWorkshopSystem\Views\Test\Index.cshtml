@model IEnumerable<MarbleWorkshopSystem.Controllers.TestResult>

@{
    ViewData["Title"] = "اختبار النظام";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-vial me-2"></i>
                        نتائج اختبار النظام
                    </h3>
                </div>

                <div class="card-body">
                    <!-- ملخص النتائج -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count()</h4>
                                    <p class="mb-0">إجمالي الاختبارات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count(t => t.IsSuccess)</h4>
                                    <p class="mb-0">اختبارات ناجحة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count(t => !t.IsSuccess)</h4>
                                    <p class="mb-0">اختبارات فاشلة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4>@(Model.Any() ? Math.Round((double)Model.Count(t => t.IsSuccess) / Model.Count() * 100, 1) : 0)%</h4>
                                    <p class="mb-0">معدل النجاح</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تفاصيل النتائج -->
                    <div class="row">
                        <div class="col-12">
                            <h5 class="mb-3">تفاصيل الاختبارات:</h5>
                            
                            @foreach (var test in Model)
                            {
                                <div class="card mb-3 @(test.IsSuccess ? "border-success" : "border-danger")">
                                    <div class="card-header @(test.IsSuccess ? "bg-success" : "bg-danger") text-white">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">
                                                <i class="fas @(test.IsSuccess ? "fa-check-circle" : "fa-times-circle") me-2"></i>
                                                @test.TestName
                                            </h6>
                                            <span class="badge @(test.IsSuccess ? "bg-light text-success" : "bg-light text-danger")">
                                                @(test.IsSuccess ? "نجح" : "فشل")
                                            </span>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <p class="mb-2"><strong>النتيجة:</strong> @test.Message</p>
                                        @if (!string.IsNullOrEmpty(test.Details))
                                        {
                                            <div class="mt-3">
                                                <button class="btn btn-sm btn-outline-info" type="button" 
                                                        data-bs-toggle="collapse" data-bs-target="#<EMAIL>()" 
                                                        aria-expanded="false">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    عرض التفاصيل
                                                </button>
                                                <div class="collapse mt-2" id="<EMAIL>()">
                                                    <div class="card card-body bg-light">
                                                        <pre class="mb-0" style="white-space: pre-wrap; font-size: 0.9em;">@test.Details</pre>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <a href="/" class="btn btn-secondary">
                                    <i class="fas fa-home me-1"></i>
                                    العودة للرئيسية
                                </a>
                                <div>
                                    <a href="/Products" class="btn btn-primary me-2">
                                        <i class="fas fa-cube me-1"></i>
                                        إدارة المنتجات
                                    </a>
                                    <button onclick="location.reload()" class="btn btn-info">
                                        <i class="fas fa-sync-alt me-1"></i>
                                        إعادة الاختبار
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات النظام -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-info me-2"></i>
                                        معلومات النظام
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>اسم النظام:</strong> نظام إدارة ورشة الرخام</p>
                                            <p><strong>الإصدار:</strong> 1.0.0</p>
                                            <p><strong>تاريخ الاختبار:</strong> @DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>إطار العمل:</strong> ASP.NET Core 9.0</p>
                                            <p><strong>قاعدة البيانات:</strong> SQL Server</p>
                                            <p><strong>البيئة:</strong> Development</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // عرض رسالة نجاح أو فشل
        @if (Model.All(t => t.IsSuccess))
        {
            <text>
                toastr.success('جميع الاختبارات نجحت! النظام يعمل بشكل صحيح.');
            </text>
        }
        else if (Model.Any(t => !t.IsSuccess))
        {
            <text>
                toastr.warning('بعض الاختبارات فشلت. يرجى مراجعة التفاصيل.');
            </text>
        }
    </script>
}
