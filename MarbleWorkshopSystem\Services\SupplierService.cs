using Microsoft.EntityFrameworkCore;
using MarbleWorkshopSystem.Data;
using MarbleWorkshopSystem.Models;

namespace MarbleWorkshopSystem.Services
{
    public class SupplierService : ISupplierService
    {
        private readonly IRepository<Supplier> _supplierRepository;
        private readonly ApplicationDbContext _context;

        public SupplierService(IRepository<Supplier> supplierRepository, ApplicationDbContext context)
        {
            _supplierRepository = supplierRepository;
            _context = context;
        }

        public async Task<IEnumerable<Supplier>> GetAllSuppliersAsync()
        {
            return await _context.Suppliers
                .Where(s => s.IsActive)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<Supplier?> GetSupplierByIdAsync(int id)
        {
            return await _context.Suppliers
                .Include(s => s.PurchaseInvoices)
                .Include(s => s.Payments)
                .FirstOrDefaultAsync(s => s.Id == id);
        }

        public async Task<IEnumerable<Supplier>> SearchSuppliersAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllSuppliersAsync();

            return await _context.Suppliers
                .Where(s => s.IsActive && 
                           (s.Name.Contains(searchTerm) || 
                            s.Phone!.Contains(searchTerm) ||
                            s.Email!.Contains(searchTerm)))
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        public async Task<Supplier> CreateSupplierAsync(Supplier supplier)
        {
            supplier.CreatedDate = DateTime.Now;

            // تطبيق الرصيد الافتتاحي
            if (supplier.OpeningBalance > 0)
            {
                // إذا كان مدين (له) فالرصيد سالب (نحن مدينون له)
                // إذا كان دائن (عليه) فالرصيد موجب (هو مدين لنا)
                supplier.Balance = supplier.OpeningBalanceType == BalanceType.Debit
                    ? -supplier.OpeningBalance
                    : supplier.OpeningBalance;
            }
            else
            {
                supplier.Balance = 0;
            }

            var createdSupplier = await _supplierRepository.AddAsync(supplier);
            await _supplierRepository.SaveChangesAsync();

            return createdSupplier;
        }

        public async Task<Supplier> UpdateSupplierAsync(Supplier supplier)
        {
            var updatedSupplier = await _supplierRepository.UpdateAsync(supplier);
            await _supplierRepository.SaveChangesAsync();
            
            return updatedSupplier;
        }

        public async Task DeleteSupplierAsync(int id)
        {
            var supplier = await GetSupplierByIdAsync(id);
            if (supplier != null)
            {
                supplier.IsActive = false;
                await UpdateSupplierAsync(supplier);
            }
        }

        public async Task<bool> SupplierExistsAsync(int id)
        {
            return await _supplierRepository.ExistsAsync(id);
        }

        public async Task<decimal> GetSupplierBalanceAsync(int supplierId)
        {
            var supplier = await GetSupplierByIdAsync(supplierId);
            return supplier?.Balance ?? 0;
        }

        public async Task UpdateSupplierBalanceAsync(int supplierId, decimal amount)
        {
            var supplier = await GetSupplierByIdAsync(supplierId);
            if (supplier != null)
            {
                supplier.Balance += amount;
                await UpdateSupplierAsync(supplier);
            }
        }

        public async Task<IEnumerable<PurchaseInvoice>> GetSupplierInvoicesAsync(int supplierId)
        {
            return await _context.PurchaseInvoices
                .Include(p => p.Items)
                .ThenInclude(i => i.Product)
                .Where(p => p.SupplierId == supplierId)
                .OrderByDescending(p => p.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Payment>> GetSupplierPaymentsAsync(int supplierId)
        {
            return await _context.Payments
                .Where(p => p.SupplierId == supplierId)
                .OrderByDescending(p => p.PaymentDate)
                .ToListAsync();
        }

        public async Task<decimal> GetSupplierTotalPurchasesAsync(int supplierId)
        {
            return await _context.PurchaseInvoices
                .Where(p => p.SupplierId == supplierId && p.Status == "مؤكدة")
                .SumAsync(p => p.TotalAmount);
        }

        public async Task<decimal> GetSupplierTotalPaymentsAsync(int supplierId)
        {
            return await _context.Payments
                .Where(p => p.SupplierId == supplierId)
                .SumAsync(p => p.Amount);
        }
    }
}
