@model MarbleWorkshopSystem.Models.Product

@{
    ViewData["Title"] = "إضافة منتج جديد";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-plus me-2"></i>
                        إضافة منتج جديد
                    </h3>
                </div>

                <div class="card-body">
                    <!-- رسالة النجاح -->
                    <div id="successMessage" class="alert alert-success alert-dismissible fade" role="alert" style="display: none;">
                        <i class="fas fa-check-circle me-2"></i>
                        <span id="successText">تم حفظ المنتج بنجاح</span>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>

                    <form id="productForm" asp-action="Create" method="post" enctype="multipart/form-data">
                        @Html.AntiForgeryToken()
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <div class="row">
                            <!-- معلومات أساسية -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Name" class="form-label">اسم المنتج *</label>
                                    <input asp-for="Name" class="form-control" placeholder="أدخل اسم المنتج">
                                    <span asp-validation-for="Name" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="Description" class="form-label">الوصف</label>
                                    <textarea asp-for="Description" class="form-control" rows="3" 
                                              placeholder="وصف المنتج (اختياري)"></textarea>
                                    <span asp-validation-for="Description" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="Category" class="form-label">الفئة *</label>
                                    <input asp-for="Category" class="form-control" list="categories" 
                                           placeholder="أدخل أو اختر فئة المنتج">
                                    <datalist id="categories">
                                        @if (ViewBag.Categories != null)
                                        {
                                            @foreach (var category in ViewBag.Categories as IEnumerable<string>)
                                            {
                                                <option value="@category"></option>
                                            }
                                        }
                                    </datalist>
                                    <span asp-validation-for="Category" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="UnitPrice" class="form-label">سعر الوحدة (جنيه) *</label>
                                    <input asp-for="UnitPrice" class="form-control" type="number" step="0.01" min="0"
                                           placeholder="0.00">
                                    <span asp-validation-for="UnitPrice" class="text-danger"></span>
                                </div>
                            </div>

                            <!-- الأبعاد والصورة -->
                            <div class="col-md-6">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="Length" class="form-label">الطول (متر) *</label>
                                            <input asp-for="Length" class="form-control" type="number" step="0.01" min="0.01"
                                                   id="lengthInput" placeholder="0.00">
                                            <span asp-validation-for="Length" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="Width" class="form-label">العرض (متر) *</label>
                                            <input asp-for="Width" class="form-control" type="number" step="0.01" min="0.01"
                                                   id="widthInput" placeholder="0.00">
                                            <span asp-validation-for="Width" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">المساحة (متر مربع)</label>
                                    <input type="text" class="form-control bg-light" id="areaDisplay" readonly 
                                           placeholder="سيتم حسابها تلقائياً">
                                    <small class="form-text text-muted">
                                        المساحة = الطول × العرض
                                    </small>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">صورة المنتج</label>
                                    <input type="file" name="imageFile" class="form-control" accept="image/*"
                                           id="imageInput">
                                    <small class="form-text text-muted">
                                        اختر صورة للمنتج (JPG, PNG, GIF)
                                    </small>
                                </div>

                                <!-- معاينة الصورة -->
                                <div class="mb-3">
                                    <div id="imagePreview" class="text-center" style="display: none;">
                                        <img id="previewImg" src="#" alt="معاينة الصورة" 
                                             class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        العودة للقائمة
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ المنتج
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // حساب المساحة تلقائياً
            function calculateArea() {
                var length = parseFloat($('#lengthInput').val()) || 0;
                var width = parseFloat($('#widthInput').val()) || 0;
                var area = length * width;
                
                if (area > 0) {
                    $('#areaDisplay').val(area.toFixed(2) + ' متر مربع');
                } else {
                    $('#areaDisplay').val('');
                }
            }

            // ربط الأحداث
            $('#lengthInput, #widthInput').on('input change', calculateArea);

            // معاينة الصورة
            $('#imageInput').change(function() {
                var file = this.files[0];
                if (file) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        $('#previewImg').attr('src', e.target.result);
                        $('#imagePreview').show();
                    }
                    reader.readAsDataURL(file);
                } else {
                    $('#imagePreview').hide();
                }
            });

            // حساب المساحة عند تحميل الصفحة
            calculateArea();

            // معالجة إرسال النموذج
            $('#productForm').on('submit', function(e) {
                e.preventDefault();

                var formData = new FormData(this);
                var submitButton = $(this).find('button[type="submit"]');
                var originalText = submitButton.html();

                // تعطيل الزر وإظهار حالة التحميل
                submitButton.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>جاري الحفظ...');

                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            // إظهار رسالة النجاح
                            $('#successMessage').removeClass('fade').addClass('show').show();

                            // تفريغ النموذج
                            $('#productForm')[0].reset();
                            $('#imagePreview').hide();
                            $('#areaDisplay').text('0.00');

                            // إخفاء رسالة النجاح بعد 3 ثوان
                            setTimeout(function() {
                                $('#successMessage').removeClass('show').addClass('fade');
                                setTimeout(function() {
                                    $('#successMessage').hide();
                                }, 150);
                            }, 3000);
                        } else {
                            alert('حدث خطأ أثناء حفظ المنتج: ' + (response.error || 'خطأ غير معروف'));
                        }
                    },
                    error: function(xhr, status, error) {
                        alert('حدث خطأ أثناء حفظ المنتج. يرجى المحاولة مرة أخرى.');
                        console.error('Error:', error);
                    },
                    complete: function() {
                        // إعادة تفعيل الزر
                        submitButton.prop('disabled', false).html(originalText);
                    }
                });
            });
        });
    </script>
}
