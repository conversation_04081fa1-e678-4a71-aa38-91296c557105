@model IEnumerable<MarbleWorkshopSystem.Models.Payment>

@{
    ViewData["Title"] = "سندات الدفع";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        سندات الدفع
                    </h3>
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        سند دفع جديد
                    </a>
                </div>
                <div class="card-body">
                    <!-- فلاتر البحث -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="get" class="row g-3">
                                <div class="col-md-3">
                                    <label for="supplierId" class="form-label">المورد</label>
                                    <select name="supplierId" id="supplierId" class="form-select">
                                        <option value="">جميع الموردين</option>
                                        @foreach (var supplier in ViewBag.Suppliers as IEnumerable<MarbleWorkshopSystem.Models.Supplier>)
                                        {
                                            <option value="@supplier.Id" selected="@(ViewBag.SelectedSupplierId?.ToString() == supplier.Id.ToString())">
                                                @supplier.Name
                                            </option>
                                        }
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="fromDate" class="form-label">من تاريخ</label>
                                    <input type="date" name="fromDate" id="fromDate" class="form-control" value="@ViewBag.FromDate" />
                                </div>
                                <div class="col-md-3">
                                    <label for="toDate" class="form-label">إلى تاريخ</label>
                                    <input type="date" name="toDate" id="toDate" class="form-control" value="@ViewBag.ToDate" />
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <button type="submit" class="btn btn-info me-2">
                                        <i class="fas fa-search me-1"></i>
                                        بحث
                                    </button>
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-undo me-1"></i>
                                        إعادة تعيين
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count()</h4>
                                    <p class="mb-0">إجمالي السندات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Sum(p => p.Amount).ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h4>
                                    <p class="mb-0">إجمالي المبلغ</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4>@(Model.Any() ? Model.Average(p => p.Amount).ToString("C", new System.Globalization.CultureInfo("ar-EG")) : "0")</h4>
                                    <p class="mb-0">متوسط المبلغ</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count(p => p.PaymentDate.Date == DateTime.Today)</h4>
                                    <p class="mb-0">سندات اليوم</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول السندات -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم السند</th>
                                    <th>التاريخ</th>
                                    <th>المورد</th>
                                    <th>المبلغ</th>
                                    <th>طريقة الدفع</th>
                                    <th>الملاحظات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            <strong class="text-primary">@item.PaymentNumber</strong>
                                        </td>
                                        <td>@item.PaymentDate.ToString("dd/MM/yyyy")</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-truck me-2 text-muted"></i>
                                                @item.Supplier?.Name
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-danger fs-6">
                                                @item.Amount.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">@item.PaymentMethod</span>
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.Notes))
                                            {
                                                <span title="@item.Notes">
                                                    @(item.Notes.Length > 30 ? item.Notes.Substring(0, 30) + "..." : item.Notes)
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-info" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a asp-action="Print" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-secondary" title="طباعة" target="_blank">
                                                    <i class="fas fa-print"></i>
                                                </a>
                                                <a asp-action="Delete" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if (!Model.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد سندات دفع</h5>
                            <p class="text-muted">ابدأ بإنشاء سندات الدفع لتتبع المدفوعات للموردين</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                إنشاء أول سند دفع
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // عرض رسائل النجاح والخطأ
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
                toastr.success('@TempData["SuccessMessage"]');
            </text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>
                toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
    </script>
}
