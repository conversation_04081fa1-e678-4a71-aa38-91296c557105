@model MarbleWorkshopSystem.Models.Supplier

@{
    ViewData["Title"] = "تعديل المورد";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        تعديل بيانات المورد: @Model.Name
                    </h4>
                </div>

                <div class="card-body">
                    <form asp-action="Edit" method="post">
                        <input type="hidden" asp-for="Id" />
                        <input type="hidden" asp-for="CreatedDate" />
                        <input type="hidden" asp-for="Balance" />

                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Name" class="form-label">
                                        <i class="fas fa-truck me-1"></i>
                                        اسم المورد <span class="text-danger">*</span>
                                    </label>
                                    <input asp-for="Name" class="form-control" placeholder="أدخل اسم المورد" />
                                    <span asp-validation-for="Name" class="text-danger"></span>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Phone" class="form-label">
                                        <i class="fas fa-phone me-1"></i>
                                        رقم الهاتف
                                    </label>
                                    <input asp-for="Phone" class="form-control" placeholder="أدخل رقم الهاتف" />
                                    <span asp-validation-for="Phone" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Email" class="form-label">
                                        <i class="fas fa-envelope me-1"></i>
                                        البريد الإلكتروني
                                    </label>
                                    <input asp-for="Email" type="email" class="form-control" placeholder="أدخل البريد الإلكتروني" />
                                    <span asp-validation-for="Email" class="text-danger"></span>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="TaxNumber" class="form-label">
                                        <i class="fas fa-file-invoice me-1"></i>
                                        الرقم الضريبي
                                    </label>
                                    <input asp-for="TaxNumber" class="form-control" placeholder="أدخل الرقم الضريبي" />
                                    <span asp-validation-for="TaxNumber" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label asp-for="Address" class="form-label">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        العنوان
                                    </label>
                                    <textarea asp-for="Address" class="form-control" rows="3" placeholder="أدخل العنوان"></textarea>
                                    <span asp-validation-for="Address" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <!-- الرصيد الافتتاحي -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-balance-scale me-2"></i>
                                            الرصيد الافتتاحي
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label asp-for="OpeningBalance" class="form-label">مبلغ الرصيد الافتتاحي</label>
                                                    <div class="input-group">
                                                        <input asp-for="OpeningBalance" class="form-control" placeholder="0.00" step="0.01" min="0">
                                                        <span class="input-group-text">ج.م</span>
                                                    </div>
                                                    <span asp-validation-for="OpeningBalance" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label asp-for="OpeningBalanceType" class="form-label">نوع الرصيد</label>
                                                    <select asp-for="OpeningBalanceType" class="form-select">
                                                        <option value="1">مدين (له)</option>
                                                        <option value="2">دائن (عليه)</option>
                                                    </select>
                                                    <span asp-validation-for="OpeningBalanceType" class="text-danger"></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="alert alert-info mb-0">
                                                    <i class="fas fa-info-circle me-2"></i>
                                                    <strong>مدين (له):</strong> المورد له رصيد في ذمتنا (نحن مدينون له)
                                                    <br>
                                                    <strong>دائن (عليه):</strong> المورد عليه رصيد لنا (هو مدين لنا)
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                        <label asp-for="IsActive" class="form-check-label">
                                            <i class="fas fa-check-circle me-1"></i>
                                            المورد نشط
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-save me-1"></i>
                                            حفظ التعديلات
                                        </button>
                                        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info">
                                            <i class="fas fa-eye me-1"></i>
                                            عرض التفاصيل
                                        </a>
                                    </div>
                                    <div>
                                        <a asp-action="Index" class="btn btn-secondary">
                                            <i class="fas fa-arrow-left me-1"></i>
                                            العودة للقائمة
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
