@model MarbleWorkshopSystem.ViewModels.InventoryReportViewModel

@{
    ViewData["Title"] = "تقرير المخزون";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-boxes me-2"></i>
                        تقرير المخزون الشامل
                    </h4>
                </div>

                <div class="card-body">
                    <!-- إحصائيات عامة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3>@Model.TotalProducts</h3>
                                    <p class="mb-0">إجمالي المنتجات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h3>@Model.LowStockCount</h3>
                                    <p class="mb-0">منتجات منخفضة المخزون</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3>@Model.TotalStockValue.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h3>
                                    <p class="mb-0">قيمة المخزون الإجمالية</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h3>@Model.TotalWaste.ToString("F2")</h3>
                                    <p class="mb-0">إجمالي الهالك</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        العودة للتقارير
                                    </a>
                                    <a asp-controller="Inventory" asp-action="Index" class="btn btn-primary">
                                        <i class="fas fa-boxes me-1"></i>
                                        إدارة المخزون
                                    </a>
                                </div>
                                <div>
                                    <a asp-action="GenerateInventoryReport" class="btn btn-success" target="_blank">
                                        <i class="fas fa-file-pdf me-1"></i>
                                        تصدير PDF
                                    </a>
                                    <button type="button" class="btn btn-info" onclick="window.print()">
                                        <i class="fas fa-print me-1"></i>
                                        طباعة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تنبيهات المخزون المنخفض -->
                    @if (Model.LowStockItems.Any())
                    {
                        <div class="alert alert-danger">
                            <h5>
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                تنبيه: منتجات تحتاج إعادة تموين (@Model.LowStockCount منتج)
                            </h5>
                            <div class="row">
                                @foreach (var item in Model.LowStockItems.Take(6))
                                {
                                    <div class="col-md-4 mb-2">
                                        <div class="card border-danger">
                                            <div class="card-body p-2">
                                                <div class="d-flex align-items-center">
                                                    @if (!string.IsNullOrEmpty(item.Product.ImagePath))
                                                    {
                                                        <img src="@item.Product.ImagePath" alt="@item.Product.Name" 
                                                             class="img-thumbnail me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                                    }
                                                    <div class="flex-grow-1">
                                                        <small class="fw-bold">@item.Product.Name</small>
                                                        <br><small class="text-muted">متبقي: @item.Quantity.ToString("F1")</small>
                                                    </div>
                                                    <span class="badge bg-danger">@item.MinimumStock.ToString("F1")</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                            @if (Model.LowStockCount > 6)
                            {
                                <p class="mb-0 mt-2">
                                    <a asp-controller="Inventory" asp-action="LowStock" class="btn btn-sm btn-outline-danger">
                                        عرض جميع المنتجات (@Model.LowStockCount)
                                    </a>
                                </p>
                            }
                        </div>
                    }

                    <!-- جدول تفاصيل المخزون -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-table me-2"></i>
                                تفاصيل المخزون
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="inventoryTable">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الفئة</th>
                                            <th>الكمية الحالية</th>
                                            <th>الكمية المتاحة</th>
                                            <th>الحد الأدنى</th>
                                            <th>الهالك</th>
                                            <th>سعر الوحدة</th>
                                            <th>قيمة المخزون</th>
                                            <th>الحالة</th>
                                            <th>آخر تحديث</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var item in Model.InventoryItems)
                                        {
                                            <tr class="@(item.IsLowStock ? (item.Quantity == 0 ? "table-danger" : "table-warning") : "")">
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        @if (!string.IsNullOrEmpty(item.Product.ImagePath))
                                                        {
                                                            <img src="@item.Product.ImagePath" alt="@item.Product.Name" 
                                                                 class="img-thumbnail me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                                        }
                                                        <div>
                                                            <strong>@item.Product.Name</strong>
                                                            @if (!string.IsNullOrEmpty(item.Product.Description))
                                                            {
                                                                <br><small class="text-muted">@item.Product.Description</small>
                                                            }
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary">@item.Product.Category</span>
                                                </td>
                                                <td>
                                                    <span class="badge @(item.Quantity == 0 ? "bg-danger" : item.IsLowStock ? "bg-warning" : "bg-success") fs-6">
                                                        @item.Quantity.ToString("F2")
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge @(item.AvailableQuantity <= 0 ? "bg-danger" : "bg-info") fs-6">
                                                        @item.AvailableQuantity.ToString("F2")
                                                    </span>
                                                </td>
                                                <td>@item.MinimumStock.ToString("F2")</td>
                                                <td>
                                                    @if (item.WasteQuantity > 0)
                                                    {
                                                        <span class="badge bg-secondary fs-6">@item.WasteQuantity.ToString("F2")</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">-</span>
                                                    }
                                                </td>
                                                <td>@item.Product.UnitPrice.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</td>
                                                <td>
                                                    <strong>@((item.Quantity * item.Product.UnitPrice).ToString("C", new System.Globalization.CultureInfo("ar-EG")))</strong>
                                                </td>
                                                <td>
                                                    @if (item.Quantity == 0)
                                                    {
                                                        <span class="badge bg-danger">
                                                            <i class="fas fa-times me-1"></i>نفد المخزون
                                                        </span>
                                                    }
                                                    else if (item.IsLowStock)
                                                    {
                                                        <span class="badge bg-warning">
                                                            <i class="fas fa-exclamation-triangle me-1"></i>منخفض
                                                        </span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-success">
                                                            <i class="fas fa-check me-1"></i>جيد
                                                        </span>
                                                    }
                                                </td>
                                                <td>
                                                    <small>@item.LastUpdated.ToString("dd/MM/yyyy")</small>
                                                    <br><small class="text-muted">@item.LastUpdated.ToString("HH:mm")</small>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                    <tfoot class="table-light">
                                        <tr>
                                            <th colspan="7">الإجمالي:</th>
                                            <th>@Model.TotalStockValue.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</th>
                                            <th colspan="2">
                                                <span class="badge bg-primary">@Model.TotalProducts منتج</span>
                                            </th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- ملخص إحصائي -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-chart-pie me-2"></i>
                                        توزيع حالة المخزون
                                    </h6>
                                </div>
                                <div class="card-body">
                                    @{
                                        var goodStock = Model.InventoryItems.Count(i => !i.IsLowStock);
                                        var lowStock = Model.InventoryItems.Count(i => i.IsLowStock && i.Quantity > 0);
                                        var outOfStock = Model.InventoryItems.Count(i => i.Quantity == 0);
                                    }
                                    <div class="row">
                                        <div class="col-4 text-center">
                                            <div class="text-success">
                                                <h4>@goodStock</h4>
                                                <small>مخزون جيد</small>
                                            </div>
                                        </div>
                                        <div class="col-4 text-center">
                                            <div class="text-warning">
                                                <h4>@lowStock</h4>
                                                <small>مخزون منخفض</small>
                                            </div>
                                        </div>
                                        <div class="col-4 text-center">
                                            <div class="text-danger">
                                                <h4>@outOfStock</h4>
                                                <small>نفد المخزون</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-calculator me-2"></i>
                                        إحصائيات مالية
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <p class="mb-1"><strong>قيمة المخزون:</strong></p>
                                            <h5 class="text-success">@Model.TotalStockValue.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h5>
                                        </div>
                                        <div class="col-6">
                                            <p class="mb-1"><strong>متوسط قيمة المنتج:</strong></p>
                                            <h5 class="text-info">@((Model.TotalProducts > 0 ? Model.TotalStockValue / Model.TotalProducts : 0).ToString("C", new System.Globalization.CultureInfo("ar-EG")))</h5>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-6">
                                            <p class="mb-1"><strong>إجمالي الكمية:</strong></p>
                                            <h6 class="text-primary">@Model.InventoryItems.Sum(i => i.Quantity).ToString("F2") وحدة</h6>
                                        </div>
                                        <div class="col-6">
                                            <p class="mb-1"><strong>إجمالي الهالك:</strong></p>
                                            <h6 class="text-secondary">@Model.TotalWaste.ToString("F2") وحدة</h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات التقرير -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6><i class="fas fa-info-circle me-2"></i>معلومات التقرير:</h6>
                                            <ul class="mb-0">
                                                <li>تاريخ التقرير: @DateTime.Now.ToString("dd/MM/yyyy HH:mm")</li>
                                                <li>إجمالي المنتجات: @Model.TotalProducts منتج</li>
                                                <li>المنتجات النشطة: @Model.InventoryItems.Count(i => i.Product.IsActive) منتج</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6><i class="fas fa-lightbulb me-2"></i>ملاحظات:</h6>
                                            <ul class="mb-0">
                                                <li>الكمية المتاحة = الكمية الحالية - الهالك</li>
                                                <li>قيمة المخزون = الكمية الحالية × سعر الوحدة</li>
                                                <li>المخزون المنخفض = الكمية ≤ الحد الأدنى</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // تفعيل DataTables للجدول
            $('#inventoryTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
                },
                "pageLength": 25,
                "order": [[8, "desc"], [2, "asc"]], // ترتيب حسب الحالة ثم الكمية
                "columnDefs": [
                    { "orderable": false, "targets": [0] } // عدم ترتيب عمود الصورة
                ]
            });
        });
    </script>
}
