@echo off
chcp 65001 > nul
title فحص توافق النظام - System Compatibility Check

echo ========================================
echo    فحص توافق النظام
echo    System Compatibility Check
echo ========================================
echo.

REM التحقق من إصدار Windows
echo جاري فحص إصدار Windows...
echo Checking Windows version...

for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo إصدار Windows: %VERSION%
echo Windows version: %VERSION%

REM تحديد نوع النظام
if "%VERSION%"=="6.1" (
    echo النظام: Windows 7
    echo System: Windows 7
    set WINDOWS_7=true
) else if "%VERSION%"=="6.2" (
    echo النظام: Windows 8
    echo System: Windows 8
    set WINDOWS_8=true
) else if "%VERSION%"=="6.3" (
    echo النظام: Windows 8.1
    echo System: Windows 8.1
    set WINDOWS_81=true
) else if "%VERSION%"=="10.0" (
    echo النظام: Windows 10/11
    echo System: Windows 10/11
    set WINDOWS_10=true
) else (
    echo النظام: غير معروف
    echo System: Unknown
    set UNKNOWN_OS=true
)

echo.

REM التحقق من معمارية النظام
echo جاري فحص معمارية النظام...
echo Checking system architecture...

if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    echo المعمارية: 64-bit
    echo Architecture: 64-bit
    set ARCH_64=true
) else if "%PROCESSOR_ARCHITECTURE%"=="x86" (
    echo المعمارية: 32-bit
    echo Architecture: 32-bit
    set ARCH_32=true
) else (
    echo المعمارية: غير معروفة
    echo Architecture: Unknown
)

echo.

REM التحقق من .NET Framework
echo جاري فحص .NET Framework...
echo Checking .NET Framework...

reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" /v Release >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ .NET Framework 4.x مثبت
    echo ✓ .NET Framework 4.x installed
) else (
    echo ✗ .NET Framework 4.x غير مثبت
    echo ✗ .NET Framework 4.x not installed
)

REM التحقق من .NET Runtime
echo.
echo جاري فحص .NET Runtime...
echo Checking .NET Runtime...

dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=1" %%i in ('dotnet --version') do set DOTNET_VERSION=%%i
    echo ✓ .NET Runtime مثبت - الإصدار: !DOTNET_VERSION!
    echo ✓ .NET Runtime installed - Version: !DOTNET_VERSION!
    set DOTNET_INSTALLED=true
) else (
    echo ✗ .NET Runtime غير مثبت
    echo ✗ .NET Runtime not installed
    set DOTNET_INSTALLED=false
)

echo.

REM التحقق من SQL Server
echo جاري فحص SQL Server...
echo Checking SQL Server...

sc query "MSSQL$SQLEXPRESS" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ SQL Server Express مثبت
    echo ✓ SQL Server Express installed
    set SQLEXPRESS_INSTALLED=true
) else (
    echo ✗ SQL Server Express غير مثبت
    echo ✗ SQL Server Express not installed
    set SQLEXPRESS_INSTALLED=false
)

REM التحقق من LocalDB
sqllocaldb info >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ SQL Server LocalDB متاح
    echo ✓ SQL Server LocalDB available
    set LOCALDB_AVAILABLE=true
) else (
    echo ✗ SQL Server LocalDB غير متاح
    echo ✗ SQL Server LocalDB not available
    set LOCALDB_AVAILABLE=false
)

echo.

REM التحقق من المنافذ
echo جاري فحص المنافذ...
echo Checking ports...

netstat -an | find ":5000" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠ المنفذ 5000 مستخدم
    echo ⚠ Port 5000 is in use
) else (
    echo ✓ المنفذ 5000 متاح
    echo ✓ Port 5000 is available
)

echo.

REM ملخص التوافق
echo ========================================
echo    ملخص التوافق
echo    Compatibility Summary
echo ========================================

if defined WINDOWS_7 (
    echo.
    echo نظام Windows 7 مكتشف
    echo Windows 7 detected
    echo.
    echo التوصيات:
    echo Recommendations:
    echo - استخدم النسخة المستقلة من التطبيق
    echo   Use the self-contained version
    echo - تأكد من تثبيت .NET Framework 4.7.2 أو أحدث
    echo   Ensure .NET Framework 4.7.2+ is installed
    echo - فعل TLS 1.2 في إعدادات النظام
    echo   Enable TLS 1.2 in system settings
)

if defined WINDOWS_10 (
    echo.
    echo نظام Windows 10/11 مكتشف
    echo Windows 10/11 detected
    echo.
    echo ✓ توافق كامل مع جميع المميزات
    echo ✓ Full compatibility with all features
)

echo.
echo حالة المتطلبات:
echo Requirements status:

if "%DOTNET_INSTALLED%"=="true" (
    echo ✓ .NET Runtime
) else (
    echo ✗ .NET Runtime - مطلوب للتشغيل
    echo ✗ .NET Runtime - Required for operation
)

if "%SQLEXPRESS_INSTALLED%"=="true" (
    echo ✓ SQL Server Express
) else if "%LOCALDB_AVAILABLE%"=="true" (
    echo ✓ SQL Server LocalDB
) else (
    echo ✗ قاعدة البيانات - مطلوبة
    echo ✗ Database - Required
)

echo.

if "%DOTNET_INSTALLED%"=="false" (
    echo تحميل .NET Runtime:
    echo Download .NET Runtime:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
)

if "%SQLEXPRESS_INSTALLED%"=="false" and "%LOCALDB_AVAILABLE%"=="false" (
    echo تحميل SQL Server Express:
    echo Download SQL Server Express:
    echo https://www.microsoft.com/sql-server/sql-server-downloads
    echo.
)

echo اضغط أي مفتاح للمتابعة...
echo Press any key to continue...
pause >nul
