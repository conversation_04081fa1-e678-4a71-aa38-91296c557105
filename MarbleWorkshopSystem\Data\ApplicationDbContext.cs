using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using MarbleWorkshopSystem.Models;

namespace MarbleWorkshopSystem.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        // DbSets
        public DbSet<Product> Products { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<Inventory> Inventories { get; set; }
        public DbSet<SalesInvoice> SalesInvoices { get; set; }
        public DbSet<SalesInvoiceItem> SalesInvoiceItems { get; set; }
        public DbSet<PurchaseInvoice> PurchaseInvoices { get; set; }
        public DbSet<PurchaseInvoiceItem> PurchaseInvoiceItems { get; set; }
        public DbSet<Expense> Expenses { get; set; }
        public DbSet<Receipt> Receipts { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<CuttingOperation> CuttingOperations { get; set; }
        public new DbSet<UserRole> UserRoles { get; set; }
        public DbSet<SystemSetting> SystemSettings { get; set; }
        public DbSet<CompanyInfo> CompanyInfos { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Product Configuration
            modelBuilder.Entity<Product>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Category).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Length).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Width).HasColumnType("decimal(18,2)");
                entity.Property(e => e.UnitPrice).HasColumnType("decimal(18,2)");

                entity.HasIndex(e => e.Name);
            });

            // Customer Configuration
            modelBuilder.Entity<Customer>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Balance).HasColumnType("decimal(18,2)");
                entity.HasIndex(e => e.Name);
            });

            // Supplier Configuration
            modelBuilder.Entity<Supplier>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Balance).HasColumnType("decimal(18,2)");
                entity.HasIndex(e => e.Name);
            });

            // Inventory Configuration
            modelBuilder.Entity<Inventory>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Quantity).HasColumnType("decimal(18,2)");
                entity.Property(e => e.MinimumStock).HasColumnType("decimal(18,2)");
                entity.Property(e => e.WasteQuantity).HasColumnType("decimal(18,2)");
                entity.HasOne(e => e.Product)
                      .WithOne(p => p.Inventory)
                      .HasForeignKey<Inventory>(e => e.ProductId);
            });

            // SalesInvoice Configuration
            modelBuilder.Entity<SalesInvoice>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.InvoiceNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.SubTotal).HasColumnType("decimal(18,2)");
                entity.Property(e => e.DiscountValue).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TotalAmount).HasColumnType("decimal(18,2)");
                entity.HasIndex(e => e.InvoiceNumber).IsUnique();
                entity.HasOne(e => e.Customer)
                      .WithMany(c => c.SalesInvoices)
                      .HasForeignKey(e => e.CustomerId);
            });

            // SalesInvoiceItem Configuration
            modelBuilder.Entity<SalesInvoiceItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Quantity).HasColumnType("decimal(18,2)");
                entity.Property(e => e.UnitPrice).HasColumnType("decimal(18,2)");

                entity.HasOne(e => e.SalesInvoice)
                      .WithMany(s => s.Items)
                      .HasForeignKey(e => e.SalesInvoiceId);
                entity.HasOne(e => e.Product)
                      .WithMany(p => p.SalesInvoiceItems)
                      .HasForeignKey(e => e.ProductId);
            });

            // PurchaseInvoice Configuration
            modelBuilder.Entity<PurchaseInvoice>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.InvoiceNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.SubTotal).HasColumnType("decimal(18,2)");
                entity.Property(e => e.DiscountValue).HasColumnType("decimal(18,2)");
                entity.Property(e => e.TotalAmount).HasColumnType("decimal(18,2)");
                entity.HasIndex(e => e.InvoiceNumber).IsUnique();
                entity.HasOne(e => e.Supplier)
                      .WithMany(s => s.PurchaseInvoices)
                      .HasForeignKey(e => e.SupplierId);
            });

            // PurchaseInvoiceItem Configuration
            modelBuilder.Entity<PurchaseInvoiceItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Quantity).HasColumnType("decimal(18,2)");
                entity.Property(e => e.UnitPrice).HasColumnType("decimal(18,2)");

                entity.HasOne(e => e.PurchaseInvoice)
                      .WithMany(p => p.Items)
                      .HasForeignKey(e => e.PurchaseInvoiceId);
                entity.HasOne(e => e.Product)
                      .WithMany(p => p.PurchaseInvoiceItems)
                      .HasForeignKey(e => e.ProductId);
            });

            // Expense Configuration
            modelBuilder.Entity<Expense>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Description).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Category).IsRequired().HasMaxLength(50);
            });

            // Receipt Configuration
            modelBuilder.Entity<Receipt>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ReceiptNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");
                entity.HasIndex(e => e.ReceiptNumber).IsUnique();
                entity.HasOne(e => e.Customer)
                      .WithMany(c => c.Receipts)
                      .HasForeignKey(e => e.CustomerId);
            });

            // Payment Configuration
            modelBuilder.Entity<Payment>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.PaymentNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");
                entity.HasIndex(e => e.PaymentNumber).IsUnique();
                entity.HasOne(e => e.Supplier)
                      .WithMany(s => s.Payments)
                      .HasForeignKey(e => e.SupplierId);
            });

            // CuttingOperation Configuration
            modelBuilder.Entity<CuttingOperation>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.OriginalQuantity).HasColumnType("decimal(18,2)");
                entity.Property(e => e.UsedQuantity).HasColumnType("decimal(18,2)");
                entity.Property(e => e.WasteQuantity).HasColumnType("decimal(18,2)");

                entity.HasOne(e => e.Product)
                      .WithMany(p => p.CuttingOperations)
                      .HasForeignKey(e => e.ProductId);
            });
        }
    }
}
