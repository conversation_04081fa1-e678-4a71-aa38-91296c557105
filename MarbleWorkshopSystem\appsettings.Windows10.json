{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore.Database.Command": "Warning"}, "Console": {"IncludeScopes": false, "LogLevel": {"Default": "Information"}}, "EventLog": {"LogLevel": {"Default": "Warning"}}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Data Source=MarbleWorkshop.db;Cache=Shared"}, "Kestrel": {"Endpoints": {"Http": {"Url": "http://localhost:5000"}}, "Limits": {"MaxConcurrentConnections": 100, "MaxConcurrentUpgradedConnections": 100, "MaxRequestBodySize": 52428800, "KeepAliveTimeout": "00:02:00", "RequestHeadersTimeout": "00:00:30"}}, "Windows10Settings": {"EnableWindowsAuthentication": false, "UseWindowsEventLog": true, "AutoStartBrowser": true, "MinimizeToTray": false, "CheckForUpdates": false, "DataBackupEnabled": true, "BackupIntervalHours": 24, "MaxBackupFiles": 7, "TempDirectory": "%TEMP%\\MarbleWorkshop", "LogDirectory": "%LOCALAPPDATA%\\MarbleWorkshop\\Logs", "BackupDirectory": "%USERPROFILE%\\Documents\\MarbleWorkshop\\Backups"}, "Performance": {"EnableResponseCaching": true, "EnableResponseCompression": true, "EnableMemoryCache": true, "CacheExpirationMinutes": 30, "MaxMemoryCacheSizeMB": 100}, "Security": {"RequireHttps": false, "EnableAntiforgery": true, "SessionTimeoutMinutes": 60, "MaxLoginAttempts": 5, "LockoutDurationMinutes": 15}, "Database": {"EnableAutoMigration": true, "EnableSeedData": true, "ConnectionTimeout": 30, "CommandTimeout": 60, "EnableRetryOnFailure": true, "MaxRetryCount": 3}, "UI": {"Theme": "default", "Language": "ar", "DateFormat": "dd/MM/yyyy", "TimeFormat": "HH:mm", "CurrencySymbol": "ج.م", "DecimalPlaces": 2, "PageSize": 20}}