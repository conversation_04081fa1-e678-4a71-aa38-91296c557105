using MarbleWorkshopSystem.Models;

namespace MarbleWorkshopSystem.Services
{
    public interface IPurchaseService
    {
        Task<IEnumerable<PurchaseInvoice>> GetAllPurchaseInvoicesAsync();
        Task<PurchaseInvoice?> GetPurchaseInvoiceByIdAsync(int id);
        Task<IEnumerable<PurchaseInvoice>> GetPurchaseInvoicesBySupplierAsync(int supplierId);
        Task<IEnumerable<PurchaseInvoice>> GetPurchaseInvoicesByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<PurchaseInvoice>> GetPurchaseInvoicesByStatusAsync(string status);
        Task<PurchaseInvoice> CreatePurchaseInvoiceAsync(PurchaseInvoice invoice);
        Task<PurchaseInvoice> UpdatePurchaseInvoiceAsync(PurchaseInvoice invoice);
        Task DeletePurchaseInvoiceAsync(int id);
        Task<PurchaseInvoice> ConfirmPurchaseInvoiceAsync(int id);
        Task<PurchaseInvoice> CancelPurchaseInvoiceAsync(int id);
        Task<string> GenerateInvoiceNumberAsync();
        Task<decimal> CalculateInvoiceTotalAsync(PurchaseInvoice invoice);
        Task<IEnumerable<PurchaseInvoice>> SearchInvoicesAsync(string searchTerm);
        Task<decimal> GetTotalPurchasesAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<int> GetInvoiceCountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<decimal> GetAveragePurchaseAsync(DateTime? fromDate = null, DateTime? toDate = null);
    }
}
