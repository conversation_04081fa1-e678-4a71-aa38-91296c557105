@model MarbleWorkshopSystem.ViewModels.SalesInvoiceViewModel

@{
    ViewData["Title"] = "فاتورة مبيعات جديدة";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-plus me-2"></i>
                        فاتورة مبيعات جديدة
                    </h3>
                </div>

                <div class="card-body">
                    <form asp-action="Create" method="post" id="salesForm">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <!-- حقول مخفية للحسابات -->
                        <input type="hidden" asp-for="Invoice.SubTotal" />
                        <input type="hidden" asp-for="Invoice.TotalAmount" />

                        <!-- معلومات الفاتورة الأساسية -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-info-circle me-2"></i>
                                            معلومات الفاتورة
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="Invoice.InvoiceNumber" class="form-label">
                                                <i class="fas fa-hashtag me-1"></i>
                                                رقم الفاتورة <span class="text-danger">*</span>
                                            </label>
                                            <input asp-for="Invoice.InvoiceNumber" class="form-control"
                                                   placeholder="سيتم إنشاؤه تلقائياً" readonly>
                                            <span asp-validation-for="Invoice.InvoiceNumber" class="text-danger"></span>
                                        </div>
                                        <div class="mb-3">
                                            <label asp-for="Invoice.InvoiceDate" class="form-label">
                                                <i class="fas fa-calendar me-1"></i>
                                                تاريخ الفاتورة <span class="text-danger">*</span>
                                            </label>
                                            <input asp-for="Invoice.InvoiceDate" class="form-control" type="date" required>
                                            <span asp-validation-for="Invoice.InvoiceDate" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-user me-2"></i>
                                            معلومات العميل
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="Invoice.CustomerId" class="form-label">
                                                <i class="fas fa-users me-1"></i>
                                                العميل <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <select asp-for="Invoice.CustomerId" class="form-select" id="customerSelect"
                                                        onchange="updateCustomerInfo()" required>
                                                    <option value="">اختر العميل</option>
                                                    @foreach (var customer in Model.Customers)
                                                    {
                                                        <option value="@customer.Id"
                                                                data-name="@customer.Name"
                                                                data-phone="@customer.Phone"
                                                                data-address="@customer.Address"
                                                                data-balance="@customer.Balance">
                                                            @customer.Name
                                                        </option>
                                                    }
                                                </select>
                                                <a asp-controller="Customers" asp-action="Create"
                                                   class="btn btn-outline-success" title="إضافة عميل جديد" target="_blank">
                                                    <i class="fas fa-plus"></i>
                                                </a>
                                            </div>
                                            <span asp-validation-for="Invoice.CustomerId" class="text-danger"></span>
                                        </div>

                                        <!-- معلومات العميل المختار -->
                                        <div id="customerInfo" class="card bg-light" style="display: none;">
                                            <div class="card-body p-3">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <p class="mb-1"><strong>الاسم:</strong> <span id="customerName"></span></p>
                                                        <p class="mb-0"><strong>الهاتف:</strong> <span id="customerPhone"></span></p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <p class="mb-1"><strong>العنوان:</strong> <span id="customerAddress"></span></p>
                                                        <p class="mb-0"><strong>الرصيد:</strong>
                                                            <span id="customerBalance" class="badge"></span>
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أصناف الفاتورة -->
                        <div class="card mb-4 border-warning">
                            <div class="card-header bg-warning text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    أصناف الفاتورة
                                </h5>
                                <div>
                                    <a asp-controller="Products" asp-action="Create"
                                       class="btn btn-sm btn-light me-2" title="إضافة منتج جديد" target="_blank">
                                        <i class="fas fa-box me-1"></i>
                                        منتج جديد
                                    </a>
                                    <button type="button" class="btn btn-sm btn-light" onclick="addInvoiceItem()">
                                        <i class="fas fa-plus me-1"></i>
                                        إضافة صنف
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover" id="itemsTable">
                                        <thead class="table-dark">
                                            <tr>
                                                <th style="width: 20%">
                                                    <i class="fas fa-cube me-1"></i>
                                                    المنتج
                                                </th>
                                                <th style="width: 10%">
                                                    <i class="fas fa-arrows-alt-h me-1"></i>
                                                    الطول (م)
                                                </th>
                                                <th style="width: 10%">
                                                    <i class="fas fa-arrows-alt-v me-1"></i>
                                                    العرض (م)
                                                </th>
                                                <th style="width: 10%">
                                                    <i class="fas fa-calculator me-1"></i>
                                                    الكمية (م²)
                                                </th>
                                                <th style="width: 12%">
                                                    <i class="fas fa-tag me-1"></i>
                                                    سعر الوحدة
                                                </th>
                                                <th style="width: 12%">
                                                    <i class="fas fa-money-bill me-1"></i>
                                                    المجموع
                                                </th>
                                                <th style="width: 11%">
                                                    <i class="fas fa-warehouse me-1"></i>
                                                    المخزون المتاح
                                                </th>
                                                <th style="width: 15%">
                                                    <i class="fas fa-cogs me-1"></i>
                                                    إجراءات
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody id="itemsTableBody">
                                            <!-- سيتم إضافة الأصناف هنا ديناميكياً -->
                                        </tbody>
                                        <tfoot class="table-light">
                                            <tr>
                                                <td colspan="5" class="text-end"><strong>المجموع الفرعي:</strong></td>
                                                <td><strong id="tableSubTotal">0.00 جنيه</strong></td>
                                                <td colspan="2"></td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>

                                <!-- رسالة عدم وجود أصناف -->
                                <div id="noItemsMessage" class="text-center py-4" style="display: none;">
                                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد أصناف في الفاتورة</h5>
                                    <p class="text-muted">ابدأ بإضافة أصناف للفاتورة</p>
                                    <button type="button" class="btn btn-primary" onclick="addInvoiceItem()">
                                        <i class="fas fa-plus me-1"></i>
                                        إضافة أول صنف
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- الخصومات والمجاميع -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-percent me-2"></i>
                                            الخصومات
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="Invoice.DiscountType" class="form-label">
                                                <i class="fas fa-tags me-1"></i>
                                                نوع الخصم
                                            </label>
                                            <select asp-for="Invoice.DiscountType" class="form-select" onchange="calculateTotals()">
                                                <option value="0">لا يوجد خصم</option>
                                                <option value="1">نسبة مئوية (%)</option>
                                                <option value="2">مبلغ ثابت (جنيه)</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label asp-for="Invoice.DiscountValue" class="form-label">
                                                <i class="fas fa-calculator me-1"></i>
                                                قيمة الخصم
                                            </label>
                                            <div class="input-group">
                                                <input asp-for="Invoice.DiscountValue" class="form-control" type="number"
                                                       step="0.01" min="0" onchange="calculateTotals()" placeholder="0.00">
                                                <span class="input-group-text" id="discountUnit">جنيه</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-receipt me-2"></i>
                                            ملخص الفاتورة
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span><i class="fas fa-list me-1"></i>عدد الأصناف:</span>
                                                    <span id="itemsCount" class="badge bg-primary">0</span>
                                                </div>
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span><i class="fas fa-cubes me-1"></i>إجمالي الكمية:</span>
                                                    <span id="totalQuantity">0.00 م²</span>
                                                </div>
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span><i class="fas fa-calculator me-1"></i>المجموع الفرعي:</span>
                                                    <span id="subTotal">0.00 جنيه</span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span><i class="fas fa-minus-circle me-1"></i>الخصم:</span>
                                                    <span id="discountAmount" class="text-danger">0.00 جنيه</span>
                                                </div>
                                                <hr>
                                                <div class="d-flex justify-content-between">
                                                    <strong><i class="fas fa-money-bill-wave me-1"></i>المجموع النهائي:</strong>
                                                    <strong id="totalAmount" class="text-success fs-5">0.00 جنيه</strong>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- تحذير المخزون -->
                                        <div id="stockWarning" class="alert alert-warning mt-3" style="display: none;">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <strong>تحذير:</strong> بعض الأصناف تتجاوز المخزون المتاح!
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- الملاحظات والحالة -->
                        <div class="row mt-4">
                            <div class="col-md-8">
                                <div class="card border-secondary">
                                    <div class="card-header bg-secondary text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-sticky-note me-2"></i>
                                            ملاحظات إضافية
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="Invoice.Notes" class="form-label">
                                                <i class="fas fa-comment me-1"></i>
                                                ملاحظات
                                            </label>
                                            <textarea asp-for="Invoice.Notes" class="form-control" rows="3"
                                                      placeholder="أدخل أي ملاحظات إضافية حول الفاتورة (اختياري)"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-flag me-2"></i>
                                            حالة الفاتورة
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="Invoice.Status" class="form-label">
                                                <i class="fas fa-info-circle me-1"></i>
                                                الحالة
                                            </label>
                                            <select asp-for="Invoice.Status" class="form-select">
                                                <option value="مسودة">مسودة</option>
                                                <option value="مؤكدة">مؤكدة</option>
                                                <option value="ملغية">ملغية</option>
                                            </select>
                                        </div>
                                        <div class="alert alert-info">
                                            <small>
                                                <i class="fas fa-info-circle me-1"></i>
                                                الفواتير المؤكدة تؤثر على المخزون والحسابات
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <a asp-action="Index" class="btn btn-secondary">
                                            <i class="fas fa-arrow-left me-1"></i>
                                            العودة للقائمة
                                        </a>
                                        <button type="button" class="btn btn-info" onclick="previewInvoice()">
                                            <i class="fas fa-eye me-1"></i>
                                            معاينة
                                        </button>
                                    </div>
                                    <div>
                                        <button type="button" class="btn btn-warning" onclick="saveDraft()">
                                            <i class="fas fa-file-alt me-1"></i>
                                            حفظ كمسودة
                                        </button>
                                        <button type="submit" class="btn btn-success" onclick="return validateInvoice()">
                                            <i class="fas fa-check me-1"></i>
                                            حفظ وتأكيد
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة عميل جديد -->
<div class="modal fade" id="addCustomerModal" tabindex="-1" aria-labelledby="addCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCustomerModalLabel">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة عميل جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addCustomerForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم العميل *</label>
                                <input type="text" class="form-control" id="customerName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" id="customerPhone">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="customerEmail">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الرقم الضريبي</label>
                                <input type="text" class="form-control" id="customerTaxNumber">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">العنوان</label>
                        <textarea class="form-control" id="customerAddress" rows="2"></textarea>
                    </div>

                    <!-- الرصيد الافتتاحي -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الرصيد الافتتاحي</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="customerOpeningBalance" step="0.01" min="0" value="0">
                                    <span class="input-group-text">ج.م</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع الرصيد</label>
                                <select class="form-select" id="customerOpeningBalanceType">
                                    <option value="1">مدين (له)</option>
                                    <option value="2">دائن (عليه)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="saveNewCustomer()">
                    <i class="fas fa-save me-1"></i>
                    حفظ العميل
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة منتج جديد -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProductModalLabel">
                    <i class="fas fa-box me-2"></i>
                    إضافة منتج جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addProductForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم المنتج *</label>
                                <input type="text" class="form-control" id="productName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الفئة *</label>
                                <select class="form-select" id="productCategory" required>
                                    <option value="">اختر الفئة</option>
                                    <option value="رخام">رخام</option>
                                    <option value="جرانيت">جرانيت</option>
                                    <option value="بورسلين">بورسلين</option>
                                    <option value="سيراميك">سيراميك</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">الطول (م)</label>
                                <input type="number" class="form-control" id="productLength" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">العرض (م)</label>
                                <input type="number" class="form-control" id="productWidth" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">سعر الوحدة *</label>
                                <input type="number" class="form-control" id="productUnitPrice" step="0.01" min="0" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" id="productDescription" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="saveNewProduct()">
                    <i class="fas fa-save me-1"></i>
                    حفظ المنتج
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        let itemIndex = 0;
        const products = @Html.Raw(Json.Serialize(Model.Products.Select(p => new {
            Id = p.Id,
            Name = p.Name ?? "",
            UnitPrice = p.UnitPrice,
            Category = p.Category ?? "",
            Description = p.Description ?? "",
            Length = p.Length,
            Width = p.Width,
            Area = p.Area
        })));

        // طباعة البيانات للتأكد من وصولها
        console.log('Products data:', products);

        // التحقق من وجود منتجات
        if (!products || products.length === 0) {
            console.warn('لا توجد منتجات متاحة');
            // إظهار رسالة للمستخدم
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-warning alert-dismissible fade show';
            alertDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه:</strong> لا توجد منتجات متاحة. يرجى إضافة منتجات أولاً من
                <a href="/Products/Create" class="alert-link">صفحة المنتجات</a>.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.querySelector('.card-body').insertBefore(alertDiv, document.querySelector('.card-body').firstChild);
        }

        // تحديث معلومات العميل المختار
        function updateCustomerInfo() {
            const select = document.getElementById('customerSelect');
            const selectedOption = select.options[select.selectedIndex];
            const customerInfo = document.getElementById('customerInfo');

            if (selectedOption.value) {
                document.getElementById('customerName').textContent = selectedOption.dataset.name || 'غير محدد';
                document.getElementById('customerPhone').textContent = selectedOption.dataset.phone || 'غير محدد';
                document.getElementById('customerAddress').textContent = selectedOption.dataset.address || 'غير محدد';

                const balance = parseFloat(selectedOption.dataset.balance) || 0;
                const balanceSpan = document.getElementById('customerBalance');
                balanceSpan.textContent = balance.toLocaleString('ar-EG', {style: 'currency', currency: 'EGP'});
                balanceSpan.className = balance > 0 ? 'badge bg-success' : balance < 0 ? 'badge bg-danger' : 'badge bg-secondary';

                customerInfo.style.display = 'block';
            } else {
                customerInfo.style.display = 'none';
            }
        }

        // تحديث وحدة الخصم
        function updateDiscountUnit() {
            const discountType = document.querySelector('[name="Invoice.DiscountType"]').value;
            const discountUnit = document.getElementById('discountUnit');

            if (discountType === '1') {
                discountUnit.textContent = '%';
            } else {
                discountUnit.textContent = 'جنيه';
            }
        }

        // إضافة مستمع لتغيير نوع الخصم
        document.addEventListener('DOMContentLoaded', function() {
            const discountTypeSelect = document.querySelector('[name="Invoice.DiscountType"]');
            discountTypeSelect.addEventListener('change', function() {
                updateDiscountUnit();
                calculateTotals();
            });

            // تعيين التاريخ الحالي
            const today = new Date().toISOString().split('T')[0];
            document.querySelector('[name="Invoice.InvoiceDate"]').value = today;

            // إضافة صنف واحد افتراضياً
            addInvoiceItem();

            // إظهار رسالة عدم وجود أصناف
            updateItemsDisplay();
        });

        function addInvoiceItem() {
            const tbody = document.getElementById('itemsTableBody');
            const row = document.createElement('tr');

            // إنشاء خيارات المنتجات
            let productOptions = '<option value="">اختر المنتج</option>';

            if (products && products.length > 0) {
                productOptions += products.map(p => {
                    const name = p.Name || 'منتج غير محدد';
                    const category = p.Category || 'فئة غير محددة';
                    const price = p.UnitPrice || 0;
                    const description = p.Description || '';
                    return `<option value="${p.Id}"
                        data-price="${price}"
                        data-name="${name}"
                        data-category="${category}"
                        data-description="${description}"
                        data-length="${p.Length || 0}"
                        data-width="${p.Width || 0}"
                        data-area="${p.Area || 0}">${name} - ${category}</option>`;
                }).join('');
            } else {
                productOptions += '<option value="" disabled>لا توجد منتجات متاحة</option>';
            }

            row.innerHTML = `
                <td>
                    <select name="Invoice.Items[${itemIndex}].ProductId" class="form-select product-select"
                            onchange="updateProductInfo(this, ${itemIndex})" required>
                        ${productOptions}
                    </select>
                    <small class="product-info-display text-muted mt-1" style="display: none;"></small>
                </td>
                <td>
                    <div class="input-group input-group-sm">
                        <input name="Invoice.Items[${itemIndex}].Length" type="number" class="form-control length-input"
                               step="0.01" min="0.01" onchange="calculateQuantityFromDimensions(${itemIndex})"
                               placeholder="0.00" required>
                        <span class="input-group-text">م</span>
                    </div>
                </td>
                <td>
                    <div class="input-group input-group-sm">
                        <input name="Invoice.Items[${itemIndex}].Width" type="number" class="form-control width-input"
                               step="0.01" min="0.01" onchange="calculateQuantityFromDimensions(${itemIndex})"
                               placeholder="0.00" required>
                        <span class="input-group-text">م</span>
                    </div>
                </td>
                <td>
                    <div class="input-group input-group-sm">
                        <input name="Invoice.Items[${itemIndex}].Quantity" type="number" class="form-control quantity-input"
                               step="0.01" min="0.01" onchange="calculateItemTotal(${itemIndex})" readonly
                               style="background-color: #e9ecef;" placeholder="0.00">
                        <span class="input-group-text">م²</span>
                    </div>
                </td>
                <td>
                    <div class="input-group input-group-sm">
                        <input name="Invoice.Items[${itemIndex}].UnitPrice" type="number" class="form-control price-input"
                               step="0.01" min="0.01" onchange="calculateItemTotal(${itemIndex})"
                               placeholder="0.00" required>
                        <span class="input-group-text">ج.م</span>
                    </div>
                </td>
                <td>
                    <strong class="item-total text-success">0.00 جنيه</strong>
                    <input name="Invoice.Items[${itemIndex}].TotalPrice" type="hidden" class="total-price-hidden">
                </td>
                <td>
                    <span class="available-stock badge bg-info">-</span>
                    <div class="stock-warning text-danger small" style="display: none;">
                        <i class="fas fa-exclamation-triangle"></i> تجاوز المخزون
                    </div>
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-info" onclick="showItemDetails(${itemIndex})" title="تفاصيل">
                            <i class="fas fa-info"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeInvoiceItem(this)" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
            itemIndex++;
            updateItemsDisplay();
        }

        function removeInvoiceItem(button) {
            if (confirm('هل أنت متأكد من حذف هذا الصنف؟')) {
                button.closest('tr').remove();
                calculateTotals();
                updateItemsDisplay();
            }
        }

        function updateItemsDisplay() {
            const tbody = document.getElementById('itemsTableBody');
            const noItemsMessage = document.getElementById('noItemsMessage');
            const itemsTable = document.getElementById('itemsTable');

            if (tbody.children.length === 0) {
                noItemsMessage.style.display = 'block';
                itemsTable.style.display = 'none';
            } else {
                noItemsMessage.style.display = 'none';
                itemsTable.style.display = 'table';
            }
        }

        async function updateProductInfo(select, index) {
            const productId = select.value;
            const row = select.closest('tr');
            const priceInput = row.querySelector('.price-input');
            const stockSpan = row.querySelector('.available-stock');
            const productInfoSpan = row.querySelector('.product-info-display');
            const stockWarning = row.querySelector('.stock-warning');

            if (productId) {
                try {
                    // استخدام بيانات المنتج من المصفوفة المحلية أولاً
                    const selectedOption = select.options[select.selectedIndex];
                    const productPrice = parseFloat(selectedOption.dataset.price) || 0;
                    const productName = selectedOption.dataset.name || 'منتج غير محدد';
                    const productCategory = selectedOption.dataset.category || 'فئة غير محددة';
                    const productDescription = selectedOption.dataset.description || '';

                    priceInput.value = productPrice.toFixed(2);

                    // عرض معلومات المنتج
                    if (productInfoSpan) {
                        productInfoSpan.innerHTML = `
                            <i class="fas fa-cube me-1 text-primary"></i>
                            <strong>${productName}</strong> - ${productCategory}
                            ${productDescription ? `<br><small class="text-muted">${productDescription}</small>` : ''}
                        `;
                        productInfoSpan.style.display = 'block';
                    }

                    // جلب معلومات المخزون من الخادم
                    const response = await fetch(`/Inventory/GetAvailableStock?productId=${productId}`);
                    if (response.ok) {
                        const data = await response.json();
                        stockSpan.textContent = data.availableStock.toFixed(2);
                        stockSpan.className = data.availableStock > 0 ? 'badge bg-success' : 'badge bg-danger';

                        // إخفاء تحذير المخزون
                        stockWarning.style.display = 'none';
                    } else {
                        stockSpan.textContent = 'غير متاح';
                        stockSpan.className = 'badge bg-secondary';
                    }

                    calculateItemTotal(index);
                } catch (error) {
                    console.error('Error fetching product info:', error);
                    stockSpan.textContent = 'خطأ';
                    stockSpan.className = 'badge bg-danger';
                }
            } else {
                // مسح المعلومات عند عدم اختيار منتج
                priceInput.value = '';
                stockSpan.textContent = '-';
                stockSpan.className = 'badge bg-info';
                productInfoSpan.style.display = 'none';
                stockWarning.style.display = 'none';
            }
        }

        function calculateQuantityFromDimensions(index) {
            const rows = document.querySelectorAll('#itemsTableBody tr');
            const row = rows[index];
            if (row) {
                const length = parseFloat(row.querySelector('.length-input').value) || 0;
                const width = parseFloat(row.querySelector('.width-input').value) || 0;
                const quantity = length * width;

                row.querySelector('.quantity-input').value = quantity.toFixed(2);
                calculateItemTotal(index);
            }
        }

        function calculateItemTotal(index) {
            const rows = document.querySelectorAll('#itemsTableBody tr');
            const row = rows[index];
            if (row) {
                const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
                const price = parseFloat(row.querySelector('.price-input').value) || 0;
                const total = quantity * price;

                row.querySelector('.item-total').textContent = total.toFixed(2) + ' جنيه';
                row.querySelector('.total-price-hidden').value = total.toFixed(2);

                // التحقق من المخزون المتاح
                checkStockAvailability(row, quantity);

                calculateTotals();
            }
        }

        function checkStockAvailability(row, requiredQuantity) {
            const stockSpan = row.querySelector('.available-stock');
            const stockWarning = row.querySelector('.stock-warning');
            const availableStock = parseFloat(stockSpan.textContent) || 0;

            if (requiredQuantity > availableStock && availableStock > 0) {
                stockWarning.style.display = 'block';
                row.style.backgroundColor = '#fff3cd'; // تحذير أصفر خفيف

                // إظهار تحذير عام
                document.getElementById('stockWarning').style.display = 'block';
            } else {
                stockWarning.style.display = 'none';
                row.style.backgroundColor = '';

                // التحقق من وجود تحذيرات أخرى
                const allWarnings = document.querySelectorAll('.stock-warning[style*="block"]');
                if (allWarnings.length === 0) {
                    document.getElementById('stockWarning').style.display = 'none';
                }
            }
        }

        function calculateTotals() {
            let subTotal = 0;
            let totalQuantity = 0;
            let itemsCount = 0;

            document.querySelectorAll('#itemsTableBody tr').forEach(row => {
                const itemTotal = parseFloat(row.querySelector('.item-total').textContent.replace(' جنيه', '')) || 0;
                const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;

                subTotal += itemTotal;
                totalQuantity += quantity;
                itemsCount++;
            });

            const discountType = parseInt(document.querySelector('[name="Invoice.DiscountType"]').value) || 0;
            const discountValue = parseFloat(document.querySelector('[name="Invoice.DiscountValue"]').value) || 0;

            let discountAmount = 0;
            if (discountType === 1) { // نسبة مئوية
                discountAmount = subTotal * (discountValue / 100);
            } else if (discountType === 2) { // مبلغ ثابت
                discountAmount = Math.min(discountValue, subTotal); // لا يمكن أن يكون الخصم أكبر من المجموع
            }

            const totalAmount = Math.max(subTotal - discountAmount, 0); // لا يمكن أن يكون المجموع سالباً

            // تحديث العرض
            document.getElementById('itemsCount').textContent = itemsCount;
            document.getElementById('totalQuantity').textContent = totalQuantity.toFixed(2) + ' م²';
            document.getElementById('subTotal').textContent = subTotal.toFixed(2) + ' جنيه';
            document.getElementById('tableSubTotal').textContent = subTotal.toFixed(2) + ' جنيه';
            document.getElementById('discountAmount').textContent = discountAmount.toFixed(2) + ' جنيه';
            document.getElementById('totalAmount').textContent = totalAmount.toFixed(2) + ' جنيه';

            // تحديث الحقول المخفية للإرسال
            document.querySelector('[name="Invoice.SubTotal"]').value = subTotal.toFixed(2);
            document.querySelector('[name="Invoice.TotalAmount"]').value = totalAmount.toFixed(2);
        }

        // عرض تفاصيل الصنف
        function showItemDetails(index) {
            const rows = document.querySelectorAll('#itemsTableBody tr');
            const row = rows[index];
            const productSelect = row.querySelector('.product-select');
            const selectedOption = productSelect.options[productSelect.selectedIndex];

            if (selectedOption.value) {
                const details = `
                    المنتج: ${selectedOption.dataset.name}
                    الفئة: ${selectedOption.dataset.category}
                    الوصف: ${selectedOption.dataset.description || 'لا يوجد'}
                    السعر: ${selectedOption.dataset.price} جنيه
                `;
                alert(details);
            } else {
                alert('يرجى اختيار منتج أولاً');
            }
        }

        // التحقق من صحة الفاتورة
        function validateInvoice() {
            const customerId = document.querySelector('[name="Invoice.CustomerId"]').value;
            const items = document.querySelectorAll('#itemsTableBody tr');

            if (!customerId) {
                alert('يرجى اختيار العميل');
                return false;
            }

            if (items.length === 0) {
                alert('يرجى إضافة صنف واحد على الأقل');
                return false;
            }

            // التحقق من اكتمال بيانات الأصناف
            let hasIncompleteItems = false;
            items.forEach((row, index) => {
                const productId = row.querySelector('.product-select').value;
                const quantity = row.querySelector('.quantity-input').value;
                const price = row.querySelector('.price-input').value;

                if (!productId || !quantity || !price || quantity <= 0 || price <= 0) {
                    hasIncompleteItems = true;
                }
            });

            if (hasIncompleteItems) {
                alert('يرجى إكمال بيانات جميع الأصناف');
                return false;
            }

            // تأكيد الحفظ
            return confirm('هل أنت متأكد من حفظ وتأكيد الفاتورة؟\nسيتم تحديث المخزون وحسابات العميل.');
        }

        // حفظ كمسودة
        function saveDraft() {
            document.querySelector('[name="Invoice.Status"]').value = 'مسودة';
            document.getElementById('salesForm').submit();
        }

        // معاينة الفاتورة
        function previewInvoice() {
            const customerId = document.querySelector('[name="Invoice.CustomerId"]').value;
            if (!customerId) {
                alert('يرجى اختيار العميل أولاً');
                return;
            }

            // يمكن إضافة نافذة معاينة هنا
            alert('ميزة المعاينة ستكون متاحة قريباً');
        }

        // إضافة صنف واحد افتراضياً
        document.addEventListener('DOMContentLoaded', function() {
            addInvoiceItem();
        });

        // فتح modal إضافة عميل جديد
        function openAddCustomerModal() {
            const modal = new bootstrap.Modal(document.getElementById('addCustomerModal'));
            modal.show();
        }

        // فتح modal إضافة منتج جديد
        function openAddProductModal() {
            const modal = new bootstrap.Modal(document.getElementById('addProductModal'));
            modal.show();
        }

        // حفظ عميل جديد
        async function saveNewCustomer() {
            const form = document.getElementById('addCustomerForm');
            const formData = new FormData();

            formData.append('Name', document.getElementById('customerName').value);
            formData.append('Phone', document.getElementById('customerPhone').value);
            formData.append('Email', document.getElementById('customerEmail').value);
            formData.append('TaxNumber', document.getElementById('customerTaxNumber').value);
            formData.append('Address', document.getElementById('customerAddress').value);
            formData.append('OpeningBalance', document.getElementById('customerOpeningBalance').value);
            formData.append('OpeningBalanceType', document.getElementById('customerOpeningBalanceType').value);
            formData.append('IsActive', true);

            try {
                const response = await fetch('/Customers/Create', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();

                    // إضافة العميل الجديد للقائمة
                    const customerSelect = document.querySelector('[name="Invoice.CustomerId"]');
                    const option = new Option(result.name, result.id);
                    customerSelect.add(option);
                    customerSelect.value = result.id;

                    // إغلاق الـ modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addCustomerModal'));
                    modal.hide();

                    // مسح النموذج
                    form.reset();

                    alert('تم إضافة العميل بنجاح');
                } else {
                    alert('حدث خطأ أثناء إضافة العميل');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('حدث خطأ أثناء إضافة العميل');
            }
        }

        // حفظ منتج جديد
        async function saveNewProduct() {
            const form = document.getElementById('addProductForm');
            const formData = new FormData();

            formData.append('Name', document.getElementById('productName').value);
            formData.append('Category', document.getElementById('productCategory').value);
            formData.append('Length', document.getElementById('productLength').value || 0);
            formData.append('Width', document.getElementById('productWidth').value || 0);
            formData.append('UnitPrice', document.getElementById('productUnitPrice').value);
            formData.append('Description', document.getElementById('productDescription').value);
            formData.append('IsActive', true);

            try {
                const response = await fetch('/Products/Create', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();

                    // إضافة المنتج الجديد لجميع قوائم المنتجات في الجدول
                    const productSelects = document.querySelectorAll('.product-select');
                    productSelects.forEach(select => {
                        const option = new Option(result.name, result.id);
                        select.add(option.cloneNode(true));
                    });

                    // إغلاق الـ modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addProductModal'));
                    modal.hide();

                    // مسح النموذج
                    form.reset();

                    alert('تم إضافة المنتج بنجاح');
                } else {
                    alert('حدث خطأ أثناء إضافة المنتج');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('حدث خطأ أثناء إضافة المنتج');
            }
        }
    </script>
}
