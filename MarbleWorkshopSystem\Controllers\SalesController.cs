using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MarbleWorkshopSystem.Models;
using MarbleWorkshopSystem.Services;
using MarbleWorkshopSystem.ViewModels;

namespace MarbleWorkshopSystem.Controllers
{
    [Authorize]
    public class SalesController : Controller
    {
        private readonly ISalesService _salesService;
        private readonly ICustomerService _customerService;
        private readonly IProductService _productService;
        private readonly IInventoryService _inventoryService;

        public SalesController(
            ISalesService salesService,
            ICustomerService customerService,
            IProductService productService,
            IInventoryService inventoryService)
        {
            _salesService = salesService;
            _customerService = customerService;
            _productService = productService;
            _inventoryService = inventoryService;
        }

        // GET: Sales
        public async Task<IActionResult> Index(string status, DateTime? fromDate, DateTime? toDate, string searchTerm)
        {
            IEnumerable<SalesInvoice> invoices;

            if (!string.IsNullOrEmpty(searchTerm))
            {
                invoices = await _salesService.SearchInvoicesAsync(searchTerm);
            }
            else if (!string.IsNullOrEmpty(status))
            {
                invoices = await _salesService.GetSalesInvoicesByStatusAsync(status);
            }
            else if (fromDate.HasValue && toDate.HasValue)
            {
                invoices = await _salesService.GetSalesInvoicesByDateRangeAsync(fromDate.Value, toDate.Value);
            }
            else
            {
                invoices = await _salesService.GetAllSalesInvoicesAsync();
            }

            ViewBag.Status = status;
            ViewBag.FromDate = fromDate?.ToString("yyyy-MM-dd");
            ViewBag.ToDate = toDate?.ToString("yyyy-MM-dd");
            ViewBag.SearchTerm = searchTerm;

            return View(invoices);
        }

        // GET: Sales/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var invoice = await _salesService.GetSalesInvoiceByIdAsync(id);
            if (invoice == null)
            {
                return NotFound();
            }

            return View(invoice);
        }

        // GET: Sales/Create
        public async Task<IActionResult> Create()
        {
            var viewModel = new SalesInvoiceViewModel
            {
                Invoice = new SalesInvoice
                {
                    InvoiceDate = DateTime.Now,
                    Items = new List<SalesInvoiceItem>()
                },
                Customers = await _customerService.GetAllCustomersAsync(),
                Products = await _productService.GetAllProductsAsync()
            };

            return View(viewModel);
        }

        // POST: Sales/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(SalesInvoiceViewModel viewModel)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    // التحقق من توفر المخزون
                    if (!await _salesService.HasSufficientStockAsync(viewModel.Invoice))
                    {
                        ModelState.AddModelError("", "المخزون غير كافي لبعض المنتجات");
                        await LoadViewModelDataAsync(viewModel);
                        return View(viewModel);
                    }

                    var invoice = await _salesService.CreateSalesInvoiceAsync(viewModel.Invoice);
                    TempData["SuccessMessage"] = "تم إنشاء فاتورة المبيعات بنجاح";
                    return RedirectToAction(nameof(Details), new { id = invoice.Id });
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "حدث خطأ أثناء إنشاء الفاتورة: " + ex.Message);
                }
            }

            await LoadViewModelDataAsync(viewModel);
            return View(viewModel);
        }

        // GET: Sales/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var invoice = await _salesService.GetSalesInvoiceByIdAsync(id);
            if (invoice == null)
            {
                return NotFound();
            }

            if (invoice.Status == "مؤكدة")
            {
                TempData["ErrorMessage"] = "لا يمكن تعديل فاتورة مؤكدة";
                return RedirectToAction(nameof(Details), new { id });
            }

            var viewModel = new SalesInvoiceViewModel
            {
                Invoice = invoice,
                Customers = await _customerService.GetAllCustomersAsync(),
                Products = await _productService.GetAllProductsAsync()
            };

            return View(viewModel);
        }

        // POST: Sales/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, SalesInvoiceViewModel viewModel)
        {
            if (id != viewModel.Invoice.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    await _salesService.UpdateSalesInvoiceAsync(viewModel.Invoice);
                    TempData["SuccessMessage"] = "تم تحديث الفاتورة بنجاح";
                    return RedirectToAction(nameof(Details), new { id });
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "حدث خطأ أثناء تحديث الفاتورة: " + ex.Message);
                }
            }

            await LoadViewModelDataAsync(viewModel);
            return View(viewModel);
        }

        // POST: Sales/Confirm/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Confirm(int id)
        {
            try
            {
                await _salesService.ConfirmSalesInvoiceAsync(id);
                TempData["SuccessMessage"] = "تم تأكيد الفاتورة بنجاح";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء تأكيد الفاتورة: " + ex.Message;
            }

            return RedirectToAction(nameof(Details), new { id });
        }

        // POST: Sales/Cancel/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Cancel(int id)
        {
            try
            {
                await _salesService.CancelSalesInvoiceAsync(id);
                TempData["SuccessMessage"] = "تم إلغاء الفاتورة بنجاح";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء إلغاء الفاتورة: " + ex.Message;
            }

            return RedirectToAction(nameof(Details), new { id });
        }

        // GET: Sales/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var invoice = await _salesService.GetSalesInvoiceByIdAsync(id);
            if (invoice == null)
            {
                return NotFound();
            }

            return View(invoice);
        }

        // POST: Sales/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                await _salesService.DeleteSalesInvoiceAsync(id);
                TempData["SuccessMessage"] = "تم حذف الفاتورة بنجاح";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف الفاتورة: " + ex.Message;
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: Sales/Print/5
        public async Task<IActionResult> Print(int id, string format = "A4")
        {
            var invoice = await _salesService.GetSalesInvoiceByIdAsync(id);
            if (invoice == null)
            {
                return NotFound();
            }

            ViewBag.PrintFormat = format;
            return View(invoice);
        }

        // AJAX: Get Product Price
        [HttpGet]
        public async Task<IActionResult> GetProductPrice(int productId)
        {
            var product = await _productService.GetProductByIdAsync(productId);
            if (product != null)
            {
                var availableStock = await _inventoryService.GetAvailableStockAsync(productId);
                return Json(new { 
                    unitPrice = product.UnitPrice,
                    availableStock = availableStock,
                    productName = product.Name
                });
            }
            
            return Json(new { unitPrice = 0, availableStock = 0, productName = "" });
        }

        private async Task LoadViewModelDataAsync(SalesInvoiceViewModel viewModel)
        {
            viewModel.Customers = await _customerService.GetAllCustomersAsync();
            viewModel.Products = await _productService.GetAllProductsAsync();
        }
    }
}
