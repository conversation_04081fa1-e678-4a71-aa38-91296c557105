using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MarbleWorkshopSystem.Models
{
    public class Product
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم المنتج مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المنتج يجب أن يكون أقل من 100 حرف")]
        public string Name { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "الوصف يجب أن يكون أقل من 500 حرف")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "الفئة مطلوبة")]
        [StringLength(50, ErrorMessage = "الفئة يجب أن تكون أقل من 50 حرف")]
        public string Category { get; set; } = string.Empty;

        [Required(ErrorMessage = "الطول مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "الطول يجب أن يكون أكبر من صفر")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Length { get; set; }

        [Required(ErrorMessage = "العرض مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "العرض يجب أن يكون أكبر من صفر")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Width { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Area { get; set; }

        [Required(ErrorMessage = "سعر الوحدة مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "سعر الوحدة يجب أن يكون أكبر من صفر")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; }

        [StringLength(255)]
        public string? ImagePath { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime UpdatedDate { get; set; } = DateTime.Now;
        public bool IsActive { get; set; } = true;

        // Navigation Properties
        public virtual Inventory? Inventory { get; set; }
        public virtual ICollection<SalesInvoiceItem> SalesInvoiceItems { get; set; } = new List<SalesInvoiceItem>();
        public virtual ICollection<PurchaseInvoiceItem> PurchaseInvoiceItems { get; set; } = new List<PurchaseInvoiceItem>();
        public virtual ICollection<CuttingOperation> CuttingOperations { get; set; } = new List<CuttingOperation>();
    }
}
