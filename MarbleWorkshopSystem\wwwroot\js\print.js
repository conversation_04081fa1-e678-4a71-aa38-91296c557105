// Print System with Multiple Format Support

class PrintManager {
    constructor() {
        this.currentFormat = 'A4';
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadSavedFormat();
    }

    bindEvents() {
        // Print button click
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-print')) {
                e.preventDefault();
                this.print();
            }
        });

        // Format change
        document.addEventListener('change', (e) => {
            if (e.target.name === 'printFormat') {
                this.changeFormat(e.target.value);
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                this.print();
            }
        });
    }

    changeFormat(format) {
        this.currentFormat = format;
        this.applyFormat(format);
        this.saveFormat(format);
    }

    applyFormat(format) {
        const body = document.body;
        
        // Remove existing format classes
        body.classList.remove('print-a4', 'print-a5', 'print-pos');
        
        // Add new format class
        switch (format) {
            case 'A4':
                body.classList.add('print-a4');
                break;
            case 'A5':
                body.classList.add('print-a5');
                break;
            case 'POS':
                body.classList.add('print-pos');
                break;
        }

        // Update print content
        this.updatePrintContent(format);
    }

    updatePrintContent(format) {
        const printContent = document.querySelector('.print-content');
        if (!printContent) return;

        // Adjust content based on format
        switch (format) {
            case 'A4':
                this.setupA4Layout(printContent);
                break;
            case 'A5':
                this.setupA5Layout(printContent);
                break;
            case 'POS':
                this.setupPOSLayout(printContent);
                break;
        }
    }

    setupA4Layout(content) {
        // A4 specific layout adjustments
        const tables = content.querySelectorAll('table');
        tables.forEach(table => {
            table.style.fontSize = '12px';
            table.style.width = '100%';
        });

        const headers = content.querySelectorAll('.company-name');
        headers.forEach(header => {
            header.style.fontSize = '20px';
        });
    }

    setupA5Layout(content) {
        // A5 specific layout adjustments
        const tables = content.querySelectorAll('table');
        tables.forEach(table => {
            table.style.fontSize = '10px';
            table.style.width = '100%';
        });

        const headers = content.querySelectorAll('.company-name');
        headers.forEach(header => {
            header.style.fontSize = '14px';
        });
    }

    setupPOSLayout(content) {
        // POS specific layout adjustments
        const tables = content.querySelectorAll('table');
        tables.forEach(table => {
            table.style.fontSize = '9px';
            table.style.width = '100%';
            table.style.fontFamily = 'Courier New, monospace';
        });

        const headers = content.querySelectorAll('.company-name');
        headers.forEach(header => {
            header.style.fontSize = '12px';
            header.style.fontFamily = 'Courier New, monospace';
        });

        // Convert tables to POS format
        this.convertToPOSFormat(content);
    }

    convertToPOSFormat(content) {
        const itemsTable = content.querySelector('.items-table table');
        if (!itemsTable) return;

        const rows = itemsTable.querySelectorAll('tbody tr');
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length >= 4) {
                // Rearrange for POS format: Item, Qty x Price = Total
                const itemName = cells[0].textContent.trim();
                const quantity = cells[1].textContent.trim();
                const price = cells[2].textContent.trim();
                const total = cells[3].textContent.trim();

                // Create POS style row
                row.innerHTML = `
                    <td colspan="4" style="border: none; padding: 2px;">
                        <div style="display: flex; justify-content: space-between;">
                            <span>${itemName}</span>
                            <span>${total}</span>
                        </div>
                        <div style="font-size: 8px; color: #666;">
                            ${quantity} × ${price}
                        </div>
                    </td>
                `;
            }
        });
    }

    print() {
        // Save current format
        this.applyFormat(this.currentFormat);

        // Add print-specific styles
        document.body.classList.add('printing');

        // Print
        window.print();

        // Remove print-specific styles after printing
        setTimeout(() => {
            document.body.classList.remove('printing');
        }, 1000);
    }

    saveFormat(format) {
        localStorage.setItem('preferredPrintFormat', format);
    }

    loadSavedFormat() {
        const savedFormat = localStorage.getItem('preferredPrintFormat');
        if (savedFormat) {
            this.currentFormat = savedFormat;
            
            // Update radio button
            const radio = document.querySelector(`input[name="printFormat"][value="${savedFormat}"]`);
            if (radio) {
                radio.checked = true;
            }
            
            this.applyFormat(savedFormat);
        }
    }

    // Utility methods
    static formatCurrency(amount, currency = 'EGP') {
        return new Intl.NumberFormat('ar-EG', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 2
        }).format(amount);
    }

    static formatDate(date, format = 'dd/MM/yyyy') {
        const d = new Date(date);
        const day = String(d.getDate()).padStart(2, '0');
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const year = d.getFullYear();

        switch (format) {
            case 'dd/MM/yyyy':
                return `${day}/${month}/${year}`;
            case 'MM/dd/yyyy':
                return `${month}/${day}/${year}`;
            case 'yyyy-MM-dd':
                return `${year}-${month}-${day}`;
            default:
                return `${day}/${month}/${year}`;
        }
    }

    static generateBarcode(text) {
        // Simple barcode representation for POS
        return `||||| ${text} |||||`;
    }
}

// Print Templates
class PrintTemplates {
    static getInvoiceTemplate(invoice, format = 'A4') {
        const template = `
            <div class="print-content print-${format.toLowerCase()}">
                ${this.getHeader(invoice.company, format)}
                ${this.getInvoiceInfo(invoice, format)}
                ${this.getCustomerInfo(invoice.customer, format)}
                ${this.getItemsTable(invoice.items, format)}
                ${this.getTotals(invoice.totals, format)}
                ${this.getFooter(invoice.company, format)}
            </div>
        `;
        return template;
    }

    static getHeader(company, format) {
        return `
            <div class="header">
                ${company.logo ? `<img src="${company.logo}" alt="Logo" class="company-logo">` : ''}
                <div class="company-name">${company.name}</div>
                <div class="company-info">
                    ${company.address ? `<div>${company.address}</div>` : ''}
                    ${company.phone ? `<div>تليفون: ${company.phone}</div>` : ''}
                    ${company.email ? `<div>بريد إلكتروني: ${company.email}</div>` : ''}
                </div>
            </div>
        `;
    }

    static getInvoiceInfo(invoice, format) {
        return `
            <div class="invoice-info">
                <div class="invoice-details">
                    <strong>رقم الفاتورة:</strong> ${invoice.number}<br>
                    <strong>التاريخ:</strong> ${PrintManager.formatDate(invoice.date)}<br>
                    <strong>الحالة:</strong> ${invoice.status}
                </div>
            </div>
        `;
    }

    static getCustomerInfo(customer, format) {
        return `
            <div class="customer-details">
                <strong>العميل:</strong> ${customer.name}<br>
                ${customer.address ? `<strong>العنوان:</strong> ${customer.address}<br>` : ''}
                ${customer.phone ? `<strong>الهاتف:</strong> ${customer.phone}<br>` : ''}
            </div>
        `;
    }

    static getItemsTable(items, format) {
        let tableHTML = `
            <div class="items-table">
                <table>
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        items.forEach(item => {
            tableHTML += `
                <tr class="item-row">
                    <td>${item.name}</td>
                    <td>${item.quantity}</td>
                    <td>${PrintManager.formatCurrency(item.price)}</td>
                    <td>${PrintManager.formatCurrency(item.total)}</td>
                </tr>
            `;
        });

        tableHTML += `
                    </tbody>
                </table>
            </div>
        `;

        return tableHTML;
    }

    static getTotals(totals, format) {
        return `
            <div class="totals">
                <table>
                    <tr>
                        <td>المجموع الفرعي:</td>
                        <td>${PrintManager.formatCurrency(totals.subtotal)}</td>
                    </tr>
                    ${totals.discount > 0 ? `
                    <tr>
                        <td>الخصم:</td>
                        <td>${PrintManager.formatCurrency(totals.discount)}</td>
                    </tr>
                    ` : ''}
                    ${totals.tax > 0 ? `
                    <tr>
                        <td>الضريبة:</td>
                        <td>${PrintManager.formatCurrency(totals.tax)}</td>
                    </tr>
                    ` : ''}
                    <tr class="total-line">
                        <td><strong>المجموع النهائي:</strong></td>
                        <td><strong>${PrintManager.formatCurrency(totals.total)}</strong></td>
                    </tr>
                </table>
            </div>
        `;
    }

    static getFooter(company, format) {
        let footer = `
            <div class="footer">
                <div>شكراً لتعاملكم معنا</div>
                ${company.taxNumber ? `<div>الرقم الضريبي: ${company.taxNumber}</div>` : ''}
        `;

        if (format === 'POS') {
            footer += `
                <div class="barcode">
                    ${PrintManager.generateBarcode(Date.now().toString())}
                </div>
            `;
        }

        footer += `</div>`;
        return footer;
    }
}

// Initialize Print Manager
document.addEventListener('DOMContentLoaded', () => {
    window.printManager = new PrintManager();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { PrintManager, PrintTemplates };
}
