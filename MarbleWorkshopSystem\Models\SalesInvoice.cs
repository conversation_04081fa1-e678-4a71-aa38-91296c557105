using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MarbleWorkshopSystem.Models
{
    public class SalesInvoice
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "رقم الفاتورة مطلوب")]
        [StringLength(50, ErrorMessage = "رقم الفاتورة يجب أن يكون أقل من 50 حرف")]
        public string InvoiceNumber { get; set; } = string.Empty;

        [Required]
        public int CustomerId { get; set; }

        [Required(ErrorMessage = "تاريخ الفاتورة مطلوب")]
        public DateTime InvoiceDate { get; set; } = DateTime.Now;

        [Column(TypeName = "decimal(18,2)")]
        public decimal SubTotal { get; set; }

        [Required(ErrorMessage = "نوع الخصم مطلوب")]
        public DiscountType DiscountType { get; set; } = DiscountType.None;

        [Range(0, double.MaxValue, ErrorMessage = "قيمة الخصم يجب أن تكون أكبر من أو تساوي صفر")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountValue { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "مسودة";

        // Computed Properties
        public decimal DiscountAmount
        {
            get
            {
                return DiscountType switch
                {
                    DiscountType.Percentage => SubTotal * (DiscountValue / 100),
                    DiscountType.FixedAmount => DiscountValue,
                    _ => 0
                };
            }
        }

        public decimal NetAmount => SubTotal - DiscountAmount;

        // Navigation Properties
        [ForeignKey("CustomerId")]
        public virtual Customer Customer { get; set; } = null!;
        public virtual ICollection<SalesInvoiceItem> Items { get; set; } = new List<SalesInvoiceItem>();
    }

    public enum DiscountType
    {
        None = 0,
        Percentage = 1,
        FixedAmount = 2
    }
}
