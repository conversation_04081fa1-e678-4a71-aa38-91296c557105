@model MarbleWorkshopSystem.Models.Customer

@{
    ViewData["Title"] = "تفاصيل العميل";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            تفاصيل العميل: @Model.Name
                        </h4>
                        <div>
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning btn-sm">
                                <i class="fas fa-edit me-1"></i>
                                تعديل
                            </a>
                            <a asp-action="Index" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left me-1"></i>
                                العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <!-- معلومات العميل الأساسية -->
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        المعلومات الأساسية
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td class="fw-bold text-muted">اسم العميل:</td>
                                            <td>@Model.Name</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">رقم الهاتف:</td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(Model.Phone))
                                                {
                                                    <a href="tel:@Model.Phone" class="text-decoration-none">
                                                        <i class="fas fa-phone me-1"></i>
                                                        @Model.Phone
                                                    </a>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">البريد الإلكتروني:</td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(Model.Email))
                                                {
                                                    <a href="mailto:@Model.Email" class="text-decoration-none">
                                                        <i class="fas fa-envelope me-1"></i>
                                                        @Model.Email
                                                    </a>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">العنوان:</td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(Model.Address))
                                                {
                                                    @Model.Address
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">الرقم الضريبي:</td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(Model.TaxNumber))
                                                {
                                                    @Model.TaxNumber
                                                }
                                                else
                                                {
                                                    <span class="text-muted">غير محدد</span>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">تاريخ الإنشاء:</td>
                                            <td>@Model.CreatedDate.ToString("dd/MM/yyyy")</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">الحالة:</td>
                                            <td>
                                                @if (Model.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger">غير نشط</span>
                                                }
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- الرصيد والإحصائيات -->
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-line me-2"></i>
                                        الرصيد والإحصائيات
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-12 mb-3">
                                            <div class="card bg-primary text-white">
                                                <div class="card-body">
                                                    <h3 class="mb-0">@Model.Balance.ToString("N2") ج.م</h3>
                                                    <small>الرصيد الحالي</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-grid gap-2">
                                        <a asp-controller="Customers" asp-action="Statement" asp-route-id="@Model.Id" 
                                           class="btn btn-outline-primary">
                                            <i class="fas fa-file-alt me-1"></i>
                                            كشف حساب العميل
                                        </a>
                                        
                                        <a asp-controller="Sales" asp-action="Create" asp-route-customerId="@Model.Id" 
                                           class="btn btn-outline-success">
                                            <i class="fas fa-plus me-1"></i>
                                            إنشاء فاتورة مبيعات
                                        </a>
                                        
                                        <a asp-controller="Receipts" asp-action="Create" asp-route-customerId="@Model.Id" 
                                           class="btn btn-outline-info">
                                            <i class="fas fa-receipt me-1"></i>
                                            إنشاء سند قبض
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الإجراءات السريعة -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="mb-0">
                                        <i class="fas fa-tools me-2"></i>
                                        الإجراءات السريعة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3 mb-2">
                                            <a asp-controller="Sales" asp-action="Index" asp-route-customerId="@Model.Id" 
                                               class="btn btn-outline-primary w-100">
                                                <i class="fas fa-shopping-cart me-1"></i>
                                                فواتير المبيعات
                                            </a>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <a asp-controller="Receipts" asp-action="Index" asp-route-customerId="@Model.Id" 
                                               class="btn btn-outline-success w-100">
                                                <i class="fas fa-receipt me-1"></i>
                                                سندات القبض
                                            </a>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <button type="button" class="btn btn-outline-warning w-100" onclick="printCustomerInfo()">
                                                <i class="fas fa-print me-1"></i>
                                                طباعة البيانات
                                            </button>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-outline-secondary w-100">
                                                <i class="fas fa-edit me-1"></i>
                                                تعديل البيانات
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function printCustomerInfo() {
            window.print();
        }
    </script>
}
