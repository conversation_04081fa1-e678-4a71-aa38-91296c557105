using System.ComponentModel.DataAnnotations;

namespace MarbleWorkshopSystem.ViewModels
{
    public class SettingsViewModel
    {
        public CompanyInfoViewModel CompanyInfo { get; set; } = new CompanyInfoViewModel();
        public SystemSettingsViewModel SystemSettings { get; set; } = new SystemSettingsViewModel();
        public InventorySettingsViewModel InventorySettings { get; set; } = new InventorySettingsViewModel();
        public InvoiceSettingsViewModel InvoiceSettings { get; set; } = new InvoiceSettingsViewModel();
        public PrintSettingsViewModel PrintSettings { get; set; } = new PrintSettingsViewModel();
    }

    public class CompanyInfoViewModel
    {
        [Required(ErrorMessage = "اسم الشركة مطلوب")]
        [StringLength(200, ErrorMessage = "اسم الشركة يجب أن يكون أقل من 200 حرف")]
        [Display(Name = "اسم الشركة")]
        public string CompanyName { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "العنوان يجب أن يكون أقل من 500 حرف")]
        [Display(Name = "العنوان")]
        public string? Address { get; set; }

        [StringLength(20, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 20 رقم")]
        [Display(Name = "رقم الهاتف")]
        public string? Phone { get; set; }

        [StringLength(20, ErrorMessage = "رقم الموبايل يجب أن يكون أقل من 20 رقم")]
        [Display(Name = "رقم الموبايل")]
        public string? Mobile { get; set; }

        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [StringLength(100, ErrorMessage = "البريد الإلكتروني يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "البريد الإلكتروني")]
        public string? Email { get; set; }

        [StringLength(50, ErrorMessage = "الرقم الضريبي يجب أن يكون أقل من 50 حرف")]
        [Display(Name = "الرقم الضريبي")]
        public string? TaxNumber { get; set; }

        [StringLength(50, ErrorMessage = "السجل التجاري يجب أن يكون أقل من 50 حرف")]
        [Display(Name = "السجل التجاري")]
        public string? CommercialRegister { get; set; }

        [StringLength(1000, ErrorMessage = "الملاحظات يجب أن تكون أقل من 1000 حرف")]
        [Display(Name = "ملاحظات")]
        public string? Notes { get; set; }
    }

    public class SystemSettingsViewModel
    {
        [Required(ErrorMessage = "العملة الافتراضية مطلوبة")]
        [StringLength(50, ErrorMessage = "العملة الافتراضية يجب أن تكون أقل من 50 حرف")]
        [Display(Name = "العملة الافتراضية")]
        public string DefaultCurrency { get; set; } = "جنيه مصري";

        [Required(ErrorMessage = "تنسيق التاريخ مطلوب")]
        [StringLength(20, ErrorMessage = "تنسيق التاريخ يجب أن يكون أقل من 20 حرف")]
        [Display(Name = "تنسيق التاريخ")]
        public string DateFormat { get; set; } = "dd/MM/yyyy";

        [Required(ErrorMessage = "المنطقة الزمنية مطلوبة")]
        [StringLength(50, ErrorMessage = "المنطقة الزمنية يجب أن تكون أقل من 50 حرف")]
        [Display(Name = "المنطقة الزمنية")]
        public string TimeZone { get; set; } = "Africa/Cairo";

        [Required(ErrorMessage = "اللغة مطلوبة")]
        [StringLength(10, ErrorMessage = "اللغة يجب أن تكون أقل من 10 أحرف")]
        [Display(Name = "اللغة")]
        public string Language { get; set; } = "ar";
    }

    public class InventorySettingsViewModel
    {
        [Required(ErrorMessage = "حد المخزون المنخفض مطلوب")]
        [Range(0, 1000, ErrorMessage = "حد المخزون المنخفض يجب أن يكون بين 0 و 1000")]
        [Display(Name = "حد المخزون المنخفض")]
        public decimal LowStockThreshold { get; set; } = 10;

        [Display(Name = "تحديث المخزون تلقائياً")]
        public bool AutoUpdateStock { get; set; } = true;

        [Display(Name = "السماح بالمخزون السالب")]
        public bool AllowNegativeStock { get; set; } = false;
    }

    public class InvoiceSettingsViewModel
    {
        [Required(ErrorMessage = "بادئة رقم الفاتورة مطلوبة")]
        [StringLength(10, ErrorMessage = "بادئة رقم الفاتورة يجب أن تكون أقل من 10 أحرف")]
        [Display(Name = "بادئة رقم الفاتورة")]
        public string InvoicePrefix { get; set; } = "INV";

        [Required(ErrorMessage = "معدل الضريبة الافتراضي مطلوب")]
        [Range(0, 100, ErrorMessage = "معدل الضريبة يجب أن يكون بين 0 و 100")]
        [Display(Name = "معدل الضريبة الافتراضي (%)")]
        public decimal DefaultTaxRate { get; set; } = 14;

        [Required(ErrorMessage = "الخصم الافتراضي مطلوب")]
        [Range(0, 100, ErrorMessage = "الخصم الافتراضي يجب أن يكون بين 0 و 100")]
        [Display(Name = "الخصم الافتراضي (%)")]
        public decimal DefaultDiscount { get; set; } = 0;

        [Display(Name = "طباعة الفاتورة بعد الحفظ")]
        public bool PrintAfterSave { get; set; } = false;
    }

    public class PrintSettingsViewModel
    {
        [Required(ErrorMessage = "تنسيق الطباعة الافتراضي مطلوب")]
        [Display(Name = "تنسيق الطباعة الافتراضي")]
        public string DefaultPrintFormat { get; set; } = "A4";

        [StringLength(100, ErrorMessage = "اسم الطابعة يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "اسم الطابعة")]
        public string? PrinterName { get; set; }

        [Required(ErrorMessage = "حجم الورق مطلوب")]
        [Display(Name = "حجم الورق")]
        public string PaperSize { get; set; } = "A4";
    }
}
