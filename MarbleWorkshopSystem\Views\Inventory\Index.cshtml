@model IEnumerable<MarbleWorkshopSystem.Models.Inventory>

@{
    ViewData["Title"] = "إدارة المخازن";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-warehouse me-2"></i>
                        إدارة المخازن
                    </h3>
                    <div>
                        <a asp-action="LowStock" class="btn btn-warning me-2">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            مخزون منخفض
                        </a>
                        <a asp-action="Alerts" class="btn btn-danger">
                            <i class="fas fa-bell me-1"></i>
                            التنبيهات
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count()</h4>
                                    <p class="mb-0">إجمالي المنتجات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count(i => !i.IsLowStock)</h4>
                                    <p class="mb-0">مخزون جيد</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count(i => i.IsLowStock)</h4>
                                    <p class="mb-0">مخزون منخفض</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Sum(i => i.WasteQuantity).ToString("F2")</h4>
                                    <p class="mb-0">إجمالي الهالك</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Inventory Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>المنتج</th>
                                    <th>الفئة</th>
                                    <th>الكمية المتوفرة</th>
                                    <th>الحد الأدنى</th>
                                    <th>الهالك</th>
                                    <th>المتاح للبيع</th>
                                    <th>الحالة</th>
                                    <th>آخر تحديث</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr class="@(item.IsLowStock ? "table-warning" : "")">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if (!string.IsNullOrEmpty(item.Product.ImagePath))
                                                {
                                                    <img src="@item.Product.ImagePath" alt="@item.Product.Name" 
                                                         class="img-thumbnail me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                                }
                                                <div>
                                                    <strong>@item.Product.Name</strong>
                                                    <br><small class="text-muted">@item.Product.Area.ToString("F2") م²</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">@item.Product.Category</span>
                                        </td>
                                        <td>
                                            <span class="h5 @(item.IsLowStock ? "text-danger" : "text-success")">
                                                @item.Quantity.ToString("F2")
                                            </span>
                                        </td>
                                        <td>
                                            <span class="text-warning">@item.MinimumStock.ToString("F2")</span>
                                        </td>
                                        <td>
                                            <span class="text-danger">@item.WasteQuantity.ToString("F2")</span>
                                        </td>
                                        <td>
                                            <span class="text-info fw-bold">@item.AvailableQuantity.ToString("F2")</span>
                                        </td>
                                        <td>
                                            @if (item.IsLowStock)
                                            {
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                                    منخفض
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>
                                                    جيد
                                                </span>
                                            }
                                        </td>
                                        <td>
                                            @item.LastUpdated.ToString("dd/MM/yyyy HH:mm")
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-success" 
                                                        data-bs-toggle="modal" data-bs-target="#addStockModal"
                                                        data-product-id="@item.ProductId" data-product-name="@item.Product.Name"
                                                        title="إضافة كمية">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-warning" 
                                                        data-bs-toggle="modal" data-bs-target="#reduceStockModal"
                                                        data-product-id="@item.ProductId" data-product-name="@item.Product.Name"
                                                        title="خصم كمية">
                                                    <i class="fas fa-minus"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        data-bs-toggle="modal" data-bs-target="#addWasteModal"
                                                        data-product-id="@item.ProductId" data-product-name="@item.Product.Name"
                                                        title="إضافة هالك">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <a asp-action="Edit" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if (!Model.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-warehouse fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا يوجد مخزون</h5>
                            <p class="text-muted">ابدأ بإضافة منتجات لإدارة المخزون</p>
                            <a asp-controller="Products" asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                إضافة منتج جديد
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Stock Modal -->
<div class="modal fade" id="addStockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة كمية للمخزون</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form asp-action="AddStock" method="post">
                <div class="modal-body">
                    <input type="hidden" name="productId" id="addStockProductId">
                    <div class="mb-3">
                        <label class="form-label">المنتج:</label>
                        <p id="addStockProductName" class="form-control-plaintext fw-bold"></p>
                    </div>
                    <div class="mb-3">
                        <label for="addStockQuantity" class="form-label">الكمية المراد إضافتها:</label>
                        <input type="number" class="form-control" name="quantity" id="addStockQuantity" 
                               step="0.01" min="0.01" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">إضافة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reduce Stock Modal -->
<div class="modal fade" id="reduceStockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">خصم كمية من المخزون</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form asp-action="ReduceStock" method="post">
                <div class="modal-body">
                    <input type="hidden" name="productId" id="reduceStockProductId">
                    <div class="mb-3">
                        <label class="form-label">المنتج:</label>
                        <p id="reduceStockProductName" class="form-control-plaintext fw-bold"></p>
                    </div>
                    <div class="mb-3">
                        <label for="reduceStockQuantity" class="form-label">الكمية المراد خصمها:</label>
                        <input type="number" class="form-control" name="quantity" id="reduceStockQuantity" 
                               step="0.01" min="0.01" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">خصم</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Waste Modal -->
<div class="modal fade" id="addWasteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة هالك</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form asp-action="AddWaste" method="post">
                <div class="modal-body">
                    <input type="hidden" name="productId" id="addWasteProductId">
                    <div class="mb-3">
                        <label class="form-label">المنتج:</label>
                        <p id="addWasteProductName" class="form-control-plaintext fw-bold"></p>
                    </div>
                    <div class="mb-3">
                        <label for="addWasteQuantity" class="form-label">كمية الهالك:</label>
                        <input type="number" class="form-control" name="wasteQuantity" id="addWasteQuantity" 
                               step="0.01" min="0.01" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">إضافة هالك</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // عرض رسائل النجاح والخطأ
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
                toastr.success('@TempData["SuccessMessage"]');
            </text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>
                toastr.error('@TempData["ErrorMessage"]');
            </text>
        }

        // إعداد المودالات
        $('#addStockModal').on('show.bs.modal', function (event) {
            var button = $(event.relatedTarget);
            var productId = button.data('product-id');
            var productName = button.data('product-name');
            
            $('#addStockProductId').val(productId);
            $('#addStockProductName').text(productName);
        });

        $('#reduceStockModal').on('show.bs.modal', function (event) {
            var button = $(event.relatedTarget);
            var productId = button.data('product-id');
            var productName = button.data('product-name');
            
            $('#reduceStockProductId').val(productId);
            $('#reduceStockProductName').text(productName);
        });

        $('#addWasteModal').on('show.bs.modal', function (event) {
            var button = $(event.relatedTarget);
            var productId = button.data('product-id');
            var productName = button.data('product-name');
            
            $('#addWasteProductId').val(productId);
            $('#addWasteProductName').text(productName);
        });
    </script>
}
