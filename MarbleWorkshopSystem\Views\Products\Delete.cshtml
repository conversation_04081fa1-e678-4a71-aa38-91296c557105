@model MarbleWorkshopSystem.Models.Product

@{
    ViewData["Title"] = "حذف المنتج";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        تأكيد حذف المنتج
                    </h3>
                </div>

                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير!</strong> هل أنت متأكد من رغبتك في حذف هذا المنتج؟ هذا الإجراء لا يمكن التراجع عنه.
                    </div>

                    <div class="row">
                        <!-- معلومات المنتج -->
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0">معلومات المنتج المراد حذفه</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>اسم المنتج:</strong> @Model.Name</p>
                                            <p><strong>الفئة:</strong> <span class="badge bg-info">@Model.Category</span></p>
                                            <p><strong>الوصف:</strong> 
                                                @if (!string.IsNullOrEmpty(Model.Description))
                                                {
                                                    @Model.Description
                                                }
                                                else
                                                {
                                                    <span class="text-muted">لا يوجد وصف</span>
                                                }
                                            </p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>الأبعاد:</strong> @Model.Length.ToString("F2") × @Model.Width.ToString("F2") متر</p>
                                            <p><strong>المساحة:</strong> <strong class="text-primary">@Model.Area.ToString("F2") متر مربع</strong></p>
                                            <p><strong>سعر الوحدة:</strong> <strong class="text-success">@Model.UnitPrice.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</strong></p>
                                        </div>
                                    </div>

                                    @if (Model.Inventory != null)
                                    {
                                        <hr>
                                        <div class="row">
                                            <div class="col-12">
                                                <h6 class="text-muted mb-3">معلومات المخزون:</h6>
                                                <div class="row text-center">
                                                    <div class="col-md-4">
                                                        <div class="border rounded p-2">
                                                            <small class="text-muted">الكمية المتوفرة</small>
                                                            <div class="h5 @(Model.Inventory.IsLowStock ? "text-danger" : "text-success")">
                                                                @Model.Inventory.Quantity.ToString("F2")
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="border rounded p-2">
                                                            <small class="text-muted">الهالك</small>
                                                            <div class="h5 text-danger">@Model.Inventory.WasteQuantity.ToString("F2")</div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="border rounded p-2">
                                                            <small class="text-muted">المتاح للبيع</small>
                                                            <div class="h5 text-info">@Model.Inventory.AvailableQuantity.ToString("F2")</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>

                        <!-- صورة المنتج -->
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="card-title mb-0">صورة المنتج</h6>
                                </div>
                                <div class="card-body text-center">
                                    @if (!string.IsNullOrEmpty(Model.ImagePath))
                                    {
                                        <img src="@Model.ImagePath" alt="@Model.Name" 
                                             class="img-fluid rounded shadow" style="max-height: 200px;">
                                    }
                                    else
                                    {
                                        <div class="bg-light d-flex align-items-center justify-content-center rounded" 
                                             style="height: 150px;">
                                            <div class="text-center">
                                                <i class="fas fa-image fa-2x text-muted mb-2"></i>
                                                <p class="text-muted small">لا توجد صورة</p>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تحذيرات إضافية -->
                    @if (Model.Inventory != null && Model.Inventory.Quantity > 0)
                    {
                        <div class="alert alert-danger mt-3" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <strong>تنبيه:</strong> هذا المنتج يحتوي على كمية في المخزون (@Model.Inventory.Quantity.ToString("F2") وحدة). 
                            حذف المنتج سيؤثر على سجلات المخزون.
                        </div>
                    }

                    <!-- أزرار الإجراءات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <div>
                                    <a asp-action="Index" class="btn btn-secondary me-2">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        العودة للقائمة
                                    </a>
                                    <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info">
                                        <i class="fas fa-eye me-1"></i>
                                        عرض التفاصيل
                                    </a>
                                </div>
                                <div>
                                    <form asp-action="Delete" method="post" class="d-inline">
                                        <input type="hidden" asp-for="Id" />
                                        <button type="submit" class="btn btn-danger" 
                                                onclick="return confirm('هل أنت متأكد من رغبتك في حذف هذا المنتج نهائياً؟')">
                                            <i class="fas fa-trash me-1"></i>
                                            تأكيد الحذف
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
