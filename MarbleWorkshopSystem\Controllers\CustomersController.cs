using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MarbleWorkshopSystem.Models;
using MarbleWorkshopSystem.Services;

namespace MarbleWorkshopSystem.Controllers
{
    [Authorize]
    public class CustomersController : Controller
    {
        private readonly ICustomerService _customerService;

        public CustomersController(ICustomerService customerService)
        {
            _customerService = customerService;
        }

        // GET: Customers
        public async Task<IActionResult> Index(string searchTerm, string status, string balance)
        {
            IEnumerable<Customer> customers;

            if (!string.IsNullOrEmpty(searchTerm))
            {
                customers = await _customerService.SearchCustomersAsync(searchTerm);
            }
            else
            {
                customers = await _customerService.GetAllCustomersAsync();
            }

            // تطبيق فلاتر إضافية
            if (!string.IsNullOrEmpty(status))
            {
                bool isActive = status == "active";
                customers = customers.Where(c => c.IsActive == isActive);
            }

            if (!string.IsNullOrEmpty(balance))
            {
                if (balance == "positive")
                    customers = customers.Where(c => c.Balance > 0);
                else if (balance == "negative")
                    customers = customers.Where(c => c.Balance < 0);
            }

            ViewBag.SearchTerm = searchTerm;
            ViewBag.Status = status;
            ViewBag.Balance = balance;
            return View(customers);
        }

        // GET: Customers/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var customer = await _customerService.GetCustomerByIdAsync(id);
            if (customer == null)
            {
                return NotFound();
            }

            // جلب الفواتير والمدفوعات
            ViewBag.Invoices = await _customerService.GetCustomerInvoicesAsync(id);
            ViewBag.Receipts = await _customerService.GetCustomerReceiptsAsync(id);
            ViewBag.TotalSales = await _customerService.GetCustomerTotalSalesAsync(id);
            ViewBag.TotalPayments = await _customerService.GetCustomerTotalPaymentsAsync(id);

            return View(customer);
        }

        // GET: Customers/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: Customers/Create
        [HttpPost]
        public async Task<IActionResult> Create(Customer customer)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    await _customerService.CreateCustomerAsync(customer);

                    // إذا كان الطلب AJAX، إرجاع JSON
                    if (Request.Headers["X-Requested-With"] == "XMLHttpRequest" ||
                        Request.ContentType?.Contains("multipart/form-data") == true)
                    {
                        return Json(new { success = true, id = customer.Id, name = customer.Name });
                    }

                    TempData["SuccessMessage"] = "تم إنشاء العميل بنجاح";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    if (Request.Headers["X-Requested-With"] == "XMLHttpRequest" ||
                        Request.ContentType?.Contains("multipart/form-data") == true)
                    {
                        return Json(new { success = false, error = ex.Message });
                    }

                    ModelState.AddModelError("", "حدث خطأ أثناء إنشاء العميل: " + ex.Message);
                }
            }

            if (Request.Headers["X-Requested-With"] == "XMLHttpRequest" ||
                Request.ContentType?.Contains("multipart/form-data") == true)
            {
                return Json(new { success = false, errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage) });
            }

            return View(customer);
        }

        // GET: Customers/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var customer = await _customerService.GetCustomerByIdAsync(id);
            if (customer == null)
            {
                return NotFound();
            }

            return View(customer);
        }

        // POST: Customers/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Customer customer)
        {
            if (id != customer.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    await _customerService.UpdateCustomerAsync(customer);
                    TempData["SuccessMessage"] = "تم تحديث العميل بنجاح";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "حدث خطأ أثناء تحديث العميل: " + ex.Message);
                }
            }

            return View(customer);
        }

        // GET: Customers/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var customer = await _customerService.GetCustomerByIdAsync(id);
            if (customer == null)
            {
                return NotFound();
            }

            return View(customer);
        }

        // POST: Customers/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                await _customerService.DeleteCustomerAsync(id);
                TempData["SuccessMessage"] = "تم حذف العميل بنجاح";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف العميل: " + ex.Message;
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: Customers/Statement/5
        public async Task<IActionResult> Statement(int id)
        {
            var customer = await _customerService.GetCustomerByIdAsync(id);
            if (customer == null)
            {
                return NotFound();
            }

            ViewBag.Invoices = await _customerService.GetCustomerInvoicesAsync(id);
            ViewBag.Receipts = await _customerService.GetCustomerReceiptsAsync(id);
            ViewBag.TotalSales = await _customerService.GetCustomerTotalSalesAsync(id);
            ViewBag.TotalPayments = await _customerService.GetCustomerTotalPaymentsAsync(id);

            return View(customer);
        }
    }
}
