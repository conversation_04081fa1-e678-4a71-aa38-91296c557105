using MarbleWorkshopSystem.Models;

namespace MarbleWorkshopSystem.Services
{
    public interface ICustomerService
    {
        Task<IEnumerable<Customer>> GetAllCustomersAsync();
        Task<Customer?> GetCustomerByIdAsync(int id);
        Task<IEnumerable<Customer>> SearchCustomersAsync(string searchTerm);
        Task<Customer> CreateCustomerAsync(Customer customer);
        Task<Customer> UpdateCustomerAsync(Customer customer);
        Task DeleteCustomerAsync(int id);
        Task<bool> CustomerExistsAsync(int id);
        Task<decimal> GetCustomerBalanceAsync(int customerId);
        Task UpdateCustomerBalanceAsync(int customerId, decimal amount);
        Task<IEnumerable<SalesInvoice>> GetCustomerInvoicesAsync(int customerId);
        Task<IEnumerable<Receipt>> GetCustomerReceiptsAsync(int customerId);
        Task<decimal> GetCustomerTotalSalesAsync(int customerId);
        Task<decimal> GetCustomerTotalPaymentsAsync(int customerId);
    }
}
