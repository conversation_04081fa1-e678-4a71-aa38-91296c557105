using Microsoft.AspNetCore.Mvc;
using MarbleWorkshopSystem.Services;
using MarbleWorkshopSystem.Models;

namespace MarbleWorkshopSystem.Controllers
{
    public class TestController : Controller
    {
        private readonly IProductService _productService;
        private readonly IInventoryService _inventoryService;
        private readonly ICustomerService _customerService;
        private readonly ISupplierService _supplierService;

        public TestController(
            IProductService productService,
            IInventoryService inventoryService,
            ICustomerService customerService,
            ISupplierService supplierService)
        {
            _productService = productService;
            _inventoryService = inventoryService;
            _customerService = customerService;
            _supplierService = supplierService;
        }

        public async Task<IActionResult> Index()
        {
            var testResults = new List<TestResult>();

            try
            {
                // اختبار خدمة المنتجات
                testResults.Add(await TestProductService());
                
                // اختبار خدمة المخزون
                testResults.Add(await TestInventoryService());
                
                // اختبار خدمة العملاء
                testResults.Add(await TestCustomerService());
                
                // اختبار خدمة الموردين
                testResults.Add(await TestSupplierService());
            }
            catch (Exception ex)
            {
                testResults.Add(new TestResult
                {
                    TestName = "اختبار عام",
                    IsSuccess = false,
                    Message = $"خطأ عام: {ex.Message}",
                    Details = ex.StackTrace
                });
            }

            return View(testResults);
        }

        private async Task<TestResult> TestProductService()
        {
            try
            {
                // اختبار جلب جميع المنتجات
                var products = await _productService.GetAllProductsAsync();
                var productCount = products.Count();

                // اختبار البحث
                var searchResults = await _productService.SearchProductsAsync("رخام");
                var searchCount = searchResults.Count();

                // اختبار الفئات
                var categories = await _productService.GetCategoriesAsync();
                var categoryCount = categories.Count();

                return new TestResult
                {
                    TestName = "خدمة المنتجات",
                    IsSuccess = true,
                    Message = "تم بنجاح",
                    Details = $"عدد المنتجات: {productCount}, نتائج البحث: {searchCount}, عدد الفئات: {categoryCount}"
                };
            }
            catch (Exception ex)
            {
                return new TestResult
                {
                    TestName = "خدمة المنتجات",
                    IsSuccess = false,
                    Message = ex.Message,
                    Details = ex.StackTrace
                };
            }
        }

        private async Task<TestResult> TestInventoryService()
        {
            try
            {
                // اختبار جلب جميع المخزون
                var inventory = await _inventoryService.GetAllInventoryAsync();
                var inventoryCount = inventory.Count();

                // اختبار المخزون المنخفض
                var lowStock = await _inventoryService.GetLowStockItemsAsync();
                var lowStockCount = lowStock.Count();

                // اختبار التنبيهات
                var alerts = await _inventoryService.GetInventoryAlertsAsync();
                var alertCount = alerts.Count();

                return new TestResult
                {
                    TestName = "خدمة المخزون",
                    IsSuccess = true,
                    Message = "تم بنجاح",
                    Details = $"عدد عناصر المخزون: {inventoryCount}, مخزون منخفض: {lowStockCount}, تنبيهات: {alertCount}"
                };
            }
            catch (Exception ex)
            {
                return new TestResult
                {
                    TestName = "خدمة المخزون",
                    IsSuccess = false,
                    Message = ex.Message,
                    Details = ex.StackTrace
                };
            }
        }

        private async Task<TestResult> TestCustomerService()
        {
            try
            {
                // اختبار جلب جميع العملاء
                var customers = await _customerService.GetAllCustomersAsync();
                var customerCount = customers.Count();

                // اختبار البحث
                var searchResults = await _customerService.SearchCustomersAsync("أحمد");
                var searchCount = searchResults.Count();

                return new TestResult
                {
                    TestName = "خدمة العملاء",
                    IsSuccess = true,
                    Message = "تم بنجاح",
                    Details = $"عدد العملاء: {customerCount}, نتائج البحث: {searchCount}"
                };
            }
            catch (Exception ex)
            {
                return new TestResult
                {
                    TestName = "خدمة العملاء",
                    IsSuccess = false,
                    Message = ex.Message,
                    Details = ex.StackTrace
                };
            }
        }

        private async Task<TestResult> TestSupplierService()
        {
            try
            {
                // اختبار جلب جميع الموردين
                var suppliers = await _supplierService.GetAllSuppliersAsync();
                var supplierCount = suppliers.Count();

                // اختبار البحث
                var searchResults = await _supplierService.SearchSuppliersAsync("شركة");
                var searchCount = searchResults.Count();

                return new TestResult
                {
                    TestName = "خدمة الموردين",
                    IsSuccess = true,
                    Message = "تم بنجاح",
                    Details = $"عدد الموردين: {supplierCount}, نتائج البحث: {searchCount}"
                };
            }
            catch (Exception ex)
            {
                return new TestResult
                {
                    TestName = "خدمة الموردين",
                    IsSuccess = false,
                    Message = ex.Message,
                    Details = ex.StackTrace
                };
            }
        }
    }

    public class TestResult
    {
        public string TestName { get; set; } = string.Empty;
        public bool IsSuccess { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? Details { get; set; }
    }
}
