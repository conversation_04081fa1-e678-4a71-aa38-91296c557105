@model MarbleWorkshopSystem.Models.SalesInvoice

@{
    ViewData["Title"] = "طباعة فاتورة المبيعات";
    Layout = null;
    var printFormat = ViewBag.PrintFormat ?? "A4";
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>فاتورة مبيعات - @Model.InvoiceNumber</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="~/css/print.css" rel="stylesheet" />
    <style>
        body { font-family: 'Arial', sans-serif; }
        .print-content { margin: 0 auto; }
        .company-header { text-align: center; margin-bottom: 30px; }
        .invoice-info { margin-bottom: 20px; }
        .customer-info { margin-bottom: 20px; }
        .items-table { margin-bottom: 20px; }
        .totals-section { margin-top: 20px; }
        .signature-section { margin-top: 40px; }
        
        @@media print {
            .no-print { display: none !important; }
            .print-controls { display: none !important; }
        }
    </style>
</head>
<body class="<EMAIL>()">
    <!-- Print Controls -->
    <div class="print-controls no-print text-center mb-3">
        <div class="format-selector">
            <label><input type="radio" name="printFormat" value="A4" @(printFormat == "A4" ? "checked" : "")> A4</label>
            <label><input type="radio" name="printFormat" value="A5" @(printFormat == "A5" ? "checked" : "")> A5</label>
            <label><input type="radio" name="printFormat" value="POS" @(printFormat == "POS" ? "checked" : "")> POS</label>
        </div>
        <button onclick="window.print()" class="btn btn-primary">
            <i class="fas fa-print me-1"></i>
            طباعة
        </button>
        <button onclick="window.close()" class="btn btn-secondary">
            <i class="fas fa-times me-1"></i>
            إغلاق
        </button>
    </div>

    <div class="print-content">
        <!-- Company Header -->
        <div class="company-header">
            <h2 class="company-name">ورشة الرخام المتقدمة</h2>
            <div class="company-info">
                <div>العنوان: شارع الصناعة، المنطقة الصناعية</div>
                <div>تليفون: 0*********0 | بريد إلكتروني: <EMAIL></div>
                <div>الرقم الضريبي: *********</div>
            </div>
            <hr>
            <h3>فاتورة مبيعات</h3>
        </div>

        <!-- Invoice and Customer Info -->
        <div class="row invoice-info">
            <div class="col-md-6">
                <div class="invoice-details">
                    <h6><strong>معلومات الفاتورة</strong></h6>
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td><strong>رقم الفاتورة:</strong></td>
                            <td>@Model.InvoiceNumber</td>
                        </tr>
                        <tr>
                            <td><strong>التاريخ:</strong></td>
                            <td>@Model.InvoiceDate.ToString("dd/MM/yyyy")</td>
                        </tr>
                        <tr>
                            <td><strong>الحالة:</strong></td>
                            <td>@Model.Status</td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="col-md-6">
                <div class="customer-details">
                    <h6><strong>معلومات العميل</strong></h6>
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td><strong>اسم العميل:</strong></td>
                            <td>@Model.Customer.Name</td>
                        </tr>
                        <tr>
                            <td><strong>الهاتف:</strong></td>
                            <td>@Model.Customer.Phone</td>
                        </tr>
                        <tr>
                            <td><strong>العنوان:</strong></td>
                            <td>@Model.Customer.Address</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Items Table -->
        <div class="items-table">
            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>المنتج</th>
                        <th>الطول (م)</th>
                        <th>العرض (م)</th>
                        <th>الكمية (م²)</th>
                        <th>سعر الوحدة</th>
                        <th>المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.Items)
                    {
                        <tr class="item-row">
                            <td>
                                <strong>@item.Product.Name</strong>
                                @if (!string.IsNullOrEmpty(item.Product.Category))
                                {
                                    <br><small class="text-muted">فئة: @item.Product.Category</small>
                                }
                                @if (!string.IsNullOrEmpty(item.Product.Description))
                                {
                                    <br><small class="text-info">@item.Product.Description</small>
                                }
                                @if (item.Product.Length > 0 && item.Product.Width > 0)
                                {
                                    <br><small class="text-secondary">مقاس المنتج: @item.Product.Length × @item.Product.Width</small>
                                }
                            </td>
                            <td class="text-center">
                                @if (item.Length > 0)
                                {
                                    @item.Length.ToString("F2")
                                }
                                else
                                {
                                    <span class="text-muted">-</span>
                                }
                            </td>
                            <td class="text-center">
                                @if (item.Width > 0)
                                {
                                    @item.Width.ToString("F2")
                                }
                                else
                                {
                                    <span class="text-muted">-</span>
                                }
                            </td>
                            <td class="text-center">
                                <strong>@item.Quantity.ToString("F2")</strong>
                                @if (item.Length > 0 && item.Width > 0)
                                {
                                    <br><small>(@item.Length.ToString("F2") × @item.Width.ToString("F2"))</small>
                                }
                            </td>
                            <td class="text-center">@item.UnitPrice.ToString("F2")</td>
                            <td class="text-center"><strong>@item.TotalPrice.ToString("F2")</strong></td>
                        </tr>
                        @if (!string.IsNullOrEmpty(item.Notes))
                        {
                            <tr>
                                <td colspan="6" class="text-muted">
                                    <small><em>ملاحظات: @item.Notes</em></small>
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>

        <!-- Totals Section -->
        <div class="row totals-section">
            <div class="col-md-6 offset-md-6">
                <div class="totals">
                    <table class="table table-borderless">
                        <tr>
                            <td>المجموع الفرعي:</td>
                            <td class="text-end">@Model.SubTotal.ToString("F2") جنيه</td>
                        </tr>
                        @if (Model.DiscountValue > 0)
                        {
                            <tr>
                                <td>
                                    الخصم 
                                    @if (Model.DiscountType == DiscountType.Percentage)
                                    {
                                        <span>(@Model.DiscountValue%)</span>
                                    }
                                    :
                                </td>
                                <td class="text-end">-@Model.DiscountAmount.ToString("F2") جنيه</td>
                            </tr>
                        }
                        <tr class="total-line">
                            <td><strong>المجموع النهائي:</strong></td>
                            <td class="text-end"><strong>@Model.TotalAmount.ToString("F2") جنيه</strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <!-- Notes -->
        @if (!string.IsNullOrEmpty(Model.Notes))
        {
            <div class="row mt-3">
                <div class="col-12">
                    <div class="border p-2">
                        <strong>ملاحظات:</strong> @Model.Notes
                    </div>
                </div>
            </div>
        }

        <!-- Signature Section -->
        <div class="signature-section">
            <div class="row">
                <div class="col-md-4 text-center">
                    <div class="border-top pt-2">
                        <strong>توقيع العميل</strong>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <div class="border-top pt-2">
                        <strong>توقيع المحاسب</strong>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <div class="border-top pt-2">
                        <strong>ختم الشركة</strong>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer text-center mt-4">
            <hr>
            <small>شكراً لتعاملكم معنا - تاريخ الطباعة: @DateTime.Now.ToString("dd/MM/yyyy HH:mm")</small>
            @if (printFormat == "POS")
            {
                <div class="barcode mt-2">
                    <small>||||| @Model.InvoiceNumber |||||</small>
                </div>
            }
        </div>
    </div>

    <script src="~/js/print.js"></script>
    <script>
        // تطبيق تنسيق الطباعة المحدد
        document.addEventListener('DOMContentLoaded', function() {
            const format = '@printFormat';
            if (window.printManager) {
                window.printManager.changeFormat(format);
            }
        });
    </script>
</body>
</html>
