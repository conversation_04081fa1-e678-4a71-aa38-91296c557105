using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MarbleWorkshopSystem.Models
{
    public class Receipt
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "رقم السند مطلوب")]
        [StringLength(50, ErrorMessage = "رقم السند يجب أن يكون أقل من 50 حرف")]
        public string ReceiptNumber { get; set; } = string.Empty;

        [Required]
        public int CustomerId { get; set; }

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Required(ErrorMessage = "تاريخ السند مطلوب")]
        public DateTime ReceiptDate { get; set; } = DateTime.Now;

        [Required(ErrorMessage = "طريقة الدفع مطلوبة")]
        [StringLength(50, ErrorMessage = "طريقة الدفع يجب أن تكون أقل من 50 حرف")]
        public string PaymentMethod { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        // Navigation Properties
        [ForeignKey("CustomerId")]
        public virtual Customer Customer { get; set; } = null!;
    }
}
