using Microsoft.EntityFrameworkCore;
using MarbleWorkshopSystem.Data;
using MarbleWorkshopSystem.Models;

namespace MarbleWorkshopSystem.Services
{
    public class PurchaseService : IPurchaseService
    {
        private readonly ApplicationDbContext _context;
        private readonly IInventoryService _inventoryService;
        private readonly ISupplierService _supplierService;

        public PurchaseService(ApplicationDbContext context, IInventoryService inventoryService, ISupplierService supplierService)
        {
            _context = context;
            _inventoryService = inventoryService;
            _supplierService = supplierService;
        }

        public async Task<IEnumerable<PurchaseInvoice>> GetAllPurchaseInvoicesAsync()
        {
            return await _context.PurchaseInvoices
                .Include(p => p.Supplier)
                .Include(p => p.Items)
                .ThenInclude(i => i.Product)
                .OrderByDescending(p => p.InvoiceDate)
                .ToListAsync();
        }

        public async Task<PurchaseInvoice?> GetPurchaseInvoiceByIdAsync(int id)
        {
            return await _context.PurchaseInvoices
                .Include(p => p.Supplier)
                .Include(p => p.Items)
                .ThenInclude(i => i.Product)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<IEnumerable<PurchaseInvoice>> GetPurchaseInvoicesBySupplierAsync(int supplierId)
        {
            return await _context.PurchaseInvoices
                .Include(p => p.Supplier)
                .Include(p => p.Items)
                .ThenInclude(i => i.Product)
                .Where(p => p.SupplierId == supplierId)
                .OrderByDescending(p => p.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PurchaseInvoice>> GetPurchaseInvoicesByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            return await _context.PurchaseInvoices
                .Include(p => p.Supplier)
                .Include(p => p.Items)
                .ThenInclude(i => i.Product)
                .Where(p => p.InvoiceDate >= fromDate && p.InvoiceDate <= toDate)
                .OrderByDescending(p => p.InvoiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<PurchaseInvoice>> GetPurchaseInvoicesByStatusAsync(string status)
        {
            return await _context.PurchaseInvoices
                .Include(p => p.Supplier)
                .Include(p => p.Items)
                .ThenInclude(i => i.Product)
                .Where(p => p.Status == status)
                .OrderByDescending(p => p.InvoiceDate)
                .ToListAsync();
        }

        public async Task<PurchaseInvoice> CreatePurchaseInvoiceAsync(PurchaseInvoice invoice)
        {
            // توليد رقم الفاتورة
            invoice.InvoiceNumber = await GenerateInvoiceNumberAsync();
            invoice.InvoiceDate = DateTime.Now;
            invoice.Status = "مسودة";

            // حساب المجاميع
            await CalculateInvoiceTotalsAsync(invoice);

            _context.PurchaseInvoices.Add(invoice);
            await _context.SaveChangesAsync();

            return invoice;
        }

        public async Task<PurchaseInvoice> UpdatePurchaseInvoiceAsync(PurchaseInvoice invoice)
        {
            // حساب المجاميع
            await CalculateInvoiceTotalsAsync(invoice);

            _context.PurchaseInvoices.Update(invoice);
            await _context.SaveChangesAsync();

            return invoice;
        }

        public async Task DeletePurchaseInvoiceAsync(int id)
        {
            var invoice = await GetPurchaseInvoiceByIdAsync(id);
            if (invoice != null)
            {
                // إذا كانت الفاتورة مؤكدة، يجب خصم الكميات من المخزون
                if (invoice.Status == "مؤكدة")
                {
                    foreach (var item in invoice.Items)
                    {
                        await _inventoryService.ReduceStockAsync(item.ProductId, item.Quantity);
                    }
                }

                _context.PurchaseInvoices.Remove(invoice);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<PurchaseInvoice> ConfirmPurchaseInvoiceAsync(int id)
        {
            var invoice = await GetPurchaseInvoiceByIdAsync(id);
            if (invoice == null)
                throw new InvalidOperationException("الفاتورة غير موجودة");

            if (invoice.Status == "مؤكدة")
                throw new InvalidOperationException("الفاتورة مؤكدة مسبقاً");

            // إضافة الكميات للمخزون
            foreach (var item in invoice.Items)
            {
                await _inventoryService.AddStockAsync(item.ProductId, item.Quantity);
            }

            // تحديث رصيد المورد
            await _supplierService.UpdateSupplierBalanceAsync(invoice.SupplierId, invoice.TotalAmount);

            invoice.Status = "مؤكدة";
            await UpdatePurchaseInvoiceAsync(invoice);

            return invoice;
        }

        public async Task<PurchaseInvoice> CancelPurchaseInvoiceAsync(int id)
        {
            var invoice = await GetPurchaseInvoiceByIdAsync(id);
            if (invoice == null)
                throw new InvalidOperationException("الفاتورة غير موجودة");

            if (invoice.Status == "ملغية")
                throw new InvalidOperationException("الفاتورة ملغية مسبقاً");

            // إذا كانت مؤكدة، يجب خصم الكميات والرصيد
            if (invoice.Status == "مؤكدة")
            {
                foreach (var item in invoice.Items)
                {
                    await _inventoryService.ReduceStockAsync(item.ProductId, item.Quantity);
                }
                await _supplierService.UpdateSupplierBalanceAsync(invoice.SupplierId, -invoice.TotalAmount);
            }

            invoice.Status = "ملغية";
            await UpdatePurchaseInvoiceAsync(invoice);

            return invoice;
        }

        public async Task<string> GenerateInvoiceNumberAsync()
        {
            var lastInvoice = await _context.PurchaseInvoices
                .OrderByDescending(p => p.Id)
                .FirstOrDefaultAsync();

            var nextNumber = (lastInvoice?.Id ?? 0) + 1;
            return $"PUR-{DateTime.Now:yyyyMM}-{nextNumber:D4}";
        }

        public async Task<decimal> CalculateInvoiceTotalAsync(PurchaseInvoice invoice)
        {
            await CalculateInvoiceTotalsAsync(invoice);
            return invoice.TotalAmount;
        }

        public async Task<IEnumerable<PurchaseInvoice>> SearchInvoicesAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllPurchaseInvoicesAsync();

            return await _context.PurchaseInvoices
                .Include(p => p.Supplier)
                .Include(p => p.Items)
                .ThenInclude(i => i.Product)
                .Where(p => p.InvoiceNumber.Contains(searchTerm) ||
                           p.Supplier.Name.Contains(searchTerm) ||
                           p.Notes!.Contains(searchTerm))
                .OrderByDescending(p => p.InvoiceDate)
                .ToListAsync();
        }

        public async Task<decimal> GetTotalPurchasesAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.PurchaseInvoices.Where(p => p.Status == "مؤكدة");

            if (fromDate.HasValue)
                query = query.Where(p => p.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(p => p.InvoiceDate <= toDate.Value);

            return await query.SumAsync(p => p.TotalAmount);
        }

        public async Task<int> GetInvoiceCountAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.PurchaseInvoices.AsQueryable();

            if (fromDate.HasValue)
                query = query.Where(p => p.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(p => p.InvoiceDate <= toDate.Value);

            return await query.CountAsync();
        }

        public async Task<decimal> GetAveragePurchaseAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.PurchaseInvoices.Where(p => p.Status == "مؤكدة");

            if (fromDate.HasValue)
                query = query.Where(p => p.InvoiceDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(p => p.InvoiceDate <= toDate.Value);

            return await query.AnyAsync() ? await query.AverageAsync(p => p.TotalAmount) : 0;
        }

        private async Task CalculateInvoiceTotalsAsync(PurchaseInvoice invoice)
        {
            // حساب إجمالي كل عنصر
            foreach (var item in invoice.Items)
            {
                // حساب الكمية من الطول والعرض إذا كانت متوفرة
                if (item.Length > 0 && item.Width > 0)
                {
                    item.Quantity = item.Length * item.Width;
                }

                item.TotalPrice = item.Quantity * item.UnitPrice;
            }

            // حساب المجموع الفرعي
            invoice.SubTotal = invoice.Items.Sum(i => i.TotalPrice);

            // حساب قيمة الخصم
            var discountAmount = invoice.DiscountType switch
            {
                DiscountType.Percentage => invoice.SubTotal * (invoice.DiscountValue / 100),
                DiscountType.FixedAmount => invoice.DiscountValue,
                _ => 0
            };

            // حساب المجموع النهائي
            invoice.TotalAmount = invoice.SubTotal - discountAmount;

            await Task.CompletedTask;
        }
    }
}
