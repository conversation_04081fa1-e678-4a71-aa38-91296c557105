using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MarbleWorkshopSystem.Models;
using MarbleWorkshopSystem.Services;

namespace MarbleWorkshopSystem.Controllers
{
    [Authorize]
    public class SuppliersController : Controller
    {
        private readonly ISupplierService _supplierService;

        public SuppliersController(ISupplierService supplierService)
        {
            _supplierService = supplierService;
        }

        // GET: Suppliers
        public async Task<IActionResult> Index(string searchTerm, string status, string balance)
        {
            IEnumerable<Supplier> suppliers;

            if (!string.IsNullOrEmpty(searchTerm))
            {
                suppliers = await _supplierService.SearchSuppliersAsync(searchTerm);
            }
            else
            {
                suppliers = await _supplierService.GetAllSuppliersAsync();
            }

            // تطبيق فلاتر إضافية
            if (!string.IsNullOrEmpty(status))
            {
                bool isActive = status == "active";
                suppliers = suppliers.Where(s => s.IsActive == isActive);
            }

            if (!string.IsNullOrEmpty(balance))
            {
                if (balance == "positive")
                    suppliers = suppliers.Where(s => s.Balance > 0);
                else if (balance == "negative")
                    suppliers = suppliers.Where(s => s.Balance < 0);
            }

            ViewBag.SearchTerm = searchTerm;
            ViewBag.Status = status;
            ViewBag.Balance = balance;
            return View(suppliers);
        }

        // GET: Suppliers/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var supplier = await _supplierService.GetSupplierByIdAsync(id);
            if (supplier == null)
            {
                return NotFound();
            }

            // جلب الفواتير والمدفوعات
            ViewBag.Invoices = await _supplierService.GetSupplierInvoicesAsync(id);
            ViewBag.Payments = await _supplierService.GetSupplierPaymentsAsync(id);
            ViewBag.TotalPurchases = await _supplierService.GetSupplierTotalPurchasesAsync(id);
            ViewBag.TotalPayments = await _supplierService.GetSupplierTotalPaymentsAsync(id);

            return View(supplier);
        }

        // GET: Suppliers/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: Suppliers/Create
        [HttpPost]
        public async Task<IActionResult> Create(Supplier supplier)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    await _supplierService.CreateSupplierAsync(supplier);

                    // إذا كان الطلب AJAX، إرجاع JSON
                    if (Request.Headers["X-Requested-With"] == "XMLHttpRequest" ||
                        Request.ContentType?.Contains("multipart/form-data") == true)
                    {
                        return Json(new { success = true, id = supplier.Id, name = supplier.Name });
                    }

                    TempData["SuccessMessage"] = "تم إنشاء المورد بنجاح";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    if (Request.Headers["X-Requested-With"] == "XMLHttpRequest" ||
                        Request.ContentType?.Contains("multipart/form-data") == true)
                    {
                        return Json(new { success = false, error = ex.Message });
                    }

                    ModelState.AddModelError("", "حدث خطأ أثناء إنشاء المورد: " + ex.Message);
                }
            }

            if (Request.Headers["X-Requested-With"] == "XMLHttpRequest" ||
                Request.ContentType?.Contains("multipart/form-data") == true)
            {
                return Json(new { success = false, errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage) });
            }

            return View(supplier);
        }

        // GET: Suppliers/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var supplier = await _supplierService.GetSupplierByIdAsync(id);
            if (supplier == null)
            {
                return NotFound();
            }

            return View(supplier);
        }

        // POST: Suppliers/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Supplier supplier)
        {
            if (id != supplier.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    await _supplierService.UpdateSupplierAsync(supplier);
                    TempData["SuccessMessage"] = "تم تحديث المورد بنجاح";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "حدث خطأ أثناء تحديث المورد: " + ex.Message);
                }
            }

            return View(supplier);
        }

        // GET: Suppliers/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var supplier = await _supplierService.GetSupplierByIdAsync(id);
            if (supplier == null)
            {
                return NotFound();
            }

            return View(supplier);
        }

        // POST: Suppliers/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                await _supplierService.DeleteSupplierAsync(id);
                TempData["SuccessMessage"] = "تم حذف المورد بنجاح";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف المورد: " + ex.Message;
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: Suppliers/Statement/5
        public async Task<IActionResult> Statement(int id)
        {
            var supplier = await _supplierService.GetSupplierByIdAsync(id);
            if (supplier == null)
            {
                return NotFound();
            }

            ViewBag.Invoices = await _supplierService.GetSupplierInvoicesAsync(id);
            ViewBag.Payments = await _supplierService.GetSupplierPaymentsAsync(id);
            ViewBag.TotalPurchases = await _supplierService.GetSupplierTotalPurchasesAsync(id);
            ViewBag.TotalPayments = await _supplierService.GetSupplierTotalPaymentsAsync(id);

            return View(supplier);
        }
    }
}
