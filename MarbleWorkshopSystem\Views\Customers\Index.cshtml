@model IEnumerable<MarbleWorkshopSystem.Models.Customer>

@{
    ViewData["Title"] = "إدارة العملاء";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>
                        إدارة العملاء
                    </h3>
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        إضافة عميل جديد
                    </a>
                </div>

                <div class="card-body">
                    <!-- Search and Filter Section -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form asp-action="Index" method="get" class="d-flex">
                                <input type="text" name="searchTerm" value="@ViewBag.SearchTerm"
                                       class="form-control me-2" placeholder="البحث في العملاء (الاسم، الهاتف، البريد الإلكتروني)...">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                                @if (!string.IsNullOrEmpty(ViewBag.SearchTerm as string))
                                {
                                    <a asp-action="Index" class="btn btn-outline-secondary ms-2">
                                        <i class="fas fa-times"></i>
                                    </a>
                                }
                            </form>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-info dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="fas fa-filter me-1"></i>
                                    فلترة
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="?status=active">العملاء النشطين</a></li>
                                    <li><a class="dropdown-item" href="?status=inactive">العملاء غير النشطين</a></li>
                                    <li><a class="dropdown-item" href="?balance=positive">رصيد موجب</a></li>
                                    <li><a class="dropdown-item" href="?balance=negative">رصيد سالب</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" asp-action="Index">عرض الكل</a></li>
                                </ul>
                            </div>
                            <a asp-action="Create" class="btn btn-success ms-2">
                                <i class="fas fa-user-plus me-1"></i>
                                عميل جديد
                            </a>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count()</h4>
                                    <small>إجمالي العملاء</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count(c => c.IsActive)</h4>
                                    <small>العملاء النشطين</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count(c => c.Balance > 0)</h4>
                                    <small>رصيد موجب</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count(c => c.Balance < 0)</h4>
                                    <small>رصيد سالب</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Customers Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم العميل</th>
                                    <th>رقم الهاتف</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>العنوان</th>
                                    <th>الرقم الضريبي</th>
                                    <th>الرصيد</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            <strong>@item.Name</strong>
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.Phone))
                                            {
                                                <i class="fas fa-phone me-1"></i>@item.Phone
                                            }
                                            else
                                            {
                                                <span class="text-muted">غير محدد</span>
                                            }
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.Email))
                                            {
                                                <i class="fas fa-envelope me-1"></i>@item.Email
                                            }
                                            else
                                            {
                                                <span class="text-muted">غير محدد</span>
                                            }
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.Address))
                                            {
                                                <i class="fas fa-map-marker-alt me-1"></i>@item.Address
                                            }
                                            else
                                            {
                                                <span class="text-muted">غير محدد</span>
                                            }
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.TaxNumber))
                                            {
                                                @item.TaxNumber
                                            }
                                            else
                                            {
                                                <span class="text-muted">غير محدد</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge @(item.Balance >= 0 ? "bg-success" : "bg-danger") fs-6">
                                                @item.Balance.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                                            </span>
                                            @if (item.OpeningBalance > 0)
                                            {
                                                <br>
                                                <small class="text-muted">
                                                    افتتاحي: @item.OpeningBalance.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                                                    (@(item.OpeningBalanceType == MarbleWorkshopSystem.Models.BalanceType.Debit ? "مدين" : "دائن"))
                                                </small>
                                            }
                                        </td>
                                        <td>
                                            @item.CreatedDate.ToString("dd/MM/yyyy")
                                        </td>
                                        <td>
                                            @if (item.IsActive)
                                            {
                                                <span class="badge bg-success">نشط</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">غير نشط</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Statement" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-primary" title="كشف حساب">
                                                    <i class="fas fa-file-invoice"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a asp-action="Delete" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if (!Model.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا يوجد عملاء</h5>
                            <p class="text-muted">ابدأ بإضافة عملاء جدد لإدارة قاعدة عملائك</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                إضافة أول عميل
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // عرض رسائل النجاح والخطأ
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
                toastr.success('@TempData["SuccessMessage"]');
            </text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>
                toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
    </script>
}
