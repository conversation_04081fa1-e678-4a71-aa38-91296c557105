using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MarbleWorkshopSystem.Models;
using MarbleWorkshopSystem.Services;

namespace MarbleWorkshopSystem.Controllers;

[Authorize]
public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly DashboardService _dashboardService;

    public HomeController(ILogger<HomeController> logger, DashboardService dashboardService)
    {
        _logger = logger;
        _dashboardService = dashboardService;
    }

    public async Task<IActionResult> Index()
    {
        try
        {
            var statistics = await _dashboardService.GetDashboardStatisticsAsync();
            return View(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في تحميل الإحصائيات");
            // إرجاع إحصائيات فارغة في حالة الخطأ
            var emptyStats = new MarbleWorkshopSystem.Services.DashboardStatistics
            {
                TopCustomers = new List<MarbleWorkshopSystem.Services.TopCustomer>(),
                TopProducts = new List<MarbleWorkshopSystem.Services.TopProduct>()
            };
            return View(emptyStats);
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetMonthlySalesData()
    {
        try
        {
            var data = await _dashboardService.GetMonthlySalesDataAsync(12);
            return Json(data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في جلب بيانات المبيعات الشهرية");
            return Json(new List<object>());
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetCategorySalesData()
    {
        try
        {
            var data = await _dashboardService.GetCategorySalesDataAsync();
            return Json(data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "خطأ في جلب بيانات فئات المبيعات");
            return Json(new List<object>());
        }
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
