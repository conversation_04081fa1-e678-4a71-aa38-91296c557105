using MarbleWorkshopSystem.Data;
using MarbleWorkshopSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace MarbleWorkshopSystem.Services
{
    public class DashboardService
    {
        private readonly ApplicationDbContext _context;

        public DashboardService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<DashboardStatistics> GetDashboardStatisticsAsync()
        {
            try
            {
                var today = DateTime.Today;
                var thisMonth = new DateTime(today.Year, today.Month, 1);
                var lastMonth = thisMonth.AddMonths(-1);
                var thisYear = new DateTime(today.Year, 1, 1);

                var stats = new DashboardStatistics();

                // إحصائيات اليوم
                try
                {
                    stats.TodaySales = await _context.SalesInvoices
                        .Where(s => s.InvoiceDate.Date == today && s.Status == "مؤكدة")
                        .SumAsync(s => (decimal?)s.TotalAmount) ?? 0;

                    stats.TodayInvoicesCount = await _context.SalesInvoices
                        .CountAsync(s => s.InvoiceDate.Date == today);
                }
                catch
                {
                    stats.TodaySales = 0;
                    stats.TodayInvoicesCount = 0;
                }

                // إحصائيات الشهر الحالي
                try
                {
                    stats.ThisMonthSales = await _context.SalesInvoices
                        .Where(s => s.InvoiceDate >= thisMonth && s.Status == "مؤكدة")
                        .SumAsync(s => (decimal?)s.TotalAmount) ?? 0;

                    stats.ThisMonthPurchases = await _context.PurchaseInvoices
                        .Where(p => p.InvoiceDate >= thisMonth && p.Status == "مؤكدة")
                        .SumAsync(p => (decimal?)p.TotalAmount) ?? 0;

                    // إحصائيات الشهر الماضي للمقارنة
                    stats.LastMonthSales = await _context.SalesInvoices
                        .Where(s => s.InvoiceDate >= lastMonth && s.InvoiceDate < thisMonth && s.Status == "مؤكدة")
                        .SumAsync(s => (decimal?)s.TotalAmount) ?? 0;

                    // إحصائيات السنة
                    stats.ThisYearSales = await _context.SalesInvoices
                        .Where(s => s.InvoiceDate >= thisYear && s.Status == "مؤكدة")
                        .SumAsync(s => (decimal?)s.TotalAmount) ?? 0;
                }
                catch
                {
                    stats.ThisMonthSales = 0;
                    stats.ThisMonthPurchases = 0;
                    stats.LastMonthSales = 0;
                    stats.ThisYearSales = 0;
                }

                // إحصائيات العملاء والموردين
                try
                {
                    stats.TotalCustomers = await _context.Customers.CountAsync();
                    stats.TotalSuppliers = await _context.Suppliers.CountAsync();
                    stats.TotalProducts = await _context.Products.CountAsync();
                }
                catch
                {
                    stats.TotalCustomers = 0;
                    stats.TotalSuppliers = 0;
                    stats.TotalProducts = 0;
                }

                // إحصائيات المخزون
                try
                {
                    stats.LowStockProducts = await _context.Inventories
                        .Where(i => i.AvailableQuantity <= 10) // حد المخزون المنخفض
                        .CountAsync();

                    stats.TotalInventoryValue = await _context.Inventories
                        .Include(i => i.Product)
                        .SumAsync(i => (decimal?)(i.AvailableQuantity * i.Product.UnitPrice)) ?? 0;
                }
                catch
                {
                    stats.LowStockProducts = 0;
                    stats.TotalInventoryValue = 0;
                }

                // المصروفات
                try
                {
                    stats.ThisMonthExpenses = await _context.Expenses
                        .Where(e => e.ExpenseDate >= thisMonth)
                        .SumAsync(e => (decimal?)e.Amount) ?? 0;
                }
                catch
                {
                    stats.ThisMonthExpenses = 0;
                }

                // الفواتير المعلقة
                try
                {
                    stats.PendingInvoices = await _context.SalesInvoices
                        .CountAsync(s => s.Status == "مسودة");
                }
                catch
                {
                    stats.PendingInvoices = 0;
                }

                // أفضل العملاء (أعلى 5)
                try
                {
                    stats.TopCustomers = await _context.SalesInvoices
                        .Where(s => s.Status == "مؤكدة" && s.InvoiceDate >= thisMonth)
                        .GroupBy(s => new { s.CustomerId, s.Customer.Name })
                        .Select(g => new TopCustomer
                        {
                            CustomerName = g.Key.Name,
                            TotalSales = g.Sum(s => s.TotalAmount),
                            InvoicesCount = g.Count()
                        })
                        .OrderByDescending(c => c.TotalSales)
                        .Take(5)
                        .ToListAsync();
                }
                catch
                {
                    stats.TopCustomers = new List<TopCustomer>();
                }

                // أفضل المنتجات (أعلى 5)
                try
                {
                    stats.TopProducts = await _context.SalesInvoices
                        .Where(s => s.Status == "مؤكدة" && s.InvoiceDate >= thisMonth)
                        .SelectMany(s => s.Items)
                        .GroupBy(i => new { i.ProductId, i.Product.Name })
                        .Select(g => new TopProduct
                        {
                            ProductName = g.Key.Name,
                            TotalQuantity = g.Sum(i => i.Quantity),
                            TotalSales = g.Sum(i => i.TotalPrice)
                        })
                        .OrderByDescending(p => p.TotalSales)
                        .Take(5)
                        .ToListAsync();
                }
                catch
                {
                    stats.TopProducts = new List<TopProduct>();
                }

                // حساب نسبة النمو
                try
                {
                    stats.SalesGrowthPercentage = stats.LastMonthSales > 0
                        ? ((stats.ThisMonthSales - stats.LastMonthSales) / stats.LastMonthSales) * 100
                        : 0;
                }
                catch
                {
                    stats.SalesGrowthPercentage = 0;
                }

                // حساب صافي الربح (تقريبي)
                try
                {
                    stats.NetProfit = stats.ThisMonthSales - stats.ThisMonthPurchases - stats.ThisMonthExpenses;
                }
                catch
                {
                    stats.NetProfit = 0;
                }

                return stats;
            }
            catch (Exception)
            {
                // في حالة فشل كامل، إرجاع إحصائيات فارغة
                return new DashboardStatistics
                {
                    TopCustomers = new List<TopCustomer>(),
                    TopProducts = new List<TopProduct>()
                };
            }
        }

        public async Task<List<MonthlySalesData>> GetMonthlySalesDataAsync(int months = 12)
        {
            try
            {
                var startDate = DateTime.Today.AddMonths(-months);

                var salesData = await _context.SalesInvoices
                    .Where(s => s.InvoiceDate >= startDate && s.Status == "مؤكدة")
                    .GroupBy(s => new { s.InvoiceDate.Year, s.InvoiceDate.Month })
                    .Select(g => new MonthlySalesData
                    {
                        Year = g.Key.Year,
                        Month = g.Key.Month,
                        TotalSales = g.Sum(s => s.TotalAmount),
                        InvoicesCount = g.Count()
                    })
                    .OrderBy(d => d.Year)
                    .ThenBy(d => d.Month)
                    .ToListAsync();

                return salesData;
            }
            catch
            {
                return new List<MonthlySalesData>();
            }
        }

        public async Task<List<CategorySalesData>> GetCategorySalesDataAsync()
        {
            try
            {
                var thisMonth = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);

                var categoryData = await _context.SalesInvoices
                    .Where(s => s.InvoiceDate >= thisMonth && s.Status == "مؤكدة")
                    .SelectMany(s => s.Items)
                    .GroupBy(i => i.Product.Category ?? "غير محدد")
                    .Select(g => new CategorySalesData
                    {
                        Category = g.Key,
                        TotalSales = g.Sum(i => i.TotalPrice),
                        TotalQuantity = g.Sum(i => i.Quantity)
                    })
                    .OrderByDescending(c => c.TotalSales)
                    .ToListAsync();

                return categoryData;
            }
            catch
            {
                return new List<CategorySalesData>();
            }
        }
    }

    public class DashboardStatistics
    {
        public decimal TodaySales { get; set; }
        public int TodayInvoicesCount { get; set; }
        public decimal ThisMonthSales { get; set; }
        public decimal LastMonthSales { get; set; }
        public decimal ThisMonthPurchases { get; set; }
        public decimal ThisYearSales { get; set; }
        public decimal ThisMonthExpenses { get; set; }
        public decimal NetProfit { get; set; }
        public decimal SalesGrowthPercentage { get; set; }
        public int TotalCustomers { get; set; }
        public int TotalSuppliers { get; set; }
        public int TotalProducts { get; set; }
        public int LowStockProducts { get; set; }
        public decimal TotalInventoryValue { get; set; }
        public int PendingInvoices { get; set; }
        public List<TopCustomer> TopCustomers { get; set; } = new();
        public List<TopProduct> TopProducts { get; set; } = new();
    }

    public class TopCustomer
    {
        public string CustomerName { get; set; } = string.Empty;
        public decimal TotalSales { get; set; }
        public int InvoicesCount { get; set; }
    }

    public class TopProduct
    {
        public string ProductName { get; set; } = string.Empty;
        public decimal TotalQuantity { get; set; }
        public decimal TotalSales { get; set; }
    }

    public class MonthlySalesData
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public decimal TotalSales { get; set; }
        public int InvoicesCount { get; set; }
        public string MonthName => new DateTime(Year, Month, 1).ToString("MMM yyyy");
    }

    public class CategorySalesData
    {
        public string Category { get; set; } = string.Empty;
        public decimal TotalSales { get; set; }
        public decimal TotalQuantity { get; set; }
    }
}
