{"ConnectionStrings": {"DefaultConnection": "Data Source=MarbleWorkshop.db", "SqlServerConnection": "Server=.\\SQLEXPRESS;Database=MarbleWorkshopDB;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true;Encrypt=false", "LocalDbConnection": "Server=(localdb)\\mssqllocaldb;Database=MarbleWorkshopDB;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "Kestrel": {"Endpoints": {"Http": {"Url": "http://localhost:5000"}}}}