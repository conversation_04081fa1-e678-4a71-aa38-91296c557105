using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MarbleWorkshopSystem.Models
{
    public class CuttingOperation
    {
        public int Id { get; set; }

        [Required]
        public int ProductId { get; set; }

        [Required(ErrorMessage = "الكمية الأصلية مطلوبة")]
        [Range(0.01, double.MaxValue, ErrorMessage = "الكمية الأصلية يجب أن تكون أكبر من صفر")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal OriginalQuantity { get; set; }

        [Required(ErrorMessage = "الكمية المستخدمة مطلوبة")]
        [Range(0.01, double.MaxValue, ErrorMessage = "الكمية المستخدمة يجب أن تكون أكبر من صفر")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal UsedQuantity { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal WasteQuantity { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal WastePercentage { get; set; }

        [Required(ErrorMessage = "تاريخ التقطيع مطلوب")]
        public DateTime CuttingDate { get; set; } = DateTime.Now;

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }

        // Navigation Properties
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
    }
}
