using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MarbleWorkshopSystem.Models;
using MarbleWorkshopSystem.Services;
using MarbleWorkshopSystem.ViewModels;
using MarbleWorkshopSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace MarbleWorkshopSystem.Controllers
{
    [Authorize(Roles = "مدير")]
    public class SettingsController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IRepository<SystemSetting> _settingRepository;
        private readonly IRepository<CompanyInfo> _companyRepository;

        public SettingsController(
            ApplicationDbContext context,
            IRepository<SystemSetting> settingRepository,
            IRepository<CompanyInfo> companyRepository)
        {
            _context = context;
            _settingRepository = settingRepository;
            _companyRepository = companyRepository;
        }

        // GET: Settings
        public async Task<IActionResult> Index()
        {
            var viewModel = new SettingsViewModel
            {
                CompanyInfo = await GetCompanyInfoAsync(),
                SystemSettings = await GetSystemSettingsAsync(),
                InventorySettings = await GetInventorySettingsAsync(),
                InvoiceSettings = await GetInvoiceSettingsAsync(),
                PrintSettings = await GetPrintSettingsAsync()
            };

            return View(viewModel);
        }

        // POST: Settings/UpdateCompany
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateCompany(CompanyInfoViewModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var companyInfo = await _context.CompanyInfos.FirstOrDefaultAsync();
                    
                    if (companyInfo == null)
                    {
                        companyInfo = new CompanyInfo();
                        _context.CompanyInfos.Add(companyInfo);
                    }

                    companyInfo.CompanyName = model.CompanyName;
                    companyInfo.Address = model.Address;
                    companyInfo.Phone = model.Phone;
                    companyInfo.Mobile = model.Mobile;
                    companyInfo.Email = model.Email;
                    companyInfo.TaxNumber = model.TaxNumber;
                    companyInfo.CommercialRegister = model.CommercialRegister;
                    companyInfo.Notes = model.Notes;
                    companyInfo.LastUpdated = DateTime.Now;
                    companyInfo.UpdatedBy = User.Identity?.Name;

                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم تحديث بيانات الشركة بنجاح";
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "حدث خطأ أثناء تحديث بيانات الشركة: " + ex.Message;
                }
            }

            return RedirectToAction(nameof(Index));
        }

        // POST: Settings/UpdateSystem
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateSystem(SystemSettingsViewModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    await UpdateSettingAsync(SettingKeys.DefaultCurrency, model.DefaultCurrency);
                    await UpdateSettingAsync(SettingKeys.DateFormat, model.DateFormat);
                    await UpdateSettingAsync(SettingKeys.TimeZone, model.TimeZone);
                    await UpdateSettingAsync(SettingKeys.Language, model.Language);

                    TempData["SuccessMessage"] = "تم تحديث إعدادات النظام بنجاح";
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "حدث خطأ أثناء تحديث إعدادات النظام: " + ex.Message;
                }
            }

            return RedirectToAction(nameof(Index));
        }

        // POST: Settings/UpdateInventory
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateInventory(InventorySettingsViewModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    await UpdateSettingAsync(SettingKeys.LowStockThreshold, model.LowStockThreshold.ToString());
                    await UpdateSettingAsync(SettingKeys.AutoUpdateStock, model.AutoUpdateStock.ToString());
                    await UpdateSettingAsync(SettingKeys.AllowNegativeStock, model.AllowNegativeStock.ToString());

                    TempData["SuccessMessage"] = "تم تحديث إعدادات المخزون بنجاح";
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "حدث خطأ أثناء تحديث إعدادات المخزون: " + ex.Message;
                }
            }

            return RedirectToAction(nameof(Index));
        }

        // POST: Settings/UpdateInvoice
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateInvoice(InvoiceSettingsViewModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    await UpdateSettingAsync(SettingKeys.InvoicePrefix, model.InvoicePrefix);
                    await UpdateSettingAsync(SettingKeys.DefaultTaxRate, model.DefaultTaxRate.ToString());
                    await UpdateSettingAsync(SettingKeys.DefaultDiscount, model.DefaultDiscount.ToString());
                    await UpdateSettingAsync(SettingKeys.PrintAfterSave, model.PrintAfterSave.ToString());

                    TempData["SuccessMessage"] = "تم تحديث إعدادات الفواتير بنجاح";
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "حدث خطأ أثناء تحديث إعدادات الفواتير: " + ex.Message;
                }
            }

            return RedirectToAction(nameof(Index));
        }

        // POST: Settings/UpdatePrint
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdatePrint(PrintSettingsViewModel model)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    await UpdateSettingAsync(SettingKeys.DefaultPrintFormat, model.DefaultPrintFormat);
                    await UpdateSettingAsync(SettingKeys.PrinterName, model.PrinterName);
                    await UpdateSettingAsync(SettingKeys.PaperSize, model.PaperSize);

                    TempData["SuccessMessage"] = "تم تحديث إعدادات الطباعة بنجاح";
                }
                catch (Exception ex)
                {
                    TempData["ErrorMessage"] = "حدث خطأ أثناء تحديث إعدادات الطباعة: " + ex.Message;
                }
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: Settings/Backup
        public IActionResult Backup()
        {
            return View();
        }

        // POST: Settings/CreateBackup
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateBackup()
        {
            try
            {
                // هنا يمكن إضافة منطق النسخ الاحتياطي
                TempData["SuccessMessage"] = "تم إنشاء النسخة الاحتياطية بنجاح";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء إنشاء النسخة الاحتياطية: " + ex.Message;
            }

            return RedirectToAction(nameof(Backup));
        }

        private async Task<CompanyInfoViewModel> GetCompanyInfoAsync()
        {
            var companyInfo = await _context.CompanyInfos.FirstOrDefaultAsync();
            
            return new CompanyInfoViewModel
            {
                CompanyName = companyInfo?.CompanyName ?? "",
                Address = companyInfo?.Address ?? "",
                Phone = companyInfo?.Phone ?? "",
                Mobile = companyInfo?.Mobile ?? "",
                Email = companyInfo?.Email ?? "",
                TaxNumber = companyInfo?.TaxNumber ?? "",
                CommercialRegister = companyInfo?.CommercialRegister ?? "",
                Notes = companyInfo?.Notes ?? ""
            };
        }

        private async Task<SystemSettingsViewModel> GetSystemSettingsAsync()
        {
            return new SystemSettingsViewModel
            {
                DefaultCurrency = await GetSettingValueAsync(SettingKeys.DefaultCurrency, "جنيه مصري"),
                DateFormat = await GetSettingValueAsync(SettingKeys.DateFormat, "dd/MM/yyyy"),
                TimeZone = await GetSettingValueAsync(SettingKeys.TimeZone, "Africa/Cairo"),
                Language = await GetSettingValueAsync(SettingKeys.Language, "ar")
            };
        }

        private async Task<InventorySettingsViewModel> GetInventorySettingsAsync()
        {
            return new InventorySettingsViewModel
            {
                LowStockThreshold = decimal.Parse(await GetSettingValueAsync(SettingKeys.LowStockThreshold, "10")),
                AutoUpdateStock = bool.Parse(await GetSettingValueAsync(SettingKeys.AutoUpdateStock, "true")),
                AllowNegativeStock = bool.Parse(await GetSettingValueAsync(SettingKeys.AllowNegativeStock, "false"))
            };
        }

        private async Task<InvoiceSettingsViewModel> GetInvoiceSettingsAsync()
        {
            return new InvoiceSettingsViewModel
            {
                InvoicePrefix = await GetSettingValueAsync(SettingKeys.InvoicePrefix, "INV"),
                DefaultTaxRate = decimal.Parse(await GetSettingValueAsync(SettingKeys.DefaultTaxRate, "14")),
                DefaultDiscount = decimal.Parse(await GetSettingValueAsync(SettingKeys.DefaultDiscount, "0")),
                PrintAfterSave = bool.Parse(await GetSettingValueAsync(SettingKeys.PrintAfterSave, "false"))
            };
        }

        private async Task<PrintSettingsViewModel> GetPrintSettingsAsync()
        {
            return new PrintSettingsViewModel
            {
                DefaultPrintFormat = await GetSettingValueAsync(SettingKeys.DefaultPrintFormat, "A4"),
                PrinterName = await GetSettingValueAsync(SettingKeys.PrinterName, ""),
                PaperSize = await GetSettingValueAsync(SettingKeys.PaperSize, "A4")
            };
        }

        private async Task<string> GetSettingValueAsync(string key, string defaultValue)
        {
            var setting = await _context.SystemSettings.FirstOrDefaultAsync(s => s.Key == key);
            return setting?.Value ?? defaultValue;
        }

        private async Task UpdateSettingAsync(string key, string value)
        {
            var setting = await _context.SystemSettings.FirstOrDefaultAsync(s => s.Key == key);
            
            if (setting == null)
            {
                setting = new SystemSetting
                {
                    Key = key,
                    Value = value,
                    UpdatedBy = User.Identity?.Name
                };
                _context.SystemSettings.Add(setting);
            }
            else
            {
                setting.Value = value;
                setting.LastUpdated = DateTime.Now;
                setting.UpdatedBy = User.Identity?.Name;
            }

            await _context.SaveChangesAsync();
        }
    }
}
