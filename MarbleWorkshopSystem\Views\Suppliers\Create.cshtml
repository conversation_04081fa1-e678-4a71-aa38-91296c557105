@model MarbleWorkshopSystem.Models.Supplier

@{
    ViewData["Title"] = "إضافة مورد جديد";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مورد جديد
                    </h3>
                </div>

                <div class="card-body">
                    <form asp-action="Create" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <!-- معلومات أساسية -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Name" class="form-label">اسم المورد *</label>
                                    <input asp-for="Name" class="form-control" placeholder="أدخل اسم المورد">
                                    <span asp-validation-for="Name" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="Phone" class="form-label">رقم الهاتف</label>
                                    <input asp-for="Phone" class="form-control" placeholder="أدخل رقم الهاتف">
                                    <span asp-validation-for="Phone" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="Email" class="form-label">البريد الإلكتروني</label>
                                    <input asp-for="Email" type="email" class="form-control" placeholder="أدخل البريد الإلكتروني">
                                    <span asp-validation-for="Email" class="text-danger"></span>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Address" class="form-label">العنوان</label>
                                    <textarea asp-for="Address" class="form-control" rows="3" placeholder="أدخل العنوان"></textarea>
                                    <span asp-validation-for="Address" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="TaxNumber" class="form-label">الرقم الضريبي</label>
                                    <input asp-for="TaxNumber" class="form-control" placeholder="أدخل الرقم الضريبي">
                                    <span asp-validation-for="TaxNumber" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <!-- الرصيد الافتتاحي -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-balance-scale me-2"></i>
                                            الرصيد الافتتاحي
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label asp-for="OpeningBalance" class="form-label">مبلغ الرصيد الافتتاحي</label>
                                                    <div class="input-group">
                                                        <input asp-for="OpeningBalance" class="form-control" placeholder="0.00" step="0.01" min="0">
                                                        <span class="input-group-text">ج.م</span>
                                                    </div>
                                                    <span asp-validation-for="OpeningBalance" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label asp-for="OpeningBalanceType" class="form-label">نوع الرصيد</label>
                                                    <select asp-for="OpeningBalanceType" class="form-select">
                                                        <option value="1">مدين (له)</option>
                                                        <option value="2">دائن (عليه)</option>
                                                    </select>
                                                    <span asp-validation-for="OpeningBalanceType" class="text-danger"></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="alert alert-info mb-0">
                                                    <i class="fas fa-info-circle me-2"></i>
                                                    <strong>مدين (له):</strong> المورد له رصيد في ذمتنا (نحن مدينون له)
                                                    <br>
                                                    <strong>دائن (عليه):</strong> المورد عليه رصيد لنا (هو مدين لنا)
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-info-circle me-2"></i>
                                            ملاحظات
                                        </h6>
                                        <ul class="mb-0">
                                            <li>الحقول المميزة بـ (*) مطلوبة</li>
                                            <li>الرصيد الافتتاحي سيتم تطبيقه على الرصيد الحالي للمورد</li>
                                            <li>يمكن تعديل جميع البيانات لاحقاً</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        العودة للقائمة
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>
                                        حفظ المورد
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
