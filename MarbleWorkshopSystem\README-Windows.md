# نظام إدارة ورشة الرخام - دليل التثبيت لنظام Windows

## متطلبات النظام

### الحد الأدنى:
- **نظام التشغيل:** Windows 7 SP1 أو أحدث
- **المعالج:** Intel/AMD x64 
- **الذاكرة:** 2 GB RAM
- **مساحة القرص:** 500 MB مساحة فارغة
- **الشبكة:** اتصال بالإنترنت (للتثبيت الأولي)

### الموصى به:
- **نظام التشغيل:** Windows 10/11
- **المعالج:** Intel Core i3 أو أحدث
- **الذاكرة:** 4 GB RAM أو أكثر
- **مساحة القرص:** 2 GB مساحة فارغة
- **قاعدة البيانات:** SQL Server Express أو أحدث

## التثبيت

### الخطوة 1: تثبيت .NET 8.0 Runtime

1. قم بتحميل .NET 8.0 Runtime من الرابط التالي:
   ```
   https://dotnet.microsoft.com/download/dotnet/8.0
   ```

2. اختر "Download .NET 8.0 Runtime" للنظام المناسب:
   - **Windows x64** (للأنظمة 64-bit)
   - **Windows x86** (للأنظمة 32-bit القديمة)

3. قم بتشغيل ملف التثبيت واتبع التعليمات

### الخطوة 2: تثبيت قاعدة البيانات (اختياري)

#### SQL Server Express (موصى به):
1. قم بتحميل SQL Server Express من:
   ```
   https://www.microsoft.com/sql-server/sql-server-downloads
   ```

2. قم بالتثبيت مع الإعدادات الافتراضية

#### أو استخدم LocalDB (مدمج مع .NET):
- لا يتطلب تثبيت إضافي
- مناسب للاستخدام الشخصي أو الشركات الصغيرة

### الخطوة 3: تشغيل النظام

#### الطريقة الأولى: التشغيل المباشر
1. انقر نقراً مزدوجاً على `start-marble-workshop.bat`
2. سيتم فتح المتصفح تلقائياً على `http://localhost:5000`

#### الطريقة الثانية: تثبيت كخدمة Windows
1. انقر بالزر الأيمن على `install-service.bat`
2. اختر "تشغيل كمدير"
3. اتبع التعليمات على الشاشة

## الاستخدام

### الوصول للنظام:
- **العنوان المحلي:** http://localhost:5000
- **العنوان الآمن:** https://localhost:5001

### بيانات الدخول الافتراضية:
- **اسم المستخدم:** <EMAIL>
- **كلمة المرور:** Admin123!

### المجلدات المهمة:
- **الملفات المرفوعة:** `wwwroot/uploads/`
- **النسخ الاحتياطية:** `Backups/`
- **ملفات السجل:** `Logs/`

## استكشاف الأخطاء

### مشكلة: "لا يمكن العثور على .NET Runtime"
**الحل:**
1. تأكد من تثبيت .NET 8.0 Runtime
2. أعد تشغيل الكمبيوتر بعد التثبيت
3. تحقق من متغيرات البيئة (PATH)

### مشكلة: "خطأ في الاتصال بقاعدة البيانات"
**الحل:**
1. تأكد من تشغيل SQL Server
2. تحقق من سلسلة الاتصال في `appsettings.json`
3. تأكد من صلاحيات المستخدم

### مشكلة: "المنفذ مستخدم بالفعل"
**الحل:**
1. أغلق أي تطبيقات تستخدم المنفذ 5000
2. أو غير المنفذ في `appsettings.json`

### مشكلة: "رفض الوصول"
**الحل:**
1. تشغيل التطبيق كمدير
2. تحقق من إعدادات Windows Firewall
3. تأكد من صلاحيات المجلد

## الصيانة

### النسخ الاحتياطي:
- يتم إنشاء نسخة احتياطية تلقائياً يومياً في الساعة 2:00 صباحاً
- مجلد النسخ الاحتياطية: `Backups/`

### التحديث:
1. أوقف الخدمة أو التطبيق
2. استبدل الملفات بالإصدار الجديد
3. أعد تشغيل الخدمة أو التطبيق

### إلغاء التثبيت:
1. انقر بالزر الأيمن على `uninstall-service.bat`
2. اختر "تشغيل كمدير"
3. اتبع التعليمات لحذف الملفات

## الدعم الفني

### معلومات النظام:
- **الإصدار:** 1.0.0
- **التقنية:** ASP.NET Core 8.0
- **قاعدة البيانات:** SQL Server / LocalDB
- **المتصفحات المدعومة:** Chrome, Firefox, Edge

### الحصول على المساعدة:
1. تحقق من ملفات السجل في مجلد `Logs/`
2. راجع هذا الدليل لحلول المشاكل الشائعة
3. تواصل مع فريق الدعم الفني

## الأمان

### نصائح الأمان:
1. غير كلمة مرور المدير الافتراضية
2. استخدم HTTPS في بيئة الإنتاج
3. قم بتحديث النظام بانتظام
4. احتفظ بنسخ احتياطية منتظمة

### إعدادات الشبكة:
- المنفذ الافتراضي: 5000 (HTTP)
- المنفذ الآمن: 5001 (HTTPS)
- يمكن تغيير المنافذ في `appsettings.json`

---

**© 2024 نظام إدارة ورشة الرخام. جميع الحقوق محفوظة.**
