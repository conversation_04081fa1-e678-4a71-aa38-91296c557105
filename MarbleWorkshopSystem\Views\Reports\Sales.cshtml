@model MarbleWorkshopSystem.ViewModels.SalesReportViewModel

@{
    ViewData["Title"] = "تقرير المبيعات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>
                        تقرير المبيعات
                    </h3>
                    <div>
                        <button type="button" class="btn btn-info me-2" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>
                            طباعة
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- فلاتر التاريخ -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="get" class="row g-3">
                                <div class="col-md-4">
                                    <label for="fromDate" class="form-label">من تاريخ</label>
                                    <input type="date" name="fromDate" id="fromDate" class="form-control" value="@Model.FromDate.ToString("yyyy-MM-dd")" />
                                </div>
                                <div class="col-md-4">
                                    <label for="toDate" class="form-label">إلى تاريخ</label>
                                    <input type="date" name="toDate" id="toDate" class="form-control" value="@Model.ToDate.ToString("yyyy-MM-dd")" />
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>
                                        تحديث التقرير
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- ملخص الفترة -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="alert alert-success">
                                <h5 class="mb-0">
                                    <i class="fas fa-calendar me-2"></i>
                                    فترة التقرير: من @Model.FromDate.ToString("dd/MM/yyyy") إلى @Model.ToDate.ToString("dd/MM/yyyy")
                                </h5>
                            </div>
                        </div>
                    </div>

                    <!-- البطاقات الإحصائية -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                                    <h4>@Model.TotalSales.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h4>
                                    <p class="mb-0">إجمالي المبيعات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-file-invoice fa-2x mb-2"></i>
                                    <h4>@Model.InvoiceCount</h4>
                                    <p class="mb-0">عدد الفواتير</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                                    <h4>@Model.AverageInvoice.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h4>
                                    <p class="mb-0">متوسط الفاتورة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- أفضل العملاء -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-users me-2"></i>
                                        أفضل العملاء
                                    </h5>
                                </div>
                                <div class="card-body">
                                    @if (Model.TopCustomers.Any())
                                    {
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>العميل</th>
                                                        <th class="text-end">المبيعات</th>
                                                        <th class="text-center">الفواتير</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach (var customer in Model.TopCustomers)
                                                    {
                                                        <tr>
                                                            <td>@customer.CustomerName</td>
                                                            <td class="text-end">@customer.TotalSales.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</td>
                                                            <td class="text-center">@customer.InvoiceCount</td>
                                                        </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                    }
                                    else
                                    {
                                        <p class="text-muted text-center">لا توجد مبيعات في هذه الفترة</p>
                                    }
                                </div>
                            </div>
                        </div>

                        <!-- أفضل المنتجات -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-box me-2"></i>
                                        أفضل المنتجات
                                    </h5>
                                </div>
                                <div class="card-body">
                                    @if (Model.TopProducts.Any())
                                    {
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>المنتج</th>
                                                        <th class="text-center">الكمية</th>
                                                        <th class="text-end">المبيعات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach (var product in Model.TopProducts)
                                                    {
                                                        <tr>
                                                            <td>@product.ProductName</td>
                                                            <td class="text-center">@product.TotalQuantity</td>
                                                            <td class="text-end">@product.TotalSales.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</td>
                                                        </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                    }
                                    else
                                    {
                                        <p class="text-muted text-center">لا توجد مبيعات في هذه الفترة</p>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">ملاحظات</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="mb-0">
                                        <li>التقرير يشمل فقط الفواتير المؤكدة</li>
                                        <li>جميع المبالغ تشمل الضرائب والخصومات</li>
                                        <li>يتم ترتيب العملاء والمنتجات حسب إجمالي المبيعات</li>
                                        <li>يتم عرض أفضل 10 عملاء ومنتجات فقط</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // عرض رسائل النجاح والخطأ
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
                toastr.success('@TempData["SuccessMessage"]');
            </text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>
                toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
    </script>
}
