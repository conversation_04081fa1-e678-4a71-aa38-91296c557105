@model MarbleWorkshopSystem.ViewModels.SettingsViewModel

@{
    ViewData["Title"] = "الإعدادات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات النظام
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Navigation Tabs -->
                    <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="company-tab" data-bs-toggle="tab" data-bs-target="#company" type="button" role="tab">
                                <i class="fas fa-building me-1"></i>
                                بيانات الشركة
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                                <i class="fas fa-desktop me-1"></i>
                                إعدادات النظام
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="inventory-tab" data-bs-toggle="tab" data-bs-target="#inventory" type="button" role="tab">
                                <i class="fas fa-warehouse me-1"></i>
                                إعدادات المخزون
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="invoice-tab" data-bs-toggle="tab" data-bs-target="#invoice" type="button" role="tab">
                                <i class="fas fa-file-invoice me-1"></i>
                                إعدادات الفواتير
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="print-tab" data-bs-toggle="tab" data-bs-target="#print" type="button" role="tab">
                                <i class="fas fa-print me-1"></i>
                                إعدادات الطباعة
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="backup-tab" data-bs-toggle="tab" data-bs-target="#backup" type="button" role="tab">
                                <i class="fas fa-database me-1"></i>
                                النسخ الاحتياطي
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications" type="button" role="tab">
                                <i class="fas fa-bell me-1"></i>
                                التنبيهات
                            </button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content mt-3" id="settingsTabContent">
                        <!-- Company Settings -->
                        <div class="tab-pane fade show active" id="company" role="tabpanel">
                            <form asp-action="UpdateCompany" method="post">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="CompanyInfo.CompanyName" class="form-label">اسم الشركة *</label>
                                            <input asp-for="CompanyInfo.CompanyName" class="form-control" />
                                            <span asp-validation-for="CompanyInfo.CompanyName" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="CompanyInfo.Email" class="form-label">البريد الإلكتروني</label>
                                            <input asp-for="CompanyInfo.Email" class="form-control" />
                                            <span asp-validation-for="CompanyInfo.Email" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="CompanyInfo.Phone" class="form-label">رقم الهاتف</label>
                                            <input asp-for="CompanyInfo.Phone" class="form-control" />
                                            <span asp-validation-for="CompanyInfo.Phone" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="CompanyInfo.Mobile" class="form-label">رقم الموبايل</label>
                                            <input asp-for="CompanyInfo.Mobile" class="form-control" />
                                            <span asp-validation-for="CompanyInfo.Mobile" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label asp-for="CompanyInfo.Address" class="form-label">العنوان</label>
                                    <textarea asp-for="CompanyInfo.Address" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="CompanyInfo.Address" class="text-danger"></span>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="CompanyInfo.TaxNumber" class="form-label">الرقم الضريبي</label>
                                            <input asp-for="CompanyInfo.TaxNumber" class="form-control" />
                                            <span asp-validation-for="CompanyInfo.TaxNumber" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="CompanyInfo.CommercialRegister" class="form-label">السجل التجاري</label>
                                            <input asp-for="CompanyInfo.CommercialRegister" class="form-control" />
                                            <span asp-validation-for="CompanyInfo.CommercialRegister" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label asp-for="CompanyInfo.Notes" class="form-label">ملاحظات</label>
                                    <textarea asp-for="CompanyInfo.Notes" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="CompanyInfo.Notes" class="text-danger"></span>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    حفظ بيانات الشركة
                                </button>
                            </form>
                        </div>

                        <!-- System Settings -->
                        <div class="tab-pane fade" id="system" role="tabpanel">
                            <form asp-action="UpdateSystem" method="post">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="SystemSettings.DefaultCurrency" class="form-label">العملة الافتراضية</label>
                                            <input asp-for="SystemSettings.DefaultCurrency" class="form-control" />
                                            <span asp-validation-for="SystemSettings.DefaultCurrency" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="SystemSettings.DateFormat" class="form-label">تنسيق التاريخ</label>
                                            <select asp-for="SystemSettings.DateFormat" class="form-select">
                                                <option value="dd/MM/yyyy">dd/MM/yyyy</option>
                                                <option value="MM/dd/yyyy">MM/dd/yyyy</option>
                                                <option value="yyyy-MM-dd">yyyy-MM-dd</option>
                                            </select>
                                            <span asp-validation-for="SystemSettings.DateFormat" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="SystemSettings.TimeZone" class="form-label">المنطقة الزمنية</label>
                                            <select asp-for="SystemSettings.TimeZone" class="form-select">
                                                <option value="Africa/Cairo">القاهرة (GMT+2)</option>
                                                <option value="Asia/Riyadh">الرياض (GMT+3)</option>
                                                <option value="UTC">UTC (GMT+0)</option>
                                            </select>
                                            <span asp-validation-for="SystemSettings.TimeZone" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="SystemSettings.Language" class="form-label">اللغة</label>
                                            <select asp-for="SystemSettings.Language" class="form-select">
                                                <option value="ar">العربية</option>
                                                <option value="en">English</option>
                                            </select>
                                            <span asp-validation-for="SystemSettings.Language" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    حفظ إعدادات النظام
                                </button>
                            </form>
                        </div>

                        <!-- Inventory Settings -->
                        <div class="tab-pane fade" id="inventory" role="tabpanel">
                            <form asp-action="UpdateInventory" method="post">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label asp-for="InventorySettings.LowStockThreshold" class="form-label">حد المخزون المنخفض</label>
                                            <input asp-for="InventorySettings.LowStockThreshold" class="form-control" type="number" step="0.01" />
                                            <span asp-validation-for="InventorySettings.LowStockThreshold" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <div class="form-check mt-4">
                                                <input asp-for="InventorySettings.AutoUpdateStock" class="form-check-input" />
                                                <label asp-for="InventorySettings.AutoUpdateStock" class="form-check-label">
                                                    تحديث المخزون تلقائياً
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <div class="form-check mt-4">
                                                <input asp-for="InventorySettings.AllowNegativeStock" class="form-check-input" />
                                                <label asp-for="InventorySettings.AllowNegativeStock" class="form-check-label">
                                                    السماح بالمخزون السالب
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    حفظ إعدادات المخزون
                                </button>
                            </form>
                        </div>

                        <!-- Invoice Settings -->
                        <div class="tab-pane fade" id="invoice" role="tabpanel">
                            <form asp-action="UpdateInvoice" method="post">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="InvoiceSettings.InvoicePrefix" class="form-label">بادئة رقم الفاتورة</label>
                                            <input asp-for="InvoiceSettings.InvoicePrefix" class="form-control" />
                                            <span asp-validation-for="InvoiceSettings.InvoicePrefix" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="InvoiceSettings.DefaultTaxRate" class="form-label">معدل الضريبة الافتراضي (%)</label>
                                            <input asp-for="InvoiceSettings.DefaultTaxRate" class="form-control" type="number" step="0.01" />
                                            <span asp-validation-for="InvoiceSettings.DefaultTaxRate" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="InvoiceSettings.DefaultDiscount" class="form-label">الخصم الافتراضي (%)</label>
                                            <input asp-for="InvoiceSettings.DefaultDiscount" class="form-control" type="number" step="0.01" />
                                            <span asp-validation-for="InvoiceSettings.DefaultDiscount" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check mt-4">
                                                <input asp-for="InvoiceSettings.PrintAfterSave" class="form-check-input" />
                                                <label asp-for="InvoiceSettings.PrintAfterSave" class="form-check-label">
                                                    طباعة الفاتورة بعد الحفظ
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    حفظ إعدادات الفواتير
                                </button>
                            </form>
                        </div>

                        <!-- Print Settings -->
                        <div class="tab-pane fade" id="print" role="tabpanel">
                            <form asp-action="UpdatePrint" method="post">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label asp-for="PrintSettings.DefaultPrintFormat" class="form-label">تنسيق الطباعة الافتراضي</label>
                                            <select asp-for="PrintSettings.DefaultPrintFormat" class="form-select">
                                                <option value="A4">A4 (210×297 مم)</option>
                                                <option value="A5">A5 (148×210 مم)</option>
                                                <option value="POS">POS (80 مم)</option>
                                            </select>
                                            <span asp-validation-for="PrintSettings.DefaultPrintFormat" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label asp-for="PrintSettings.PrinterName" class="form-label">اسم الطابعة</label>
                                            <input asp-for="PrintSettings.PrinterName" class="form-control" />
                                            <span asp-validation-for="PrintSettings.PrinterName" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label asp-for="PrintSettings.PaperSize" class="form-label">حجم الورق</label>
                                            <select asp-for="PrintSettings.PaperSize" class="form-select">
                                                <option value="A4">A4</option>
                                                <option value="A5">A5</option>
                                                <option value="Letter">Letter</option>
                                                <option value="Legal">Legal</option>
                                            </select>
                                            <span asp-validation-for="PrintSettings.PaperSize" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    حفظ إعدادات الطباعة
                                </button>
                            </form>
                        </div>

                        <!-- Backup Settings -->
                        <div class="tab-pane fade" id="backup" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">النسخ الاحتياطي التلقائي</h6>
                                        </div>
                                        <div class="card-body">
                                            <form asp-action="UpdateBackup" method="post">
                                                <div class="mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="autoBackup" name="AutoBackup" checked>
                                                        <label class="form-check-label" for="autoBackup">
                                                            تفعيل النسخ الاحتياطي التلقائي
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">تكرار النسخ الاحتياطي</label>
                                                    <select class="form-select" name="BackupFrequency">
                                                        <option value="daily">يومياً</option>
                                                        <option value="weekly" selected>أسبوعياً</option>
                                                        <option value="monthly">شهرياً</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">مسار حفظ النسخ</label>
                                                    <input type="text" class="form-control" name="BackupPath" value="C:\Backups\MarbleWorkshop">
                                                </div>
                                                <button type="submit" class="btn btn-success">
                                                    <i class="fas fa-save me-1"></i>
                                                    حفظ الإعدادات
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">إجراءات النسخ الاحتياطي</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="d-grid gap-2">
                                                <button type="button" class="btn btn-primary" onclick="createBackup()">
                                                    <i class="fas fa-download me-1"></i>
                                                    إنشاء نسخة احتياطية الآن
                                                </button>
                                                <button type="button" class="btn btn-warning" onclick="restoreBackup()">
                                                    <i class="fas fa-upload me-1"></i>
                                                    استعادة نسخة احتياطية
                                                </button>
                                                <button type="button" class="btn btn-info" onclick="exportData()">
                                                    <i class="fas fa-file-export me-1"></i>
                                                    تصدير البيانات (Excel)
                                                </button>
                                            </div>
                                            <hr>
                                            <small class="text-muted">
                                                آخر نسخة احتياطية: 15/08/2025 - 10:30 ص
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Notifications Settings -->
                        <div class="tab-pane fade" id="notifications" role="tabpanel">
                            <form asp-action="UpdateNotifications" method="post">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">تنبيهات المخزون</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="lowStockAlert" name="LowStockAlert" checked>
                                                        <label class="form-check-label" for="lowStockAlert">
                                                            تنبيه المخزون المنخفض
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="outOfStockAlert" name="OutOfStockAlert" checked>
                                                        <label class="form-check-label" for="outOfStockAlert">
                                                            تنبيه نفاد المخزون
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="expiryAlert" name="ExpiryAlert">
                                                        <label class="form-check-label" for="expiryAlert">
                                                            تنبيه انتهاء الصلاحية
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">تنبيهات المبيعات</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="dailySalesAlert" name="DailySalesAlert">
                                                        <label class="form-check-label" for="dailySalesAlert">
                                                            تقرير المبيعات اليومي
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="paymentDueAlert" name="PaymentDueAlert" checked>
                                                        <label class="form-check-label" for="paymentDueAlert">
                                                            تنبيه استحقاق المدفوعات
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">البريد الإلكتروني للتنبيهات</label>
                                                    <input type="email" class="form-control" name="NotificationEmail" placeholder="<EMAIL>">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>
                                            حفظ إعدادات التنبيهات
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // عرض رسائل النجاح والخطأ
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
                toastr.success('@TempData["SuccessMessage"]');
            </text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>
                toastr.error('@TempData["ErrorMessage"]');
            </text>
        }

        // دوال النسخ الاحتياطي
        function createBackup() {
            if (confirm('هل تريد إنشاء نسخة احتياطية الآن؟')) {
                // هنا يمكن إضافة استدعاء AJAX لإنشاء النسخة الاحتياطية
                toastr.info('جاري إنشاء النسخة الاحتياطية...');

                // محاكاة العملية
                setTimeout(() => {
                    toastr.success('تم إنشاء النسخة الاحتياطية بنجاح');
                }, 3000);
            }
        }

        function restoreBackup() {
            if (confirm('تحذير: سيتم استبدال البيانات الحالية. هل تريد المتابعة؟')) {
                // إنشاء input لاختيار الملف
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.bak,.sql';
                input.onchange = function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        toastr.info('جاري استعادة النسخة الاحتياطية...');
                        // هنا يمكن إضافة منطق رفع الملف واستعادة البيانات
                    }
                };
                input.click();
            }
        }

        function exportData() {
            toastr.info('جاري تصدير البيانات...');
            // هنا يمكن إضافة منطق تصدير البيانات إلى Excel
            setTimeout(() => {
                toastr.success('تم تصدير البيانات بنجاح');
            }, 2000);
        }
    </script>
}
