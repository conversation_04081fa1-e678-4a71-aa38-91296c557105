{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}, "Console": {"IncludeScopes": false, "LogLevel": {"Default": "Information"}}, "File": {"Path": "Logs/marble-workshop-{Date}.log", "LogLevel": {"Default": "Information"}}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=MarbleWorkshopDB;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true", "SqlServerConnection": "Server=.\\SQLEXPRESS;Database=MarbleWorkshopDB;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true;Encrypt=false"}, "Kestrel": {"Endpoints": {"Http": {"Url": "http://localhost:5000"}, "Https": {"Url": "https://localhost:5001"}}, "Limits": {"MaxConcurrentConnections": 100, "MaxConcurrentUpgradedConnections": 100, "MaxRequestBodySize": 10485760, "KeepAliveTimeout": "00:02:00", "RequestHeadersTimeout": "00:00:30"}}, "Application": {"Name": "نظام إدارة ورشة الرخام", "Version": "1.0.0", "Environment": "Production", "SupportedCultures": ["ar-EG", "en-US"], "DefaultCulture": "ar-EG", "TimeZone": "Egypt Standard Time"}, "Security": {"RequireHttps": false, "CookieSecure": "SameAsRequest", "SessionTimeout": 30, "MaxLoginAttempts": 5, "LockoutDuration": 15}, "Features": {"EnableDetailedErrors": false, "EnableDeveloperExceptionPage": false, "EnableSwagger": false, "EnableResponseCompression": true, "EnableResponseCaching": true}, "Database": {"CommandTimeout": 30, "EnableSensitiveDataLogging": false, "EnableDetailedErrors": false, "MaxRetryCount": 3, "MaxRetryDelay": "00:00:30"}, "FileStorage": {"MaxFileSize": 5242880, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".xls", ".xlsx"], "UploadPath": "wwwroot/uploads", "TempPath": "wwwroot/temp"}, "Backup": {"Enabled": true, "Schedule": "0 2 * * *", "RetentionDays": 30, "BackupPath": "Backups"}, "Performance": {"EnableOutputCaching": true, "CacheDuration": 300, "EnableCompression": true, "CompressionLevel": "Optimal"}}