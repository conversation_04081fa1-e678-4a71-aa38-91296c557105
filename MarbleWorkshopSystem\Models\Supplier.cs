using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MarbleWorkshopSystem.Models
{
    public class Supplier
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم المورد مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المورد يجب أن يكون أقل من 100 حرف")]
        public string Name { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "رقم الهاتف يجب أن يكون أقل من 20 حرف")]
        public string? Phone { get; set; }

        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [StringLength(100, ErrorMessage = "البريد الإلكتروني يجب أن يكون أقل من 100 حرف")]
        public string? Email { get; set; }

        [StringLength(200, ErrorMessage = "العنوان يجب أن يكون أقل من 200 حرف")]
        public string? Address { get; set; }

        [StringLength(50, ErrorMessage = "الرقم الضريبي يجب أن يكون أقل من 50 حرف")]
        public string? TaxNumber { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Balance { get; set; } = 0;

        [Display(Name = "الرصيد الافتتاحي")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; } = 0;

        [Display(Name = "نوع الرصيد الافتتاحي")]
        public BalanceType OpeningBalanceType { get; set; } = BalanceType.Debit;

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public bool IsActive { get; set; } = true;

        // Navigation Properties
        public virtual ICollection<PurchaseInvoice> PurchaseInvoices { get; set; } = new List<PurchaseInvoice>();
        public virtual ICollection<Payment> Payments { get; set; } = new List<Payment>();
    }
}
