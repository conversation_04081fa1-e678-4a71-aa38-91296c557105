@echo off
chcp 65001 > nul
title إلغاء تثبيت خدمة نظام إدارة ورشة الرخام

echo ========================================
echo    إلغاء تثبيت خدمة نظام إدارة ورشة الرخام
echo    Uninstall Marble Workshop Service
echo ========================================
echo.

REM التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [خطأ] يجب تشغيل هذا الملف كمدير
    echo [ERROR] This script must be run as Administrator
    echo.
    echo انقر بالزر الأيمن واختر "تشغيل كمدير"
    echo Right-click and select "Run as Administrator"
    pause
    exit /b 1
)

echo جاري إيقاف الخدمة...
echo Stopping service...

REM إيقاف الخدمة
sc stop "MarbleWorkshopService" >nul 2>&1
timeout /t 5 /nobreak >nul

echo جاري إلغاء تثبيت الخدمة...
echo Uninstalling service...

REM حذف الخدمة
sc delete "MarbleWorkshopService"

if %errorlevel% equ 0 (
    echo ✓ تم إلغاء تثبيت الخدمة بنجاح
    echo ✓ Service uninstalled successfully
) else (
    echo ✗ فشل في إلغاء تثبيت الخدمة
    echo ✗ Failed to uninstall service
)

echo.
echo هل تريد حذف ملفات التطبيق؟ (y/n)
echo Do you want to delete application files? (y/n)
set /p choice=

if /i "%choice%"=="y" (
    set SERVICE_PATH=C:\MarbleWorkshop
    if exist "%SERVICE_PATH%" (
        echo جاري حذف الملفات...
        echo Deleting files...
        rmdir /s /q "%SERVICE_PATH%"
        echo ✓ تم حذف الملفات
        echo ✓ Files deleted
    )
)

echo.
echo تم الانتهاء
echo Completed
pause
