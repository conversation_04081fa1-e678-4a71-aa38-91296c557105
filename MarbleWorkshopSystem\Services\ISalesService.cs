using MarbleWorkshopSystem.Models;

namespace MarbleWorkshopSystem.Services
{
    public interface ISalesService
    {
        Task<IEnumerable<SalesInvoice>> GetAllSalesInvoicesAsync();
        Task<SalesInvoice?> GetSalesInvoiceByIdAsync(int id);
        Task<IEnumerable<SalesInvoice>> GetSalesInvoicesByCustomerAsync(int customerId);
        Task<IEnumerable<SalesInvoice>> GetSalesInvoicesByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<IEnumerable<SalesInvoice>> GetSalesInvoicesByStatusAsync(string status);
        Task<SalesInvoice> CreateSalesInvoiceAsync(SalesInvoice invoice);
        Task<SalesInvoice> UpdateSalesInvoiceAsync(SalesInvoice invoice);
        Task DeleteSalesInvoiceAsync(int id);
        Task<SalesInvoice> ConfirmSalesInvoiceAsync(int id);
        Task<SalesInvoice> CancelSalesInvoiceAsync(int id);
        Task<string> GenerateInvoiceNumberAsync();
        Task<decimal> CalculateInvoiceTotalAsync(SalesInvoice invoice);
        Task<bool> HasSufficientStockAsync(SalesInvoice invoice);
        Task<IEnumerable<SalesInvoice>> SearchInvoicesAsync(string searchTerm);
        Task<decimal> GetTotalSalesAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<int> GetInvoiceCountAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<decimal> GetAverageSaleAsync(DateTime? fromDate = null, DateTime? toDate = null);
    }
}
