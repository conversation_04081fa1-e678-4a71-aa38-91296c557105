@model MarbleWorkshopSystem.Models.PurchaseInvoice

@{
    ViewData["Title"] = "طباعة فاتورة المشتريات";
    Layout = null;
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .invoice-header {
            border-bottom: 3px solid #007bff;
            margin-bottom: 30px;
            padding-bottom: 20px;
        }
        
        .company-info {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .invoice-title {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
        }
        
        .invoice-details {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .supplier-info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            border-right: 4px solid #2196f3;
        }
        
        .items-table {
            margin: 20px 0;
        }
        
        .items-table th {
            background-color: #007bff;
            color: white;
            text-align: center;
            padding: 12px 8px;
            font-weight: bold;
        }
        
        .items-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }
        
        .item-row:hover {
            background-color: #f8f9fa;
        }
        
        .totals-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #007bff;
        }
        
        .total-row {
            font-size: 16px;
            font-weight: bold;
            color: #007bff;
        }
        
        .footer-info {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            text-align: center;
            color: #6c757d;
        }
        
        @@media print {
            .no-print {
                display: none !important;
            }
            
            body {
                font-size: 12px;
            }
            
            .invoice-title {
                background: #007bff !important;
                -webkit-print-color-adjust: exact;
            }
            
            .items-table th {
                background-color: #007bff !important;
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- أزرار الطباعة -->
        <div class="no-print mb-3">
            <div class="d-flex justify-content-between">
                <div>
                    <button onclick="window.print()" class="btn btn-primary">
                        <i class="fas fa-print me-1"></i>
                        طباعة
                    </button>
                    <a href="@Url.Action("Details", new { id = Model.Id })" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        العودة
                    </a>
                </div>
                <div>
                    <select class="form-select" onchange="changePrintFormat(this.value)" style="width: auto;">
                        <option value="A4" selected="@(ViewBag.PrintFormat == "A4")">A4</option>
                        <option value="A5" selected="@(ViewBag.PrintFormat == "A5")">A5</option>
                        <option value="thermal" selected="@(ViewBag.PrintFormat == "thermal")">طابعة حرارية</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <div class="company-info">
                <h2 class="text-primary mb-1">ورشة الرخام المتطورة</h2>
                <p class="mb-1">العنوان: شارع الصناعة، المنطقة الصناعية</p>
                <p class="mb-0">الهاتف: 01234567890 | البريد الإلكتروني: <EMAIL></p>
            </div>
        </div>

        <!-- عنوان الفاتورة -->
        <div class="invoice-title">
            <h3 class="mb-0">
                <i class="fas fa-file-invoice me-2"></i>
                فاتورة مشتريات
            </h3>
        </div>

        <!-- تفاصيل الفاتورة -->
        <div class="row">
            <div class="col-md-6">
                <div class="invoice-details">
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        تفاصيل الفاتورة
                    </h5>
                    <div class="row">
                        <div class="col-6"><strong>رقم الفاتورة:</strong></div>
                        <div class="col-6">@Model.InvoiceNumber</div>
                    </div>
                    <div class="row">
                        <div class="col-6"><strong>التاريخ:</strong></div>
                        <div class="col-6">@Model.InvoiceDate.ToString("dd/MM/yyyy")</div>
                    </div>
                    <div class="row">
                        <div class="col-6"><strong>الحالة:</strong></div>
                        <div class="col-6">
                            <span class="badge @(Model.Status == "مؤكدة" ? "bg-success" : "bg-warning")">
                                @Model.Status
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="supplier-info">
                    <h5 class="text-primary mb-3">
                        <i class="fas fa-truck me-2"></i>
                        بيانات المورد
                    </h5>
                    <div class="row">
                        <div class="col-4"><strong>الاسم:</strong></div>
                        <div class="col-8">@Model.Supplier.Name</div>
                    </div>
                    @if (!string.IsNullOrEmpty(Model.Supplier.Phone))
                    {
                        <div class="row">
                            <div class="col-4"><strong>الهاتف:</strong></div>
                            <div class="col-8">@Model.Supplier.Phone</div>
                        </div>
                    }
                    @if (!string.IsNullOrEmpty(Model.Supplier.Address))
                    {
                        <div class="row">
                            <div class="col-4"><strong>العنوان:</strong></div>
                            <div class="col-8">@Model.Supplier.Address</div>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- جدول الأصناف -->
        <table class="table table-bordered items-table">
            <thead>
                <tr>
                    <th style="width: 30%">المنتج</th>
                    <th style="width: 12%">الطول</th>
                    <th style="width: 12%">العرض</th>
                    <th style="width: 12%">الكمية</th>
                    <th style="width: 12%">سعر الوحدة</th>
                    <th style="width: 12%">المجموع</th>
                    <th style="width: 10%">ملاحظات</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.Items)
                {
                    <tr class="item-row">
                        <td>
                            <strong>@item.Product.Name</strong>
                            @if (!string.IsNullOrEmpty(item.Product.Category))
                            {
                                <br><small class="text-muted">فئة: @item.Product.Category</small>
                            }
                            @if (!string.IsNullOrEmpty(item.Product.Description))
                            {
                                <br><small class="text-info">@item.Product.Description</small>
                            }
                            @if (item.Product.Length > 0 && item.Product.Width > 0)
                            {
                                <br><small class="text-secondary">مقاس المنتج: @item.Product.Length × @item.Product.Width</small>
                            }
                        </td>
                        <td class="text-center">@item.Length.ToString("F2")</td>
                        <td class="text-center">@item.Width.ToString("F2")</td>
                        <td class="text-center">@item.Quantity.ToString("F2")</td>
                        <td class="text-center">@item.UnitPrice.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</td>
                        <td class="text-center">@item.TotalPrice.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</td>
                        <td class="text-center">
                            @if (!string.IsNullOrEmpty(item.Notes))
                            {
                                <small>@item.Notes</small>
                            }
                            else
                            {
                                <span class="text-muted">-</span>
                            }
                        </td>
                    </tr>
                }
            </tbody>
        </table>

        <!-- قسم المجاميع -->
        <div class="row">
            <div class="col-md-6 offset-md-6">
                <div class="totals-section">
                    <div class="row mb-2">
                        <div class="col-6"><strong>المجموع الفرعي:</strong></div>
                        <div class="col-6 text-end">@Model.SubTotal.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</div>
                    </div>
                    
                    @if (Model.DiscountType != MarbleWorkshopSystem.Models.DiscountType.None)
                    {
                        <div class="row mb-2">
                            <div class="col-6">
                                <strong>الخصم (@(Model.DiscountType == MarbleWorkshopSystem.Models.DiscountType.Percentage ? Model.DiscountValue.ToString("F1") + "%" : "مبلغ ثابت")):</strong>
                            </div>
                            <div class="col-6 text-end text-danger">-@Model.DiscountAmount.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</div>
                        </div>
                    }
                    
                    <hr>
                    <div class="row total-row">
                        <div class="col-6"><strong>المجموع النهائي:</strong></div>
                        <div class="col-6 text-end"><strong>@Model.TotalAmount.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</strong></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ملاحظات الفاتورة -->
        @if (!string.IsNullOrEmpty(Model.Notes))
        {
            <div class="row mt-3">
                <div class="col-12">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-sticky-note me-2"></i>ملاحظات:</h6>
                        <p class="mb-0">@Model.Notes</p>
                    </div>
                </div>
            </div>
        }

        <!-- تذييل الفاتورة -->
        <div class="footer-info">
            <p class="mb-1">شكراً لتعاملكم معنا</p>
            <p class="mb-0">تم إنشاء هذه الفاتورة بواسطة نظام إدارة ورشة الرخام</p>
            <small>تاريخ الطباعة: @DateTime.Now.ToString("dd/MM/yyyy HH:mm")</small>
        </div>
    </div>

    <script>
        function changePrintFormat(format) {
            window.location.href = '@Url.Action("Print", new { id = Model.Id })' + '?format=' + format;
        }
    </script>
</body>
</html>
