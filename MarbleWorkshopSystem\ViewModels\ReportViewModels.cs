using MarbleWorkshopSystem.Models;

namespace MarbleWorkshopSystem.ViewModels
{
    public class FinancialReportViewModel
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalPurchases { get; set; }
        public decimal TotalExpenses { get; set; }
        public decimal NetProfit { get; set; }
        public int SalesCount { get; set; }
        public int PurchasesCount { get; set; }
        public decimal AverageSale { get; set; }
        public decimal AveragePurchase { get; set; }
        public List<CategoryExpenseViewModel> ExpensesByCategory { get; set; } = new List<CategoryExpenseViewModel>();
    }

    public class CategoryExpenseViewModel
    {
        public string Category { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public int Count { get; set; }
    }

    public class SalesReportViewModel
    {
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
        public decimal TotalSales { get; set; }
        public int InvoiceCount { get; set; }
        public decimal AverageInvoice { get; set; }
        public List<CustomerSalesViewModel> TopCustomers { get; set; } = new List<CustomerSalesViewModel>();
        public List<ProductSalesViewModel> TopProducts { get; set; } = new List<ProductSalesViewModel>();
    }

    public class CustomerSalesViewModel
    {
        public string CustomerName { get; set; } = string.Empty;
        public decimal TotalSales { get; set; }
        public int InvoiceCount { get; set; }
    }

    public class ProductSalesViewModel
    {
        public string ProductName { get; set; } = string.Empty;
        public decimal TotalQuantity { get; set; }
        public decimal TotalSales { get; set; }
    }

    public class InventoryReportViewModel
    {
        public int TotalProducts { get; set; }
        public int LowStockCount { get; set; }
        public decimal TotalStockValue { get; set; }
        public decimal TotalWaste { get; set; }
        public IEnumerable<Inventory> InventoryItems { get; set; } = new List<Inventory>();
        public IEnumerable<Inventory> LowStockItems { get; set; } = new List<Inventory>();
    }

    public class CustomersReportViewModel
    {
        public int TotalCustomers { get; set; }
        public int ActiveCustomers { get; set; }
        public decimal TotalBalance { get; set; }
        public List<CustomerDetailViewModel> CustomerDetails { get; set; } = new List<CustomerDetailViewModel>();
    }

    public class CustomerDetailViewModel
    {
        public string CustomerName { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public decimal Balance { get; set; }
        public decimal TotalSales { get; set; }
        public int InvoiceCount { get; set; }
        public DateTime? LastSaleDate { get; set; }
    }
}
