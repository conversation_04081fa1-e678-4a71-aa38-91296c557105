@model IEnumerable<MarbleWorkshopSystem.Models.Inventory>

@{
    ViewData["Title"] = "المنتجات منخفضة المخزون";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        المنتجات منخفضة المخزون
                    </h4>
                </div>

                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }

                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }

                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count()</h4>
                                    <p class="mb-0">منتجات منخفضة المخزون</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count(i => i.Quantity == 0)</h4>
                                    <p class="mb-0">منتجات نفد مخزونها</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Sum(i => i.AvailableQuantity).ToString("F2")</h4>
                                    <p class="mb-0">إجمالي الكمية المتاحة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Sum(i => i.WasteQuantity).ToString("F2")</h4>
                                    <p class="mb-0">إجمالي الهالك</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if (Model.Any())
                    {
                        <!-- جدول المنتجات -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الكمية الحالية</th>
                                        <th>الحد الأدنى</th>
                                        <th>الكمية المتاحة</th>
                                        <th>كمية الهالك</th>
                                        <th>حالة المخزون</th>
                                        <th>آخر تحديث</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model.OrderBy(i => i.Quantity))
                                    {
                                        <tr class="@(item.Quantity == 0 ? "table-danger" : "table-warning")">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if (!string.IsNullOrEmpty(item.Product.ImagePath))
                                                    {
                                                        <img src="@item.Product.ImagePath" alt="@item.Product.Name" 
                                                             class="img-thumbnail me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                                    }
                                                    <div>
                                                        <strong>@item.Product.Name</strong>
                                                        <br><small class="text-muted">@item.Product.Category</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge @(item.Quantity == 0 ? "bg-danger" : "bg-warning") fs-6">
                                                    @item.Quantity.ToString("F2")
                                                </span>
                                            </td>
                                            <td>@item.MinimumStock.ToString("F2")</td>
                                            <td>
                                                <span class="badge @(item.AvailableQuantity <= 0 ? "bg-danger" : "bg-info") fs-6">
                                                    @item.AvailableQuantity.ToString("F2")
                                                </span>
                                            </td>
                                            <td>
                                                @if (item.WasteQuantity > 0)
                                                {
                                                    <span class="badge bg-secondary fs-6">@item.WasteQuantity.ToString("F2")</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (item.Quantity == 0)
                                                {
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-times me-1"></i>نفد المخزون
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-warning">
                                                        <i class="fas fa-exclamation-triangle me-1"></i>مخزون منخفض
                                                    </span>
                                                }
                                            </td>
                                            <td>
                                                <small>@item.LastUpdated.ToString("dd/MM/yyyy")</small>
                                                <br><small class="text-muted">@item.LastUpdated.ToString("HH:mm")</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-success" 
                                                            onclick="showAddStockModal(@item.ProductId, '@item.Product.Name')">
                                                        <i class="fas fa-plus me-1"></i>إضافة
                                                    </button>
                                                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-warning">
                                                        <i class="fas fa-edit me-1"></i>تعديل
                                                    </a>
                                                    <a asp-controller="Products" asp-action="Details" asp-route-id="@item.ProductId" 
                                                       class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye me-1"></i>تفاصيل
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                            <h4 class="text-success mt-3">ممتاز! لا توجد منتجات منخفضة المخزون</h4>
                            <p class="text-muted">جميع المنتجات لديها مخزون كافي</p>
                            <a asp-action="Index" class="btn btn-primary">
                                <i class="fas fa-list me-1"></i>
                                عرض جميع المنتجات
                            </a>
                        </div>
                    }

                    <!-- أزرار الإجراءات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <div>
                                    <a asp-action="Index" class="btn btn-primary">
                                        <i class="fas fa-list me-1"></i>
                                        جميع المنتجات
                                    </a>
                                    <a asp-action="Alerts" class="btn btn-warning">
                                        <i class="fas fa-bell me-1"></i>
                                        التنبيهات
                                    </a>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-success" onclick="showBulkAddStockModal()">
                                        <i class="fas fa-plus-circle me-1"></i>
                                        إضافة مخزون جماعي
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة مخزون -->
<div class="modal fade" id="addStockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    إضافة مخزون
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form asp-action="AddStock" method="post">
                <div class="modal-body">
                    <input type="hidden" id="modalProductId" name="productId" />
                    
                    <div class="mb-3">
                        <label class="form-label">المنتج:</label>
                        <p id="modalProductName" class="fw-bold text-primary"></p>
                    </div>
                    
                    <div class="mb-3">
                        <label for="modalQuantity" class="form-label">
                            <i class="fas fa-cubes me-1"></i>
                            الكمية المراد إضافتها <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="modalQuantity" name="quantity" 
                                   step="0.01" min="0.01" required placeholder="أدخل الكمية">
                            <span class="input-group-text">وحدة</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>إلغاء
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>إضافة للمخزون
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function showAddStockModal(productId, productName) {
            document.getElementById('modalProductId').value = productId;
            document.getElementById('modalProductName').textContent = productName;
            document.getElementById('modalQuantity').value = '';
            
            var modal = new bootstrap.Modal(document.getElementById('addStockModal'));
            modal.show();
        }
        
        function showBulkAddStockModal() {
            // يمكن تطوير هذه الوظيفة لاحقاً لإضافة مخزون جماعي
            alert('سيتم تطوير هذه الميزة قريباً');
        }
    </script>
}
