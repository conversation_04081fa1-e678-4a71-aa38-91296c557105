@model IEnumerable<MarbleWorkshopSystem.Models.Inventory>

@{
    ViewData["Title"] = "تنبيهات المخزون";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-bell me-2"></i>
                        تنبيهات المخزون
                    </h4>
                </div>

                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }

                    <!-- إحصائيات التنبيهات -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count(i => i.Quantity == 0)</h4>
                                    <p class="mb-0">نفد المخزون</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count(i => i.IsLowStock && i.Quantity > 0)</h4>
                                    <p class="mb-0">مخزون منخفض</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count(i => i.WasteQuantity > 0)</h4>
                                    <p class="mb-0">يحتوي على هالك</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count()</h4>
                                    <p class="mb-0">إجمالي التنبيهات</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    @{
                        var outOfStockItems = Model.Where(i => i.Quantity == 0).ToList();
                        var lowStockItems = Model.Where(i => i.IsLowStock && i.Quantity > 0).ToList();
                        var wasteItems = Model.Where(i => i.WasteQuantity > 0).ToList();
                    }

                    @if (Model.Any())
                    {
                        <!-- تنبيهات نفاد المخزون -->
                        @if (outOfStockItems.Any())
                        {
                            <div class="alert alert-danger">
                                <h5>
                                    <i class="fas fa-times-circle me-2"></i>
                                    تنبيه: منتجات نفد مخزونها (@outOfStockItems.Count منتج)
                                </h5>
                                <div class="row">
                                    @foreach (var item in outOfStockItems.Take(6))
                                    {
                                        <div class="col-md-4 mb-2">
                                            <div class="card border-danger">
                                                <div class="card-body p-2">
                                                    <div class="d-flex align-items-center">
                                                        @if (!string.IsNullOrEmpty(item.Product.ImagePath))
                                                        {
                                                            <img src="@item.Product.ImagePath" alt="@item.Product.Name" 
                                                                 class="img-thumbnail me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                                        }
                                                        <div class="flex-grow-1">
                                                            <small class="fw-bold">@item.Product.Name</small>
                                                            <br><small class="text-muted">@item.Product.Category</small>
                                                        </div>
                                                        <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-danger">
                                                            <i class="fas fa-plus"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                                @if (outOfStockItems.Count > 6)
                                {
                                    <p class="mb-0 mt-2">
                                        <a asp-action="LowStock" class="btn btn-sm btn-outline-danger">
                                            عرض جميع المنتجات (@outOfStockItems.Count)
                                        </a>
                                    </p>
                                }
                            </div>
                        }

                        <!-- تنبيهات المخزون المنخفض -->
                        @if (lowStockItems.Any())
                        {
                            <div class="alert alert-warning">
                                <h5>
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    تحذير: منتجات مخزونها منخفض (@lowStockItems.Count منتج)
                                </h5>
                                <div class="row">
                                    @foreach (var item in lowStockItems.Take(6))
                                    {
                                        <div class="col-md-4 mb-2">
                                            <div class="card border-warning">
                                                <div class="card-body p-2">
                                                    <div class="d-flex align-items-center">
                                                        @if (!string.IsNullOrEmpty(item.Product.ImagePath))
                                                        {
                                                            <img src="@item.Product.ImagePath" alt="@item.Product.Name" 
                                                                 class="img-thumbnail me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                                        }
                                                        <div class="flex-grow-1">
                                                            <small class="fw-bold">@item.Product.Name</small>
                                                            <br><small class="text-muted">متبقي: @item.Quantity.ToString("F1")</small>
                                                        </div>
                                                        <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-warning">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                                @if (lowStockItems.Count > 6)
                                {
                                    <p class="mb-0 mt-2">
                                        <a asp-action="LowStock" class="btn btn-sm btn-outline-warning">
                                            عرض جميع المنتجات (@lowStockItems.Count)
                                        </a>
                                    </p>
                                }
                            </div>
                        }

                        <!-- تنبيهات الهالك -->
                        @if (wasteItems.Any())
                        {
                            <div class="alert alert-info">
                                <h5>
                                    <i class="fas fa-trash me-2"></i>
                                    معلومات: منتجات تحتوي على هالك (@wasteItems.Count منتج)
                                </h5>
                                <div class="row">
                                    @foreach (var item in wasteItems.Take(6))
                                    {
                                        <div class="col-md-4 mb-2">
                                            <div class="card border-info">
                                                <div class="card-body p-2">
                                                    <div class="d-flex align-items-center">
                                                        @if (!string.IsNullOrEmpty(item.Product.ImagePath))
                                                        {
                                                            <img src="@item.Product.ImagePath" alt="@item.Product.Name" 
                                                                 class="img-thumbnail me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                                        }
                                                        <div class="flex-grow-1">
                                                            <small class="fw-bold">@item.Product.Name</small>
                                                            <br><small class="text-muted">هالك: @item.WasteQuantity.ToString("F1")</small>
                                                        </div>
                                                        <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                                @if (wasteItems.Count > 6)
                                {
                                    <p class="mb-0 mt-2">
                                        <a asp-action="Index" class="btn btn-sm btn-outline-info">
                                            عرض جميع المنتجات (@wasteItems.Count)
                                        </a>
                                    </p>
                                }
                            </div>
                        }

                        <!-- جدول تفصيلي للتنبيهات -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-table me-2"></i>
                                    تفاصيل التنبيهات
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>المنتج</th>
                                                <th>الكمية الحالية</th>
                                                <th>الحد الأدنى</th>
                                                <th>الهالك</th>
                                                <th>نوع التنبيه</th>
                                                <th>الأولوية</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var item in Model.OrderBy(i => i.Quantity).ThenByDescending(i => i.WasteQuantity))
                                            {
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            @if (!string.IsNullOrEmpty(item.Product.ImagePath))
                                                            {
                                                                <img src="@item.Product.ImagePath" alt="@item.Product.Name" 
                                                                     class="img-thumbnail me-2" style="width: 25px; height: 25px; object-fit: cover;">
                                                            }
                                                            <div>
                                                                <small class="fw-bold">@item.Product.Name</small>
                                                                <br><small class="text-muted">@item.Product.Category</small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge @(item.Quantity == 0 ? "bg-danger" : item.IsLowStock ? "bg-warning" : "bg-success") fs-6">
                                                            @item.Quantity.ToString("F1")
                                                        </span>
                                                    </td>
                                                    <td>@item.MinimumStock.ToString("F1")</td>
                                                    <td>
                                                        @if (item.WasteQuantity > 0)
                                                        {
                                                            <span class="badge bg-secondary fs-6">@item.WasteQuantity.ToString("F1")</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">-</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        @if (item.Quantity == 0)
                                                        {
                                                            <span class="badge bg-danger">نفد المخزون</span>
                                                        }
                                                        else if (item.IsLowStock)
                                                        {
                                                            <span class="badge bg-warning">مخزون منخفض</span>
                                                        }
                                                        @if (item.WasteQuantity > 0)
                                                        {
                                                            <span class="badge bg-info">يحتوي هالك</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        @if (item.Quantity == 0)
                                                        {
                                                            <span class="badge bg-danger">عالية</span>
                                                        }
                                                        else if (item.IsLowStock)
                                                        {
                                                            <span class="badge bg-warning">متوسطة</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="badge bg-info">منخفضة</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-warning">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a asp-controller="Products" asp-action="Details" asp-route-id="@item.ProductId" 
                                                               class="btn btn-sm btn-info">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                            <h4 class="text-success mt-3">ممتاز! لا توجد تنبيهات</h4>
                            <p class="text-muted">جميع المنتجات في حالة جيدة</p>
                            <a asp-action="Index" class="btn btn-primary">
                                <i class="fas fa-list me-1"></i>
                                عرض جميع المنتجات
                            </a>
                        </div>
                    }

                    <!-- أزرار الإجراءات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <div>
                                    <a asp-action="Index" class="btn btn-primary">
                                        <i class="fas fa-list me-1"></i>
                                        جميع المنتجات
                                    </a>
                                    <a asp-action="LowStock" class="btn btn-danger">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        المخزون المنخفض
                                    </a>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-success" onclick="window.location.reload()">
                                        <i class="fas fa-sync me-1"></i>
                                        تحديث التنبيهات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
