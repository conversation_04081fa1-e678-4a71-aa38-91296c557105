@model IEnumerable<MarbleWorkshopSystem.Models.PurchaseInvoice>

@{
    ViewData["Title"] = "فواتير المشتريات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-shopping-bag me-2"></i>
                        فواتير المشتريات
                    </h3>
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        فاتورة جديدة
                    </a>
                </div>

                <div class="card-body">
                    <!-- Filter Section -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <form asp-action="Index" method="get" class="row g-3">
                                <div class="col-md-2">
                                    <label class="form-label">الحالة</label>
                                    <select name="status" class="form-select">
                                        <option value="">جميع الحالات</option>
                                        <option value="مسودة" selected="@(ViewBag.Status == "مسودة")">مسودة</option>
                                        <option value="مؤكدة" selected="@(ViewBag.Status == "مؤكدة")">مؤكدة</option>
                                        <option value="ملغية" selected="@(ViewBag.Status == "ملغية")">ملغية</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">من تاريخ</label>
                                    <input type="date" name="fromDate" class="form-control" value="@ViewBag.FromDate">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">إلى تاريخ</label>
                                    <input type="date" name="toDate" class="form-control" value="@ViewBag.ToDate">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">البحث</label>
                                    <input type="text" name="searchTerm" class="form-control" value="@ViewBag.SearchTerm" 
                                           placeholder="رقم الفاتورة أو اسم المورد...">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-outline-primary">
                                            <i class="fas fa-filter me-1"></i>
                                            تصفية
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count()</h4>
                                    <p class="mb-0">إجمالي الفواتير</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Where(i => i.Status == "مؤكدة").Sum(i => i.TotalAmount).ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h4>
                                    <p class="mb-0">إجمالي المشتريات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count(i => i.Status == "مسودة")</h4>
                                    <p class="mb-0">فواتير معلقة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4>@(Model.Where(i => i.Status == "مؤكدة").Any() ? Model.Where(i => i.Status == "مؤكدة").Average(i => i.TotalAmount).ToString("C", new System.Globalization.CultureInfo("ar-EG")) : "0")</h4>
                                    <p class="mb-0">متوسط الفاتورة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Invoices Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>التاريخ</th>
                                    <th>المورد</th>
                                    <th>المجموع الفرعي</th>
                                    <th>الخصم</th>
                                    <th>المجموع النهائي</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            <strong>@item.InvoiceNumber</strong>
                                        </td>
                                        <td>
                                            <i class="fas fa-calendar me-1"></i>
                                            @item.InvoiceDate.ToString("dd/MM/yyyy")
                                        </td>
                                        <td>
                                            <i class="fas fa-truck me-1"></i>
                                            @item.Supplier.Name
                                        </td>
                                        <td>
                                            @item.SubTotal.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                                        </td>
                                        <td>
                                            @if (item.DiscountValue > 0)
                                            {
                                                <span class="text-warning">
                                                    @item.DiscountAmount.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">لا يوجد</span>
                                            }
                                        </td>
                                        <td>
                                            <strong class="text-success">
                                                @item.TotalAmount.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                                            </strong>
                                        </td>
                                        <td>
                                            @switch (item.Status)
                                            {
                                                case "مسودة":
                                                    <span class="badge bg-warning">مسودة</span>
                                                    break;
                                                case "مؤكدة":
                                                    <span class="badge bg-success">مؤكدة</span>
                                                    break;
                                                case "ملغية":
                                                    <span class="badge bg-danger">ملغية</span>
                                                    break;
                                                default:
                                                    <span class="badge bg-secondary">@item.Status</span>
                                                    break;
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Print" asp-route-id="@item.Id"
                                                   class="btn btn-sm btn-outline-primary" title="طباعة" target="_blank">
                                                    <i class="fas fa-print"></i>
                                                </a>
                                                <a asp-controller="Reports" asp-action="PrintPurchaseInvoice" asp-route-id="@item.Id"
                                                   class="btn btn-sm btn-outline-danger" title="تحميل PDF" target="_blank">
                                                    <i class="fas fa-file-pdf"></i>
                                                </a>
                                                @if (item.Status == "مسودة")
                                                {
                                                    <a asp-action="Edit" asp-route-id="@item.Id" 
                                                       class="btn btn-sm btn-outline-warning" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                }
                                                <a asp-action="Delete" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if (!Model.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد فواتير مشتريات</h5>
                            <p class="text-muted">ابدأ بإنشاء فواتير المشتريات لتتبع مشترياتك</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                إنشاء أول فاتورة
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // عرض رسائل النجاح والخطأ
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
                toastr.success('@TempData["SuccessMessage"]');
            </text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>
                toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
    </script>
}
