@model IEnumerable<MarbleWorkshopSystem.Models.Product>

@{
    ViewData["Title"] = "إدارة المنتجات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-cube me-2"></i>
                        إدارة المنتجات والخامات
                    </h3>
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        إضافة منتج جديد
                    </a>
                </div>

                <div class="card-body">
                    <!-- Search and Filter Section -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form asp-action="Index" method="get" class="d-flex">
                                <input type="text" name="searchTerm" value="@ViewBag.SearchTerm" 
                                       class="form-control me-2" placeholder="البحث في المنتجات...">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <form asp-action="Index" method="get">
                                <select name="category" class="form-select" onchange="this.form.submit()">
                                    <option value="">جميع الفئات</option>
                                    @if (ViewBag.Categories != null)
                                    {
                                        @foreach (var category in ViewBag.Categories as IEnumerable<string>)
                                        {
                                            <option value="@category" selected="@(ViewBag.SelectedCategory == category)">
                                                @category
                                            </option>
                                        }
                                    }
                                </select>
                            </form>
                        </div>
                    </div>

                    <!-- Products Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الصورة</th>
                                    <th>اسم المنتج</th>
                                    <th>الفئة</th>
                                    <th>الأبعاد (طول × عرض)</th>
                                    <th>المساحة (م²)</th>
                                    <th>سعر الوحدة</th>
                                    <th>المخزون</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.ImagePath))
                                            {
                                                <img src="@item.ImagePath" alt="@item.Name" 
                                                     class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                            }
                                            else
                                            {
                                                <div class="bg-light d-flex align-items-center justify-content-center" 
                                                     style="width: 50px; height: 50px;">
                                                    <i class="fas fa-image text-muted"></i>
                                                </div>
                                            }
                                        </td>
                                        <td>
                                            <strong>@item.Name</strong>
                                            @if (!string.IsNullOrEmpty(item.Description))
                                            {
                                                <br><small class="text-muted">@item.Description</small>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge bg-info">@item.Category</span>
                                        </td>
                                        <td>
                                            @item.Length.ToString("F2") × @item.Width.ToString("F2") م
                                        </td>
                                        <td>
                                            <strong>@item.Area.ToString("F2")</strong> م²
                                        </td>
                                        <td>
                                            @item.UnitPrice.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                                        </td>
                                        <td>
                                            @if (item.Inventory != null)
                                            {
                                                <span class="badge @(item.Inventory.IsLowStock ? "bg-danger" : "bg-success")">
                                                    @item.Inventory.Quantity.ToString("F2")
                                                </span>
                                                @if (item.Inventory.IsLowStock)
                                                {
                                                    <i class="fas fa-exclamation-triangle text-warning ms-1" 
                                                       title="مخزون منخفض"></i>
                                                }
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">غير محدد</span>
                                            }
                                        </td>
                                        <td>
                                            @if (item.IsActive)
                                            {
                                                <span class="badge bg-success">نشط</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">غير نشط</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a asp-action="Details" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a asp-action="Edit" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a asp-action="Delete" asp-route-id="@item.Id" 
                                                   class="btn btn-sm btn-outline-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    @if (!Model.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-cube fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد منتجات</h5>
                            <p class="text-muted">ابدأ بإضافة منتجات جديدة لإدارة مخزونك</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                إضافة أول منتج
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // عرض رسائل النجاح والخطأ
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
                toastr.success('@TempData["SuccessMessage"]');
            </text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>
                toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
    </script>
}
