@model MarbleWorkshopSystem.Models.Product

@{
    ViewData["Title"] = "تفاصيل المنتج";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        تفاصيل المنتج: @Model.Name
                    </h3>
                </div>

                <div class="card-body">
                    <div class="row">
                        <!-- معلومات المنتج -->
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">اسم المنتج:</label>
                                        <p class="form-control-plaintext">@Model.Name</p>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label fw-bold">الفئة:</label>
                                        <p class="form-control-plaintext">
                                            <span class="badge bg-info fs-6">@Model.Category</span>
                                        </p>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label fw-bold">الوصف:</label>
                                        <p class="form-control-plaintext">
                                            @if (!string.IsNullOrEmpty(Model.Description))
                                            {
                                                @Model.Description
                                            }
                                            else
                                            {
                                                <span class="text-muted">لا يوجد وصف</span>
                                            }
                                        </p>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">الأبعاد:</label>
                                        <p class="form-control-plaintext">
                                            <i class="fas fa-ruler me-1"></i>
                                            @Model.Length.ToString("F2") × @Model.Width.ToString("F2") متر
                                        </p>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label fw-bold">المساحة:</label>
                                        <p class="form-control-plaintext">
                                            <i class="fas fa-expand-arrows-alt me-1"></i>
                                            <strong class="text-primary">@Model.Area.ToString("F2") متر مربع</strong>
                                        </p>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label fw-bold">سعر الوحدة:</label>
                                        <p class="form-control-plaintext">
                                            <i class="fas fa-money-bill-wave me-1"></i>
                                            <strong class="text-success">@Model.UnitPrice.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</strong>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات المخزون -->
                            @if (Model.Inventory != null)
                            {
                                <div class="card mt-4">
                                    <div class="card-header bg-light">
                                        <h5 class="card-title mb-0">
                                            <i class="fas fa-warehouse me-2"></i>
                                            معلومات المخزون
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="text-center">
                                                    <h6 class="text-muted">الكمية المتوفرة</h6>
                                                    <h4 class="@(Model.Inventory.IsLowStock ? "text-danger" : "text-success")">
                                                        @Model.Inventory.Quantity.ToString("F2")
                                                        @if (Model.Inventory.IsLowStock)
                                                        {
                                                            <i class="fas fa-exclamation-triangle ms-1" title="مخزون منخفض"></i>
                                                        }
                                                    </h4>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="text-center">
                                                    <h6 class="text-muted">الحد الأدنى</h6>
                                                    <h4 class="text-warning">@Model.Inventory.MinimumStock.ToString("F2")</h4>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="text-center">
                                                    <h6 class="text-muted">الهالك</h6>
                                                    <h4 class="text-danger">@Model.Inventory.WasteQuantity.ToString("F2")</h4>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="text-center">
                                                    <h6 class="text-muted">المتاح للبيع</h6>
                                                    <h4 class="text-info">@Model.Inventory.AvailableQuantity.ToString("F2")</h4>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }

                            <!-- معلومات إضافية -->
                            <div class="card mt-4">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-info me-2"></i>
                                        معلومات إضافية
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>تاريخ الإنشاء:</strong> @Model.CreatedDate.ToString("dd/MM/yyyy HH:mm")</p>
                                            <p><strong>آخر تحديث:</strong> @Model.UpdatedDate.ToString("dd/MM/yyyy HH:mm")</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>الحالة:</strong> 
                                                @if (Model.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                }
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- صورة المنتج -->
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-image me-2"></i>
                                        صورة المنتج
                                    </h5>
                                </div>
                                <div class="card-body text-center">
                                    @if (!string.IsNullOrEmpty(Model.ImagePath))
                                    {
                                        <img src="@Model.ImagePath" alt="@Model.Name" 
                                             class="img-fluid rounded shadow" style="max-height: 300px;">
                                    }
                                    else
                                    {
                                        <div class="bg-light d-flex align-items-center justify-content-center rounded" 
                                             style="height: 200px;">
                                            <div class="text-center">
                                                <i class="fas fa-image fa-3x text-muted mb-2"></i>
                                                <p class="text-muted">لا توجد صورة</p>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <a asp-action="Index" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    العودة للقائمة
                                </a>
                                <div>
                                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning me-2">
                                        <i class="fas fa-edit me-1"></i>
                                        تعديل
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                                        <i class="fas fa-trash me-1"></i>
                                        حذف
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
