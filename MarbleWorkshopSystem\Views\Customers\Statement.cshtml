@model MarbleWorkshopSystem.Models.Customer

@{
    ViewData["Title"] = "كشف حساب العميل";
    var invoices = ViewBag.Invoices as IEnumerable<MarbleWorkshopSystem.Models.SalesInvoice>;
    var receipts = ViewBag.Receipts as IEnumerable<MarbleWorkshopSystem.Models.Receipt>;
    var totalSales = (decimal)(ViewBag.TotalSales ?? 0);
    var totalPayments = (decimal)(ViewBag.TotalPayments ?? 0);
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-file-invoice me-2"></i>
                        كشف حساب العميل - @Model.Name
                    </h3>
                    <div>
                        <button type="button" class="btn btn-info me-2" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>
                            طباعة
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- معلومات العميل -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-user me-2"></i>
                                        معلومات العميل
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>الاسم:</strong></td>
                                            <td>@Model.Name</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الهاتف:</strong></td>
                                            <td>@Model.Phone</td>
                                        </tr>
                                        <tr>
                                            <td><strong>العنوان:</strong></td>
                                            <td>@Model.Address</td>
                                        </tr>
                                        <tr>
                                            <td><strong>البريد الإلكتروني:</strong></td>
                                            <td>@Model.Email</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-line me-2"></i>
                                        ملخص الحساب
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>إجمالي المبيعات:</strong></td>
                                            <td class="text-success">@totalSales.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</td>
                                        </tr>
                                        <tr>
                                            <td><strong>إجمالي المدفوعات:</strong></td>
                                            <td class="text-info">@totalPayments.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</td>
                                        </tr>
                                        <tr class="border-top">
                                            <td><strong>الرصيد الحالي:</strong></td>
                                            <td class="@(Model.Balance >= 0 ? "text-success" : "text-danger")">
                                                <strong>@Model.Balance.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</strong>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تبويبات الحركات -->
                    <ul class="nav nav-tabs" id="statementTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="invoices-tab" data-bs-toggle="tab" data-bs-target="#invoices" type="button" role="tab">
                                <i class="fas fa-file-invoice me-1"></i>
                                الفواتير (@(invoices?.Count() ?? 0))
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="receipts-tab" data-bs-toggle="tab" data-bs-target="#receipts" type="button" role="tab">
                                <i class="fas fa-receipt me-1"></i>
                                سندات القبض (@(receipts?.Count() ?? 0))
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content mt-3" id="statementTabContent">
                        <!-- تبويب الفواتير -->
                        <div class="tab-pane fade show active" id="invoices" role="tabpanel">
                            @if (invoices != null && invoices.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>رقم الفاتورة</th>
                                                <th>التاريخ</th>
                                                <th>المبلغ</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var invoice in invoices)
                                            {
                                                <tr>
                                                    <td>
                                                        <strong class="text-primary">@invoice.InvoiceNumber</strong>
                                                    </td>
                                                    <td>@invoice.InvoiceDate.ToString("dd/MM/yyyy")</td>
                                                    <td>
                                                        <span class="badge bg-success">
                                                            @invoice.TotalAmount.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge @(invoice.Status == "مؤكدة" ? "bg-success" : "bg-warning")">
                                                            @invoice.Status
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <a asp-controller="Sales" asp-action="Details" asp-route-id="@invoice.Id" 
                                                           class="btn btn-sm btn-outline-info" title="عرض الفاتورة">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد فواتير لهذا العميل</h5>
                                </div>
                            }
                        </div>

                        <!-- تبويب سندات القبض -->
                        <div class="tab-pane fade" id="receipts" role="tabpanel">
                            @if (receipts != null && receipts.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>رقم السند</th>
                                                <th>التاريخ</th>
                                                <th>المبلغ</th>
                                                <th>طريقة الدفع</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var receipt in receipts)
                                            {
                                                <tr>
                                                    <td>
                                                        <strong class="text-primary">@receipt.ReceiptNumber</strong>
                                                    </td>
                                                    <td>@receipt.ReceiptDate.ToString("dd/MM/yyyy")</td>
                                                    <td>
                                                        <span class="badge bg-info">
                                                            @receipt.Amount.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary">@receipt.PaymentMethod</span>
                                                    </td>
                                                    <td>
                                                        <a asp-controller="Receipts" asp-action="Details" asp-route-id="@receipt.Id" 
                                                           class="btn btn-sm btn-outline-info" title="عرض السند">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="text-center py-4">
                                    <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد سندات قبض لهذا العميل</h5>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // عرض رسائل النجاح والخطأ
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
                toastr.success('@TempData["SuccessMessage"]');
            </text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>
                toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
    </script>
}
