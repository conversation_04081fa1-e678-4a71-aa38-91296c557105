﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MarbleWorkshopSystem.Migrations
{
    /// <inheritdoc />
    public partial class AddLengthWidthToInvoiceItems : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "Length",
                table: "SalesInvoiceItems",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "Notes",
                table: "SalesInvoiceItems",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Width",
                table: "SalesInvoiceItems",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "Length",
                table: "PurchaseInvoiceItems",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "Notes",
                table: "PurchaseInvoiceItems",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Width",
                table: "PurchaseInvoiceItems",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Length",
                table: "SalesInvoiceItems");

            migrationBuilder.DropColumn(
                name: "Notes",
                table: "SalesInvoiceItems");

            migrationBuilder.DropColumn(
                name: "Width",
                table: "SalesInvoiceItems");

            migrationBuilder.DropColumn(
                name: "Length",
                table: "PurchaseInvoiceItems");

            migrationBuilder.DropColumn(
                name: "Notes",
                table: "PurchaseInvoiceItems");

            migrationBuilder.DropColumn(
                name: "Width",
                table: "PurchaseInvoiceItems");
        }
    }
}
