@echo off
chcp 65001 > nul
title نظام إدارة ورشة الرخام - Marble Workshop System

echo ========================================
echo    نظام إدارة ورشة الرخام
echo    Marble Workshop Management System
echo ========================================
echo.

REM التحقق من وجود .NET Runtime
echo جاري التحقق من متطلبات النظام...
echo Checking system requirements...

dotnet --version > nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo [خطأ] .NET 8.0 Runtime غير مثبت على النظام
    echo [ERROR] .NET 8.0 Runtime is not installed
    echo.
    echo يرجى تحميل وتثبيت .NET 8.0 Runtime من:
    echo Please download and install .NET 8.0 Runtime from:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    pause
    exit /b 1
)

REM التحقق من إصدار .NET
for /f "tokens=1" %%i in ('dotnet --version') do set dotnet_version=%%i
echo تم العثور على .NET إصدار: %dotnet_version%
echo Found .NET version: %dotnet_version%

REM التحقق من قاعدة البيانات
echo.
echo جاري التحقق من قاعدة البيانات...
echo Checking database connection...

REM بدء التطبيق
echo.
echo جاري بدء تشغيل النظام...
echo Starting the system...
echo.
echo سيتم فتح المتصفح تلقائياً على العنوان:
echo The browser will open automatically at:
echo http://localhost:5000
echo.
echo للإيقاف اضغط Ctrl+C
echo To stop, press Ctrl+C
echo.

REM تشغيل التطبيق
REM التحقق من وجود ملف exe (النسخة المنشورة)
if exist "MarbleWorkshopSystem.exe" (
    echo تشغيل النسخة المنشورة...
    echo Running published version...
    .\MarbleWorkshopSystem.exe --urls "http://localhost:5000;https://localhost:5001"
) else (
    echo تشغيل نسخة التطوير...
    echo Running development version...
    dotnet run --urls "http://localhost:5000;https://localhost:5001"
)

if %errorlevel% neq 0 (
    echo.
    echo [خطأ] فشل في تشغيل التطبيق
    echo [ERROR] Failed to start the application
    echo.
    echo تحقق من:
    echo Please check:
    echo 1. اتصال قاعدة البيانات / Database connection
    echo 2. صلاحيات المجلد / Folder permissions
    echo 3. منافذ الشبكة / Network ports
    echo.
    pause
    exit /b 1
)

pause
