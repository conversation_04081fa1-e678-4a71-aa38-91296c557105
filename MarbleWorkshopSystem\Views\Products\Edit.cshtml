@model MarbleWorkshopSystem.Models.Product

@{
    ViewData["Title"] = "تعديل المنتج";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>
                        تعديل المنتج: @Model.Name
                    </h3>
                </div>

                <div class="card-body">
                    <form asp-action="Edit" method="post" enctype="multipart/form-data">
                        <input type="hidden" asp-for="Id" />
                        <input type="hidden" asp-for="ImagePath" />
                        <input type="hidden" asp-for="CreatedDate" />
                        <input type="hidden" asp-for="IsActive" />
                        
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <div class="row">
                            <!-- معلومات أساسية -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Name" class="form-label">اسم المنتج *</label>
                                    <input asp-for="Name" class="form-control" placeholder="أدخل اسم المنتج">
                                    <span asp-validation-for="Name" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="Description" class="form-label">الوصف</label>
                                    <textarea asp-for="Description" class="form-control" rows="3" 
                                              placeholder="وصف المنتج (اختياري)"></textarea>
                                    <span asp-validation-for="Description" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="Category" class="form-label">الفئة *</label>
                                    <input asp-for="Category" class="form-control" list="categories" 
                                           placeholder="أدخل أو اختر فئة المنتج">
                                    <datalist id="categories">
                                        @if (ViewBag.Categories != null)
                                        {
                                            @foreach (var category in ViewBag.Categories as IEnumerable<string>)
                                            {
                                                <option value="@category"></option>
                                            }
                                        }
                                    </datalist>
                                    <span asp-validation-for="Category" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="UnitPrice" class="form-label">سعر الوحدة (جنيه) *</label>
                                    <input asp-for="UnitPrice" class="form-control" type="number" step="0.01" min="0"
                                           placeholder="0.00">
                                    <span asp-validation-for="UnitPrice" class="text-danger"></span>
                                </div>
                            </div>

                            <!-- الأبعاد والصورة -->
                            <div class="col-md-6">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="Length" class="form-label">الطول (متر) *</label>
                                            <input asp-for="Length" class="form-control" type="number" step="0.01" min="0.01"
                                                   id="lengthInput" placeholder="0.00">
                                            <span asp-validation-for="Length" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label asp-for="Width" class="form-label">العرض (متر) *</label>
                                            <input asp-for="Width" class="form-control" type="number" step="0.01" min="0.01"
                                                   id="widthInput" placeholder="0.00">
                                            <span asp-validation-for="Width" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">المساحة (متر مربع)</label>
                                    <input type="text" class="form-control bg-light" id="areaDisplay" readonly 
                                           placeholder="سيتم حسابها تلقائياً">
                                    <small class="form-text text-muted">
                                        المساحة = الطول × العرض
                                    </small>
                                </div>

                                <!-- الصورة الحالية -->
                                @if (!string.IsNullOrEmpty(Model.ImagePath))
                                {
                                    <div class="mb-3">
                                        <label class="form-label">الصورة الحالية</label>
                                        <div class="text-center">
                                            <img src="@Model.ImagePath" alt="@Model.Name" 
                                                 class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                                        </div>
                                    </div>
                                }

                                <div class="mb-3">
                                    <label class="form-label">تغيير صورة المنتج</label>
                                    <input type="file" name="imageFile" class="form-control" accept="image/*"
                                           id="imageInput">
                                    <small class="form-text text-muted">
                                        اختر صورة جديدة للمنتج (JPG, PNG, GIF) - اتركها فارغة للاحتفاظ بالصورة الحالية
                                    </small>
                                </div>

                                <!-- معاينة الصورة الجديدة -->
                                <div class="mb-3">
                                    <div id="imagePreview" class="text-center" style="display: none;">
                                        <label class="form-label">معاينة الصورة الجديدة</label>
                                        <div>
                                            <img id="previewImg" src="#" alt="معاينة الصورة" 
                                                 class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        العودة للقائمة
                                    </a>
                                    <div>
                                        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info me-2">
                                            <i class="fas fa-eye me-1"></i>
                                            عرض التفاصيل
                                        </a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>
                                            حفظ التغييرات
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // حساب المساحة تلقائياً
            function calculateArea() {
                var length = parseFloat($('#lengthInput').val()) || 0;
                var width = parseFloat($('#widthInput').val()) || 0;
                var area = length * width;
                
                if (area > 0) {
                    $('#areaDisplay').val(area.toFixed(2) + ' متر مربع');
                } else {
                    $('#areaDisplay').val('');
                }
            }

            // ربط الأحداث
            $('#lengthInput, #widthInput').on('input change', calculateArea);

            // معاينة الصورة الجديدة
            $('#imageInput').change(function() {
                var file = this.files[0];
                if (file) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        $('#previewImg').attr('src', e.target.result);
                        $('#imagePreview').show();
                    }
                    reader.readAsDataURL(file);
                } else {
                    $('#imagePreview').hide();
                }
            });

            // حساب المساحة عند تحميل الصفحة
            calculateArea();
        });
    </script>
}
