@model MarbleWorkshopSystem.ViewModels.LoginViewModel

@{
    ViewData["Title"] = "تسجيل الدخول";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}

<form asp-action="Login" method="post">
    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert" style="display: none;"></div>

    <div class="mb-4">
        <label asp-for="Username" class="form-label fw-semibold">
            <i class="fas fa-user me-2 text-primary"></i>
            اسم المستخدم
        </label>
        <input asp-for="Username" class="form-control form-control-lg"
               placeholder="أدخل اسم المستخدم"
               autocomplete="username" />
        <span asp-validation-for="Username" class="text-danger"></span>
    </div>

    <div class="mb-4">
        <label asp-for="Password" class="form-label fw-semibold">
            <i class="fas fa-lock me-2 text-primary"></i>
            كلمة المرور
        </label>
        <div class="position-relative">
            <input asp-for="Password" class="form-control form-control-lg"
                   placeholder="أدخل كلمة المرور"
                   autocomplete="current-password"
                   id="passwordInput" />
            <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y me-2"
                    onclick="togglePassword()" style="border: none; background: none;">
                <i class="fas fa-eye" id="passwordToggleIcon"></i>
            </button>
        </div>
        <span asp-validation-for="Password" class="text-danger"></span>
    </div>

    <div class="mb-4">
        <div class="form-check">
            <input asp-for="RememberMe" class="form-check-input" id="rememberMe" />
            <label asp-for="RememberMe" class="form-check-label" for="rememberMe">
                <i class="fas fa-clock me-1"></i>
                تذكرني لمدة 30 يوماً
            </label>
        </div>
    </div>

    <div class="d-grid mb-3">
        <button type="submit" class="btn btn-login btn-lg text-white">
            <i class="fas fa-sign-in-alt me-2"></i>
            تسجيل الدخول
        </button>
    </div>

    <!-- معلومات تجريبية -->
    <div class="alert alert-info" role="alert">
        <i class="fas fa-info-circle me-2"></i>
        <strong>بيانات تجريبية:</strong><br>
        <small>
            اسم المستخدم: admin<br>
            كلمة المرور: Admin123!
        </small>
    </div>
</form>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        // إظهار/إخفاء كلمة المرور
        function togglePassword() {
            const passwordInput = document.getElementById('passwordInput');
            const toggleIcon = document.getElementById('passwordToggleIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // تحسين عرض رسائل الخطأ
        document.addEventListener('DOMContentLoaded', function() {
            const validationSummary = document.querySelector('[data-valmsg-summary="true"]');
            if (validationSummary && validationSummary.innerHTML.trim()) {
                validationSummary.style.display = 'block';
            }

            // تركيز على حقل اسم المستخدم
            const usernameInput = document.querySelector('input[name="Username"]');
            if (usernameInput) {
                usernameInput.focus();
            }
        });

        // تأثيرات بصرية للنموذج
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('focused');
            });
        });
    </script>
}
