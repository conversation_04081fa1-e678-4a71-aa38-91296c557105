using Microsoft.EntityFrameworkCore;
using MarbleWorkshopSystem.Data;
using MarbleWorkshopSystem.Models;

namespace MarbleWorkshopSystem.Services
{
    public class InventoryService : IInventoryService
    {
        private readonly IRepository<Inventory> _inventoryRepository;
        private readonly ApplicationDbContext _context;

        public InventoryService(IRepository<Inventory> inventoryRepository, ApplicationDbContext context)
        {
            _inventoryRepository = inventoryRepository;
            _context = context;
        }

        public async Task<IEnumerable<Inventory>> GetAllInventoryAsync()
        {
            return await _context.Inventories
                .Include(i => i.Product)
                .OrderBy(i => i.Product.Name)
                .ToListAsync();
        }

        public async Task<Inventory?> GetInventoryByIdAsync(int id)
        {
            return await _context.Inventories
                .Include(i => i.Product)
                .FirstOrDefaultAsync(i => i.Id == id);
        }

        public async Task<Inventory?> GetInventoryByProductIdAsync(int productId)
        {
            return await _context.Inventories
                .Include(i => i.Product)
                .FirstOrDefaultAsync(i => i.ProductId == productId);
        }

        public async Task<IEnumerable<Inventory>> GetLowStockItemsAsync()
        {
            return await _context.Inventories
                .Include(i => i.Product)
                .Where(i => i.Quantity <= i.MinimumStock && i.Product.IsActive)
                .OrderBy(i => i.Product.Name)
                .ToListAsync();
        }

        public async Task<Inventory> UpdateInventoryAsync(Inventory inventory)
        {
            inventory.LastUpdated = DateTime.Now;
            var updatedInventory = await _inventoryRepository.UpdateAsync(inventory);
            await _inventoryRepository.SaveChangesAsync();
            return updatedInventory;
        }

        public async Task AddStockAsync(int productId, decimal quantity)
        {
            var inventory = await GetInventoryByProductIdAsync(productId);
            if (inventory != null)
            {
                inventory.Quantity += quantity;
                await UpdateInventoryAsync(inventory);
            }
        }

        public async Task ReduceStockAsync(int productId, decimal quantity)
        {
            var inventory = await GetInventoryByProductIdAsync(productId);
            if (inventory != null)
            {
                if (inventory.Quantity >= quantity)
                {
                    inventory.Quantity -= quantity;
                    await UpdateInventoryAsync(inventory);
                }
                else
                {
                    throw new InvalidOperationException($"المخزون غير كافي. المتوفر: {inventory.Quantity}, المطلوب: {quantity}");
                }
            }
        }

        public async Task AddWasteAsync(int productId, decimal wasteQuantity)
        {
            var inventory = await GetInventoryByProductIdAsync(productId);
            if (inventory != null)
            {
                inventory.WasteQuantity += wasteQuantity;
                await UpdateInventoryAsync(inventory);
            }
        }

        public async Task<bool> HasSufficientStockAsync(int productId, decimal requiredQuantity)
        {
            var inventory = await GetInventoryByProductIdAsync(productId);
            return inventory != null && inventory.AvailableQuantity >= requiredQuantity;
        }

        public async Task<decimal> GetAvailableStockAsync(int productId)
        {
            var inventory = await GetInventoryByProductIdAsync(productId);
            return inventory?.AvailableQuantity ?? 0;
        }

        public async Task<IEnumerable<Inventory>> GetInventoryAlertsAsync()
        {
            return await _context.Inventories
                .Include(i => i.Product)
                .Where(i => i.Quantity <= i.MinimumStock && i.Product.IsActive)
                .OrderBy(i => i.Quantity)
                .ToListAsync();
        }
    }
}
