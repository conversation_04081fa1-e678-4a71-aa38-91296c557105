@echo off
chcp 65001 > nul
title فحص التوافق مع ويندوز 10

echo ========================================
echo    فحص التوافق مع ويندوز 10
echo    Windows 10 Compatibility Check
echo ========================================
echo.

:: التحقق من إصدار ويندوز
echo 1. فحص إصدار ويندوز...
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo    إصدار ويندوز: %VERSION%

if "%VERSION%" geq "10.0" (
    echo    ✓ ويندوز 10 أو أحدث - متوافق
) else (
    echo    ⚠ إصدار أقدم من ويندوز 10 - قد تواجه مشاكل
)
echo.

:: التحقق من معمارية النظام
echo 2. فحص معمارية النظام...
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    echo    ✓ نظام 64-bit - متوافق
) else if "%PROCESSOR_ARCHITECTURE%"=="x86" (
    echo    ⚠ نظام 32-bit - قد تحتاج نسخة x86
) else (
    echo    ⚠ معمارية غير معروفة: %PROCESSOR_ARCHITECTURE%
)
echo.

:: التحقق من .NET Runtime
echo 3. فحص .NET Runtime...
dotnet --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f %%i in ('dotnet --version 2^>nul') do set DOTNET_VERSION=%%i
    echo    ✓ .NET Runtime مثبت: %DOTNET_VERSION%
    
    :: التحقق من إصدار .NET
    echo %DOTNET_VERSION% | findstr /r "^8\." >nul
    if %errorlevel% equ 0 (
        echo    ✓ .NET 8.0 متوفر - متوافق تماماً
    ) else (
        echo %DOTNET_VERSION% | findstr /r "^[6-9]\." >nul
        if %errorlevel% equ 0 (
            echo    ✓ .NET حديث متوفر - متوافق
        ) else (
            echo    ⚠ إصدار .NET قديم - يُنصح بالتحديث
        )
    )
) else (
    echo    ✗ .NET Runtime غير مثبت
    echo    يرجى تحميله من: https://dotnet.microsoft.com/download
)
echo.

:: التحقق من الذاكرة
echo 4. فحص الذاكرة المتاحة...
for /f "skip=1" %%p in ('wmic computersystem get TotalPhysicalMemory') do (
    set /a MEMORY_GB=%%p/1024/1024/1024
    goto :memory_done
)
:memory_done
if %MEMORY_GB% geq 4 (
    echo    ✓ الذاكرة: %MEMORY_GB% GB - كافية
) else if %MEMORY_GB% geq 2 (
    echo    ⚠ الذاكرة: %MEMORY_GB% GB - قد تكون بطيئة
) else (
    echo    ✗ الذاكرة: %MEMORY_GB% GB - غير كافية
)
echo.

:: التحقق من المساحة الحرة
echo 5. فحص المساحة الحرة...
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do set FREE_SPACE=%%a
set /a FREE_SPACE_MB=%FREE_SPACE:~0,-3%/1024/1024
if %FREE_SPACE_MB% geq 1000 (
    echo    ✓ المساحة الحرة: %FREE_SPACE_MB% MB - كافية
) else if %FREE_SPACE_MB% geq 500 (
    echo    ⚠ المساحة الحرة: %FREE_SPACE_MB% MB - محدودة
) else (
    echo    ✗ المساحة الحرة: %FREE_SPACE_MB% MB - غير كافية
)
echo.

:: التحقق من المنافذ
echo 6. فحص توفر المنافذ...
netstat -an | find ":5000 " >nul
if %errorlevel% equ 0 (
    echo    ⚠ المنفذ 5000 مستخدم
) else (
    echo    ✓ المنفذ 5000 متاح
)

netstat -an | find ":5001 " >nul
if %errorlevel% equ 0 (
    echo    ⚠ المنفذ 5001 مستخدم
) else (
    echo    ✓ المنفذ 5001 متاح
)
echo.

:: التحقق من صلاحيات المدير
echo 7. فحص الصلاحيات...
net session >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✓ تعمل بصلاحيات المدير
) else (
    echo    ⚠ لا تعمل بصلاحيات المدير - قد تحتاجها لبعض الميزات
)
echo.

:: التحقق من Windows Defender
echo 8. فحص Windows Defender...
powershell -Command "Get-MpPreference" >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✓ Windows Defender نشط - تأكد من إضافة استثناء للبرنامج
) else (
    echo    ⚠ تعذر فحص Windows Defender
)
echo.

:: التحقق من جدار الحماية
echo 9. فحص جدار الحماية...
netsh advfirewall show allprofiles state | find "ON" >nul
if %errorlevel% equ 0 (
    echo    ✓ جدار الحماية نشط - قد تحتاج لإضافة استثناء
) else (
    echo    ⚠ جدار الحماية معطل أو تعذر فحصه
)
echo.

:: ملخص النتائج
echo ========================================
echo              ملخص النتائج
echo ========================================
echo.
echo إذا رأيت علامات ✓ فقط، فالنظام متوافق تماماً
echo إذا رأيت علامات ⚠، فقد تواجه مشاكل بسيطة
echo إذا رأيت علامات ✗، فيجب حل هذه المشاكل أولاً
echo.
echo للحصول على أفضل أداء:
echo - تأكد من تثبيت .NET 8.0 Runtime
echo - أضف استثناء في Windows Defender
echo - أضف استثناء في جدار الحماية للمنافذ 5000-5001
echo - شغل البرنامج بصلاحيات المدير عند الحاجة
echo.
echo ========================================

pause
