﻿@model MarbleWorkshopSystem.Services.DashboardStatistics

@{
    ViewData["Title"] = "لوحة التحكم الرئيسية";
}

<!-- إضافة Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<div class="container-fluid">
    <!-- ترحيب وتاريخ -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-1">مرحباً بك في ورشة الرخام المتقدمة</h2>
                            <p class="mb-0">نظام إدارة شامل لورشة الرخام والجرانيت</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <h4 id="currentDate"></h4>
                            <p id="currentTime" class="mb-0"></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات السريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                مبيعات اليوم
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                @Model.TodaySales.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                            </div>
                            <small class="text-muted">@Model.TodayInvoicesCount فاتورة</small>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                مبيعات الشهر
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                @Model.ThisMonthSales.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                            </div>
                            <small class="text-muted">
                                @if (Model.SalesGrowthPercentage >= 0)
                                {
                                    <span class="text-success">
                                        <i class="fas fa-arrow-up"></i> @Model.SalesGrowthPercentage.ToString("F1")%
                                    </span>
                                }
                                else
                                {
                                    <span class="text-danger">
                                        <i class="fas fa-arrow-down"></i> @Math.Abs(Model.SalesGrowthPercentage).ToString("F1")%
                                    </span>
                                }
                            </small>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                صافي الربح
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                @Model.NetProfit.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                            </div>
                            <small class="text-muted">هذا الشهر</small>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                قيمة المخزون
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                @Model.TotalInventoryValue.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                            </div>
                            @if (Model.LowStockProducts > 0)
                            {
                                <small class="text-warning">
                                    <i class="fas fa-exclamation-triangle"></i> @Model.LowStockProducts منتج منخفض
                                </small>
                            }
                            else
                            {
                                <small class="text-success">المخزون جيد</small>
                            }
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-warehouse fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row mb-4">
        <!-- رسم بياني للمبيعات الشهرية -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">مبيعات آخر 12 شهر</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow">
                            <a class="dropdown-item" href="#" onclick="exportChart('salesChart')">تصدير كصورة</a>
                            <a class="dropdown-item" href="#" onclick="printChart('salesChart')">طباعة</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="salesChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- رسم دائري للفئات -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">مبيعات حسب الفئة</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="categoryChart" width="400" height="400"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الجداول التفصيلية -->
    <div class="row">
        <!-- أفضل العملاء -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">أفضل العملاء هذا الشهر</h6>
                </div>
                <div class="card-body">
                    @if (Model.TopCustomers.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <thead>
                                    <tr>
                                        <th>العميل</th>
                                        <th>عدد الفواتير</th>
                                        <th>إجمالي المبيعات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var customer in Model.TopCustomers)
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-sm me-3">
                                                        <div class="avatar-initial rounded-circle bg-label-primary">
                                                            @customer.CustomerName.Substring(0, 1)
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">@customer.CustomerName</h6>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">@customer.InvoicesCount</span>
                                            </td>
                                            <td>
                                                <strong>@customer.TotalSales.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</strong>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد مبيعات هذا الشهر</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- أفضل المنتجات -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">أفضل المنتجات هذا الشهر</h6>
                </div>
                <div class="card-body">
                    @if (Model.TopProducts.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <thead>
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الكمية المباعة</th>
                                        <th>إجمالي المبيعات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var product in Model.TopProducts)
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-sm me-3">
                                                        <div class="avatar-initial rounded-circle bg-label-success">
                                                            <i class="fas fa-cube"></i>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0">@product.ProductName</h6>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">@product.TotalQuantity.ToString("F1") م²</span>
                                            </td>
                                            <td>
                                                <strong>@product.TotalSales.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</strong>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-box fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد مبيعات هذا الشهر</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات إضافية -->
    <div class="row mb-4">
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                إجمالي العملاء
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalCustomers</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                إجمالي المنتجات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalProducts</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-box fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-md-6 mb-4">
            <div class="card border-left-secondary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                فواتير معلقة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.PendingInvoices</div>
                            @if (Model.PendingInvoices > 0)
                            {
                                <small class="text-warning">تحتاج مراجعة</small>
                            }
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- روابط سريعة -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">الإجراءات السريعة</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a asp-controller="Sales" asp-action="Create" class="btn btn-primary btn-block">
                                <i class="fas fa-plus me-2"></i>
                                فاتورة مبيعات جديدة
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a asp-controller="Purchases" asp-action="Create" class="btn btn-success btn-block">
                                <i class="fas fa-shopping-cart me-2"></i>
                                فاتورة مشتريات جديدة
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a asp-controller="Products" asp-action="Create" class="btn btn-info btn-block">
                                <i class="fas fa-cube me-2"></i>
                                إضافة منتج جديد
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a asp-controller="Reports" asp-action="Index" class="btn btn-warning btn-block">
                                <i class="fas fa-chart-bar me-2"></i>
                                عرض التقارير
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CSS إضافي -->
<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }
    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }
    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }
    .border-left-secondary {
        border-left: 0.25rem solid #858796 !important;
    }
    .bg-gradient-primary {
        background: linear-gradient(87deg, #5e72e4 0, #825ee4 100%) !important;
    }
    .avatar-initial {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 38px;
        height: 38px;
        font-size: 14px;
        font-weight: 600;
    }
    .bg-label-primary {
        background-color: rgba(78, 115, 223, 0.12) !important;
        color: #4e73df !important;
    }
    .bg-label-success {
        background-color: rgba(28, 200, 138, 0.12) !important;
        color: #1cc88a !important;
    }
</style>

<script>
    // تحديث التاريخ والوقت
    function updateDateTime() {
        const now = new Date();
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };

        document.getElementById('currentDate').textContent = now.toLocaleDateString('ar-EG', options);
        document.getElementById('currentTime').textContent = now.toLocaleTimeString('ar-EG');
    }

    // تحديث كل ثانية
    setInterval(updateDateTime, 1000);
    updateDateTime();

    // رسم بياني للمبيعات الشهرية
    let salesChart;
    let categoryChart;

    document.addEventListener('DOMContentLoaded', function() {
        // جلب بيانات المبيعات الشهرية
        fetch('/Home/GetMonthlySalesData')
            .then(response => response.json())
            .then(data => {
                const ctx = document.getElementById('salesChart').getContext('2d');
                salesChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: data.map(d => d.monthName),
                        datasets: [{
                            label: 'المبيعات (جنيه)',
                            data: data.map(d => d.totalSales),
                            borderColor: '#4e73df',
                            backgroundColor: 'rgba(78, 115, 223, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return 'المبيعات: ' + context.parsed.y.toLocaleString('ar-EG') + ' جنيه';
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return value.toLocaleString('ar-EG') + ' جنيه';
                                    }
                                }
                            }
                        }
                    }
                });
            })
            .catch(error => console.error('خطأ في جلب بيانات المبيعات:', error));

        // جلب بيانات الفئات
        fetch('/Home/GetCategorySalesData')
            .then(response => response.json())
            .then(data => {
                const ctx = document.getElementById('categoryChart').getContext('2d');
                const colors = [
                    '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
                    '#6f42c1', '#fd7e14', '#20c997', '#6c757d', '#17a2b8'
                ];

                categoryChart = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: data.map(d => d.category),
                        datasets: [{
                            data: data.map(d => d.totalSales),
                            backgroundColor: colors.slice(0, data.length),
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                                        return context.label + ': ' + context.parsed.toLocaleString('ar-EG') + ' جنيه (' + percentage + '%)';
                                    }
                                }
                            }
                        }
                    }
                });
            })
            .catch(error => console.error('خطأ في جلب بيانات الفئات:', error));
    });

    // دوال تصدير الرسوم البيانية
    function exportChart(chartId) {
        const chart = chartId === 'salesChart' ? salesChart : categoryChart;
        const url = chart.toBase64Image();
        const link = document.createElement('a');
        link.download = chartId + '.png';
        link.href = url;
        link.click();
    }

    function printChart(chartId) {
        const chart = chartId === 'salesChart' ? salesChart : categoryChart;
        const url = chart.toBase64Image();
        const printWindow = window.open('', '_blank');
        printWindow.document.write('<img src="' + url + '" style="width:100%;">');
        printWindow.document.close();
        printWindow.print();
    }

    // تحديث تلقائي للإحصائيات كل 5 دقائق
    setInterval(function() {
        location.reload();
    }, 300000); // 5 دقائق
</script>
