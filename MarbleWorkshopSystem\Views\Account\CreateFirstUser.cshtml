@model MarbleWorkshopSystem.ViewModels.CreateFirstUserViewModel
@{
    ViewData["Title"] = "إنشاء المستخدم الأول";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}

<form asp-action="CreateFirstUser" method="post">
    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert" style="display: none;"></div>
    
    <!-- رسالة ترحيب -->
    <div class="alert alert-success mb-4" role="alert">
        <i class="fas fa-star me-2"></i>
        <strong>مرحباً بك!</strong><br>
        <small>يبدو أن هذه هي المرة الأولى لاستخدام النظام. يرجى إنشاء حساب المدير الأول.</small>
    </div>
    
    <div class="mb-4">
        <label asp-for="Username" class="form-label fw-semibold">
            <i class="fas fa-user me-2 text-primary"></i>
            اسم المستخدم <span class="text-danger">*</span>
        </label>
        <input asp-for="Username" class="form-control form-control-lg" 
               placeholder="أدخل اسم المستخدم (3-50 حرف)" 
               autocomplete="username" />
        <span asp-validation-for="Username" class="text-danger"></span>
        <small class="form-text text-muted">سيتم استخدام هذا الاسم لتسجيل الدخول</small>
    </div>

    <div class="mb-4">
        <label asp-for="FullName" class="form-label fw-semibold">
            <i class="fas fa-id-card me-2 text-primary"></i>
            الاسم الكامل <span class="text-danger">*</span>
        </label>
        <input asp-for="FullName" class="form-control form-control-lg" 
               placeholder="أدخل الاسم الكامل" 
               autocomplete="name" />
        <span asp-validation-for="FullName" class="text-danger"></span>
    </div>

    <div class="mb-4">
        <label asp-for="Email" class="form-label fw-semibold">
            <i class="fas fa-envelope me-2 text-primary"></i>
            البريد الإلكتروني <span class="text-danger">*</span>
        </label>
        <input asp-for="Email" class="form-control form-control-lg" 
               placeholder="أدخل البريد الإلكتروني" 
               autocomplete="email" />
        <span asp-validation-for="Email" class="text-danger"></span>
    </div>

    <div class="mb-4">
        <label asp-for="Password" class="form-label fw-semibold">
            <i class="fas fa-lock me-2 text-primary"></i>
            كلمة المرور <span class="text-danger">*</span>
        </label>
        <div class="position-relative">
            <input asp-for="Password" class="form-control form-control-lg" 
                   placeholder="أدخل كلمة المرور (6 أحرف على الأقل)" 
                   autocomplete="new-password" 
                   id="passwordInput" />
            <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y me-2" 
                    onclick="togglePassword('passwordInput', 'passwordToggleIcon')" style="border: none; background: none;">
                <i class="fas fa-eye" id="passwordToggleIcon"></i>
            </button>
        </div>
        <span asp-validation-for="Password" class="text-danger"></span>
    </div>

    <div class="mb-4">
        <label asp-for="ConfirmPassword" class="form-label fw-semibold">
            <i class="fas fa-lock me-2 text-primary"></i>
            تأكيد كلمة المرور <span class="text-danger">*</span>
        </label>
        <div class="position-relative">
            <input asp-for="ConfirmPassword" class="form-control form-control-lg" 
                   placeholder="أعد إدخال كلمة المرور" 
                   autocomplete="new-password" 
                   id="confirmPasswordInput" />
            <button type="button" class="btn btn-link position-absolute end-0 top-50 translate-middle-y me-2" 
                    onclick="togglePassword('confirmPasswordInput', 'confirmPasswordToggleIcon')" style="border: none; background: none;">
                <i class="fas fa-eye" id="confirmPasswordToggleIcon"></i>
            </button>
        </div>
        <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
    </div>

    <div class="d-grid mb-3">
        <button type="submit" class="btn btn-login btn-lg text-white">
            <i class="fas fa-user-plus me-2"></i>
            إنشاء حساب المدير
        </button>
    </div>
    
    <!-- معلومات إضافية -->
    <div class="alert alert-warning" role="alert">
        <i class="fas fa-shield-alt me-2"></i>
        <strong>ملاحظة:</strong><br>
        <small>
            سيتم إنشاء هذا المستخدم كمدير للنظام مع جميع الصلاحيات.<br>
            تأكد من حفظ بيانات الدخول في مكان آمن.
        </small>
    </div>
</form>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // إظهار/إخفاء كلمة المرور
        function togglePassword(inputId, iconId) {
            const passwordInput = document.getElementById(inputId);
            const toggleIcon = document.getElementById(iconId);
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
        
        // تحسين عرض رسائل الخطأ
        document.addEventListener('DOMContentLoaded', function() {
            const validationSummary = document.querySelector('[data-valmsg-summary="true"]');
            if (validationSummary && validationSummary.innerHTML.trim()) {
                validationSummary.style.display = 'block';
            }
            
            // تركيز على حقل اسم المستخدم
            const usernameInput = document.querySelector('input[name="Username"]');
            if (usernameInput) {
                usernameInput.focus();
            }
        });
        
        // تأثيرات بصرية للنموذج
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('focused');
            });
        });
    </script>
}
