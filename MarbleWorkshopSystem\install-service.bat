@echo off
chcp 65001 > nul
title تثبيت خدمة نظام إدارة ورشة الرخام

echo ========================================
echo    تثبيت خدمة نظام إدارة ورشة الرخام
echo    Install Marble Workshop Service
echo ========================================
echo.

REM التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [خطأ] يجب تشغيل هذا الملف كمدير
    echo [ERROR] This script must be run as Administrator
    echo.
    echo انقر بالزر الأيمن واختر "تشغيل كمدير"
    echo Right-click and select "Run as Administrator"
    pause
    exit /b 1
)

echo جاري تثبيت الخدمة...
echo Installing service...

REM إنشاء مجلد الخدمة
set SERVICE_PATH=C:\MarbleWorkshop
if not exist "%SERVICE_PATH%" (
    mkdir "%SERVICE_PATH%"
    echo تم إنشاء مجلد: %SERVICE_PATH%
    echo Created folder: %SERVICE_PATH%
)

REM نسخ الملفات
echo جاري نسخ ملفات التطبيق...
echo Copying application files...
xcopy /E /I /Y "bin\Release\net8.0\publish\win-x64\*" "%SERVICE_PATH%\"

REM تثبيت الخدمة
echo جاري تثبيت خدمة Windows...
echo Installing Windows Service...

sc create "MarbleWorkshopService" ^
    binPath= "\"%SERVICE_PATH%\MarbleWorkshopSystem.exe\" --contentRoot \"%SERVICE_PATH%\"" ^
    start= auto ^
    DisplayName= "نظام إدارة ورشة الرخام" ^
    description= "خدمة نظام إدارة ورشة الرخام والجرانيت"

if %errorlevel% equ 0 (
    echo.
    echo ✓ تم تثبيت الخدمة بنجاح
    echo ✓ Service installed successfully
    echo.
    
    REM بدء الخدمة
    echo جاري بدء الخدمة...
    echo Starting service...
    sc start "MarbleWorkshopService"
    
    if %errorlevel% equ 0 (
        echo ✓ تم بدء الخدمة بنجاح
        echo ✓ Service started successfully
        echo.
        echo يمكنك الآن الوصول للنظام عبر:
        echo You can now access the system at:
        echo http://localhost:5000
    ) else (
        echo ✗ فشل في بدء الخدمة
        echo ✗ Failed to start service
    )
) else (
    echo ✗ فشل في تثبيت الخدمة
    echo ✗ Failed to install service
)

echo.
echo لإلغاء تثبيت الخدمة استخدم: uninstall-service.bat
echo To uninstall the service use: uninstall-service.bat
echo.
pause
