using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using MarbleWorkshopSystem.Data;
using MarbleWorkshopSystem.Services;
using MarbleWorkshopSystem.Models;
using System.Globalization;
using Microsoft.Extensions.Hosting.WindowsServices;

// إعداد التطبيق للعمل كخدمة Windows
var options = new WebApplicationOptions
{
    Args = args,
    ContentRootPath = WindowsServiceHelpers.IsWindowsService()
        ? AppContext.BaseDirectory
        : default
};

var builder = WebApplication.CreateBuilder(options);

// إضافة دعم Windows Services
if (WindowsServiceHelpers.IsWindowsService())
{
    builder.Host.UseWindowsService();
}

// إعداد الثقافة العربية
var supportedCultures = new[] { "ar-EG", "en-US" };
builder.Services.Configure<RequestLocalizationOptions>(options =>
{
    options.DefaultRequestCulture = new Microsoft.AspNetCore.Localization.RequestCulture("ar-EG");
    options.SupportedCultures = supportedCultures.Select(c => new CultureInfo(c)).ToList();
    options.SupportedUICultures = supportedCultures.Select(c => new CultureInfo(c)).ToList();
});

// Add services to the container.
builder.Services.AddControllersWithViews(options =>
{
    // تحسين الأداء
    options.EnableEndpointRouting = true;
})
.AddNewtonsoftJson(options =>
{
    // إعدادات JSON للتوافق مع العربية
    options.SerializerSettings.Culture = new CultureInfo("ar-EG");
    options.SerializerSettings.DateFormatString = "dd/MM/yyyy";
});

// إضافة خدمات الأداء والتخزين المؤقت
builder.Services.AddMemoryCache();
builder.Services.AddResponseCaching();
builder.Services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
});

// إعدادات الجلسة
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(30);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
    options.Cookie.Name = "MarbleWorkshop.Session";
});

// إعدادات الأمان
builder.Services.AddAntiforgery(options =>
{
    options.HeaderName = "X-CSRF-TOKEN";
    options.Cookie.Name = "MarbleWorkshop.Antiforgery";
    options.Cookie.HttpOnly = true;
    options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
});

// Add Entity Framework مع دعم SQL Server و SQLite
builder.Services.AddDbContext<ApplicationDbContext>(options =>
{
    var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");

    // تحديد نوع قاعدة البيانات بناءً على سلسلة الاتصال
    if (connectionString?.Contains(".db") == true || connectionString?.StartsWith("Data Source=") == true)
    {
        // استخدام SQLite
        options.UseSqlite(connectionString);
        builder.Services.BuildServiceProvider().GetService<ILogger<Program>>()?.LogInformation("Using SQLite database");
    }
    else
    {
        // استخدام SQL Server
        options.UseSqlServer(connectionString, sqlOptions =>
        {
            sqlOptions.EnableRetryOnFailure(
                maxRetryCount: 3,
                maxRetryDelay: TimeSpan.FromSeconds(30),
                errorNumbersToAdd: null);
            sqlOptions.CommandTimeout(30);
        });
        builder.Services.BuildServiceProvider().GetService<ILogger<Program>>()?.LogInformation("Using SQL Server database");
    }

    // تمكين السجلات في بيئة التطوير فقط
    if (builder.Environment.IsDevelopment())
    {
        options.EnableSensitiveDataLogging();
        options.EnableDetailedErrors();
    }
});

// Add Identity
builder.Services.AddIdentity<ApplicationUser, IdentityRole>(options =>
{
    options.SignIn.RequireConfirmedAccount = false;
    options.Password.RequireDigit = false;
    options.Password.RequireLowercase = false;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequireUppercase = false;
    options.Password.RequiredLength = 4;
})
.AddEntityFrameworkStores<ApplicationDbContext>()
.AddDefaultTokenProviders();

// Configure application cookie
builder.Services.ConfigureApplicationCookie(options =>
{
    options.LoginPath = "/Account/Login";
    options.LogoutPath = "/Account/Logout";
    options.AccessDeniedPath = "/Account/AccessDenied";
});

// Add Repository Pattern
builder.Services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

// Add Services
builder.Services.AddScoped<IProductService, ProductService>();
builder.Services.AddScoped<IInventoryService, InventoryService>();
builder.Services.AddScoped<ICustomerService, CustomerService>();
builder.Services.AddScoped<ISupplierService, SupplierService>();
builder.Services.AddScoped<ISalesService, SalesService>();
builder.Services.AddScoped<IPurchaseService, PurchaseService>();
builder.Services.AddScoped<SimpleReportService>();
builder.Services.AddScoped<DashboardService>();

var app = builder.Build();

// Seed Database
try
{
    using (var scope = app.Services.CreateScope())
    {
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

        // التأكد من إنشاء قاعدة البيانات
        await context.Database.EnsureCreatedAsync();

        // تشغيل المايجريشن
        if (context.Database.GetPendingMigrations().Any())
        {
            await context.Database.MigrateAsync();
        }

        // إضافة البيانات الأولية
        await SeedData.InitializeAsync(context);

        app.Logger.LogInformation("تم تهيئة قاعدة البيانات بنجاح / Database initialized successfully");
    }
}
catch (Exception ex)
{
    app.Logger.LogError(ex, "خطأ في تهيئة قاعدة البيانات / Database initialization error");
    // لا نوقف التطبيق، سنحاول إنشاء قاعدة البيانات عند أول طلب
}

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}
else
{
    app.UseDeveloperExceptionPage();
}

// إعداد الثقافة
app.UseRequestLocalization();

// تمكين ضغط الاستجابة
app.UseResponseCompression();

// تمكين التخزين المؤقت
app.UseResponseCaching();

// إعداد الملفات الثابتة مع التخزين المؤقت
app.UseStaticFiles(new StaticFileOptions
{
    OnPrepareResponse = ctx =>
    {
        // تخزين مؤقت للملفات الثابتة لمدة 30 يوم
        ctx.Context.Response.Headers.Append("Cache-Control", "public,max-age=2592000");
    }
});

// إعداد التوجيه
app.UseRouting();

// إعداد الجلسة
app.UseSession();

// إعداد المصادقة والتخويل
app.UseAuthentication();
app.UseAuthorization();

// تمكين الحماية من CSRF
app.Use(async (context, next) =>
{
    // إضافة رؤوس الأمان
    context.Response.Headers.Append("X-Content-Type-Options", "nosniff");
    context.Response.Headers.Append("X-Frame-Options", "DENY");
    context.Response.Headers.Append("X-XSS-Protection", "1; mode=block");
    context.Response.Headers.Append("Referrer-Policy", "strict-origin-when-cross-origin");

    await next();
});

// تعيين المسارات - البدء بصفحة الدخول أو إنشاء المستخدم الأول
app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Account}/{action=Login}/{id?}");

// إعداد معالجة الأخطاء المخصصة
app.UseStatusCodePagesWithReExecute("/Home/Error", "?statusCode={0}");

// تشغيل التطبيق
try
{
    app.Logger.LogInformation("بدء تشغيل نظام إدارة ورشة الرخام...");
    app.Logger.LogInformation("Starting Marble Workshop Management System...");

    app.Run();
}
catch (Exception ex)
{
    app.Logger.LogCritical(ex, "خطأ في بدء تشغيل التطبيق / Application startup error");
    throw;
}
