using Microsoft.EntityFrameworkCore;
using MarbleWorkshopSystem.Data;
using MarbleWorkshopSystem.Models;

namespace MarbleWorkshopSystem.Services
{
    public class ProductService : IProductService
    {
        private readonly IRepository<Product> _productRepository;
        private readonly IRepository<Inventory> _inventoryRepository;
        private readonly ApplicationDbContext _context;

        public ProductService(IRepository<Product> productRepository, 
                            IRepository<Inventory> inventoryRepository,
                            ApplicationDbContext context)
        {
            _productRepository = productRepository;
            _inventoryRepository = inventoryRepository;
            _context = context;
        }

        public async Task<IEnumerable<Product>> GetAllProductsAsync()
        {
            return await _context.Products
                .Include(p => p.Inventory)
                .Where(p => p.IsActive)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<Product?> GetProductByIdAsync(int id)
        {
            return await _context.Products
                .Include(p => p.Inventory)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<IEnumerable<Product>> GetProductsByCategoryAsync(string category)
        {
            return await _context.Products
                .Include(p => p.Inventory)
                .Where(p => p.Category == category && p.IsActive)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Product>> SearchProductsAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllProductsAsync();

            return await _context.Products
                .Include(p => p.Inventory)
                .Where(p => p.IsActive && 
                           (p.Name.Contains(searchTerm) || 
                            p.Description!.Contains(searchTerm) ||
                            p.Category.Contains(searchTerm)))
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<Product> CreateProductAsync(Product product)
        {
            // حساب المساحة
            product.Area = product.Length * product.Width;
            product.CreatedDate = DateTime.Now;
            product.UpdatedDate = DateTime.Now;

            var createdProduct = await _productRepository.AddAsync(product);
            await _productRepository.SaveChangesAsync();

            // إنشاء سجل مخزون للمنتج الجديد
            var inventory = new Inventory
            {
                ProductId = createdProduct.Id,
                Quantity = 0,
                MinimumStock = 0,
                WasteQuantity = 0,
                LastUpdated = DateTime.Now
            };

            await _inventoryRepository.AddAsync(inventory);
            await _inventoryRepository.SaveChangesAsync();

            return createdProduct;
        }

        public async Task<Product> UpdateProductAsync(Product product)
        {
            // حساب المساحة
            product.Area = product.Length * product.Width;
            product.UpdatedDate = DateTime.Now;

            var updatedProduct = await _productRepository.UpdateAsync(product);
            await _productRepository.SaveChangesAsync();

            return updatedProduct;
        }

        public async Task DeleteProductAsync(int id)
        {
            var product = await GetProductByIdAsync(id);
            if (product != null)
            {
                product.IsActive = false;
                await UpdateProductAsync(product);
            }
        }

        public async Task<bool> ProductExistsAsync(int id)
        {
            return await _productRepository.ExistsAsync(id);
        }

        public async Task<IEnumerable<string>> GetCategoriesAsync()
        {
            return await _context.Products
                .Where(p => p.IsActive)
                .Select(p => p.Category)
                .Distinct()
                .OrderBy(c => c)
                .ToListAsync();
        }

        public async Task UpdateProductAreaAsync(int productId)
        {
            var product = await GetProductByIdAsync(productId);
            if (product != null)
            {
                product.Area = product.Length * product.Width;
                product.UpdatedDate = DateTime.Now;
                await _productRepository.UpdateAsync(product);
                await _productRepository.SaveChangesAsync();
            }
        }
    }
}
