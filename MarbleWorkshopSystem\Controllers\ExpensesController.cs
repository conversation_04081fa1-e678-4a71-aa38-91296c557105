using Microsoft.AspNetCore.Mvc;
using MarbleWorkshopSystem.Models;
using MarbleWorkshopSystem.Services;

namespace MarbleWorkshopSystem.Controllers
{
    public class ExpensesController : Controller
    {
        private readonly IRepository<Expense> _expenseRepository;

        public ExpensesController(IRepository<Expense> expenseRepository)
        {
            _expenseRepository = expenseRepository;
        }

        // GET: Expenses
        public async Task<IActionResult> Index(string category, DateTime? fromDate, DateTime? toDate)
        {
            var expenses = await _expenseRepository.GetAllAsync();

            // تطبيق الفلاتر
            if (!string.IsNullOrEmpty(category))
            {
                expenses = expenses.Where(e => e.Category == category);
            }

            if (fromDate.HasValue)
            {
                expenses = expenses.Where(e => e.ExpenseDate >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                expenses = expenses.Where(e => e.ExpenseDate <= toDate.Value);
            }

            // جلب الفئات للفلتر
            var allExpenses = await _expenseRepository.GetAllAsync();
            ViewBag.Categories = allExpenses.Select(e => e.Category).Distinct().OrderBy(c => c).ToList();
            ViewBag.SelectedCategory = category;
            ViewBag.FromDate = fromDate?.ToString("yyyy-MM-dd");
            ViewBag.ToDate = toDate?.ToString("yyyy-MM-dd");

            return View(expenses.OrderByDescending(e => e.ExpenseDate));
        }

        // GET: Expenses/Details/5
        public async Task<IActionResult> Details(int id)
        {
            var expense = await _expenseRepository.GetByIdAsync(id);
            if (expense == null)
            {
                return NotFound();
            }

            return View(expense);
        }

        // GET: Expenses/Create
        public async Task<IActionResult> Create()
        {
            // جلب الفئات الموجودة
            var allExpenses = await _expenseRepository.GetAllAsync();
            ViewBag.Categories = allExpenses.Select(e => e.Category).Distinct().OrderBy(c => c).ToList();
            
            return View();
        }

        // POST: Expenses/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Expense expense)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    await _expenseRepository.AddAsync(expense);
                    await _expenseRepository.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم إضافة المصروف بنجاح";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "حدث خطأ أثناء إضافة المصروف: " + ex.Message);
                }
            }

            // إعادة تحميل الفئات في حالة الخطأ
            var allExpenses = await _expenseRepository.GetAllAsync();
            ViewBag.Categories = allExpenses.Select(e => e.Category).Distinct().OrderBy(c => c).ToList();
            
            return View(expense);
        }

        // GET: Expenses/Edit/5
        public async Task<IActionResult> Edit(int id)
        {
            var expense = await _expenseRepository.GetByIdAsync(id);
            if (expense == null)
            {
                return NotFound();
            }

            // جلب الفئات الموجودة
            var allExpenses = await _expenseRepository.GetAllAsync();
            ViewBag.Categories = allExpenses.Select(e => e.Category).Distinct().OrderBy(c => c).ToList();

            return View(expense);
        }

        // POST: Expenses/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, Expense expense)
        {
            if (id != expense.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    await _expenseRepository.UpdateAsync(expense);
                    await _expenseRepository.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم تحديث المصروف بنجاح";
                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", "حدث خطأ أثناء تحديث المصروف: " + ex.Message);
                }
            }

            // إعادة تحميل الفئات في حالة الخطأ
            var allExpenses = await _expenseRepository.GetAllAsync();
            ViewBag.Categories = allExpenses.Select(e => e.Category).Distinct().OrderBy(c => c).ToList();

            return View(expense);
        }

        // GET: Expenses/Delete/5
        public async Task<IActionResult> Delete(int id)
        {
            var expense = await _expenseRepository.GetByIdAsync(id);
            if (expense == null)
            {
                return NotFound();
            }

            return View(expense);
        }

        // POST: Expenses/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            try
            {
                await _expenseRepository.DeleteAsync(id);
                await _expenseRepository.SaveChangesAsync();
                TempData["SuccessMessage"] = "تم حذف المصروف بنجاح";
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف المصروف: " + ex.Message;
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: Expenses/Report
        public async Task<IActionResult> Report(DateTime? fromDate, DateTime? toDate, string category)
        {
            var expenses = await _expenseRepository.GetAllAsync();

            // تطبيق الفلاتر
            if (!string.IsNullOrEmpty(category))
            {
                expenses = expenses.Where(e => e.Category == category);
            }

            if (fromDate.HasValue)
            {
                expenses = expenses.Where(e => e.ExpenseDate >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                expenses = expenses.Where(e => e.ExpenseDate <= toDate.Value);
            }

            // إحصائيات
            ViewBag.TotalAmount = expenses.Sum(e => e.Amount);
            ViewBag.ExpenseCount = expenses.Count();
            ViewBag.CategorySummary = expenses.GroupBy(e => e.Category)
                                            .Select(g => new { Category = g.Key, Total = g.Sum(e => e.Amount), Count = g.Count() })
                                            .OrderByDescending(x => x.Total)
                                            .ToList();

            // جلب الفئات للفلتر
            var allExpenses = await _expenseRepository.GetAllAsync();
            ViewBag.Categories = allExpenses.Select(e => e.Category).Distinct().OrderBy(c => c).ToList();
            ViewBag.SelectedCategory = category;
            ViewBag.FromDate = fromDate?.ToString("yyyy-MM-dd");
            ViewBag.ToDate = toDate?.ToString("yyyy-MM-dd");

            return View(expenses.OrderByDescending(e => e.ExpenseDate));
        }
    }
}
