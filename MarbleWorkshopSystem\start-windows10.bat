@echo off
chcp 65001 > nul
title نظام إدارة ورشة الرخام - ويندوز 10

echo ========================================
echo    نظام إدارة ورشة الرخام
echo    Marble Workshop Management System
echo    متوافق مع ويندوز 10
echo ========================================
echo.

:: التحقق من إصدار ويندوز
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo إصدار ويندوز: %VERSION%

:: التحقق من وجود .NET Runtime
echo جاري التحقق من .NET Runtime...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo تحذير: .NET Runtime غير مثبت أو غير متاح
    echo يرجى تثبيت .NET 8.0 Runtime من:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    echo.
    echo الضغط على أي مفتاح للمتابعة مع ملف التشغيل المستقل...
    pause >nul
) else (
    echo ✓ .NET Runtime متاح
)

:: إنشاء المجلدات المطلوبة
echo جاري إنشاء المجلدات المطلوبة...
if not exist "%LOCALAPPDATA%\MarbleWorkshop" mkdir "%LOCALAPPDATA%\MarbleWorkshop"
if not exist "%LOCALAPPDATA%\MarbleWorkshop\Logs" mkdir "%LOCALAPPDATA%\MarbleWorkshop\Logs"
if not exist "%USERPROFILE%\Documents\MarbleWorkshop" mkdir "%USERPROFILE%\Documents\MarbleWorkshop"
if not exist "%USERPROFILE%\Documents\MarbleWorkshop\Backups" mkdir "%USERPROFILE%\Documents\MarbleWorkshop\Backups"

:: تعيين متغيرات البيئة
set ASPNETCORE_ENVIRONMENT=Production
set ASPNETCORE_URLS=http://localhost:5000
set DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false

:: التحقق من المنفذ
echo جاري التحقق من توفر المنفذ 5000...
netstat -an | find "5000" >nul
if %errorlevel% equ 0 (
    echo تحذير: المنفذ 5000 مستخدم بالفعل
    echo جاري البحث عن منفذ بديل...
    set ASPNETCORE_URLS=http://localhost:5001
    echo سيتم استخدام المنفذ 5001
) else (
    echo ✓ المنفذ 5000 متاح
)

echo.
echo جاري بدء تشغيل النظام...
echo يرجى الانتظار...
echo.

:: محاولة تشغيل النسخة المبنية أولاً
if exist "MarbleWorkshopSystem.exe" (
    echo تشغيل النسخة المبنية...
    start "" "MarbleWorkshopSystem.exe" --environment=Production
) else if exist "bin\Release\net8.0\win-x64\publish\MarbleWorkshopSystem.exe" (
    echo تشغيل النسخة المنشورة...
    start "" "bin\Release\net8.0\win-x64\publish\MarbleWorkshopSystem.exe" --environment=Production
) else (
    echo تشغيل باستخدام dotnet...
    dotnet run --configuration Release --environment Production --urls %ASPNETCORE_URLS%
)

:: انتظار قليل ثم فتح المتصفح
timeout /t 3 /nobreak >nul
echo.
echo جاري فتح المتصفح...

:: محاولة فتح المتصفح
if "%ASPNETCORE_URLS%"=="http://localhost:5001" (
    start http://localhost:5001
) else (
    start http://localhost:5000
)

echo.
echo ========================================
echo النظام يعمل الآن!
echo 
echo للوصول للنظام:
if "%ASPNETCORE_URLS%"=="http://localhost:5001" (
    echo http://localhost:5001
) else (
    echo http://localhost:5000
)
echo.
echo لإيقاف النظام: اضغط Ctrl+C
echo ========================================
echo.

:: إبقاء النافذة مفتوحة
cmd /k
