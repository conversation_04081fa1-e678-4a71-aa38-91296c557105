using System.ComponentModel.DataAnnotations;

namespace MarbleWorkshopSystem.Models
{
    public class SystemSetting
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Key { get; set; } = string.Empty;

        [Required]
        public string Value { get; set; } = string.Empty;

        [StringLength(200)]
        public string? Description { get; set; }

        [StringLength(50)]
        public string Category { get; set; } = "عام";

        public DateTime LastUpdated { get; set; } = DateTime.Now;

        [StringLength(100)]
        public string? UpdatedBy { get; set; }
    }

    public class CompanyInfo
    {
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string CompanyName { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Address { get; set; }

        [StringLength(20)]
        public string? Phone { get; set; }

        [StringLength(20)]
        public string? Mobile { get; set; }

        [StringLength(100)]
        public string? Email { get; set; }

        [StringLength(50)]
        public string? TaxNumber { get; set; }

        [StringLength(50)]
        public string? CommercialRegister { get; set; }

        [StringLength(500)]
        public string? LogoPath { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public DateTime LastUpdated { get; set; } = DateTime.Now;

        [StringLength(100)]
        public string? UpdatedBy { get; set; }
    }

    public static class SettingKeys
    {
        // إعدادات الشركة
        public const string CompanyName = "CompanyName";
        public const string CompanyAddress = "CompanyAddress";
        public const string CompanyPhone = "CompanyPhone";
        public const string CompanyEmail = "CompanyEmail";
        public const string CompanyLogo = "CompanyLogo";

        // إعدادات النظام
        public const string DefaultCurrency = "DefaultCurrency";
        public const string DateFormat = "DateFormat";
        public const string TimeZone = "TimeZone";
        public const string Language = "Language";

        // إعدادات المخزون
        public const string LowStockThreshold = "LowStockThreshold";
        public const string AutoUpdateStock = "AutoUpdateStock";
        public const string AllowNegativeStock = "AllowNegativeStock";

        // إعدادات الفواتير
        public const string InvoicePrefix = "InvoicePrefix";
        public const string DefaultTaxRate = "DefaultTaxRate";
        public const string DefaultDiscount = "DefaultDiscount";
        public const string PrintAfterSave = "PrintAfterSave";

        // إعدادات الطباعة
        public const string DefaultPrintFormat = "DefaultPrintFormat";
        public const string PrinterName = "PrinterName";
        public const string PaperSize = "PaperSize";

        // إعدادات النسخ الاحتياطي
        public const string BackupPath = "BackupPath";
        public const string AutoBackup = "AutoBackup";
        public const string BackupFrequency = "BackupFrequency";
    }
}
