/* Print Styles for Different Paper Sizes */

/* Common Print Styles */
@media print {
    body {
        font-family: 'Arial', sans-serif;
        font-size: 12px;
        line-height: 1.4;
        color: #000;
        background: white;
        margin: 0;
        padding: 0;
    }

    .no-print {
        display: none !important;
    }

    .print-only {
        display: block !important;
    }

    .page-break {
        page-break-after: always;
    }

    .page-break-before {
        page-break-before: always;
    }

    .page-break-inside-avoid {
        page-break-inside: avoid;
    }

    table {
        border-collapse: collapse;
        width: 100%;
        margin-bottom: 10px;
    }

    table, th, td {
        border: 1px solid #000;
    }

    th, td {
        padding: 5px;
        text-align: right;
    }

    th {
        background-color: #f0f0f0;
        font-weight: bold;
    }

    .text-center {
        text-align: center;
    }

    .text-left {
        text-align: left;
    }

    .text-right {
        text-align: right;
    }

    .font-bold {
        font-weight: bold;
    }

    .font-large {
        font-size: 16px;
    }

    .font-small {
        font-size: 10px;
    }
}

/* A4 Print Format (210mm x 297mm) */
@media print and (max-width: 210mm) {
    @page {
        size: A4;
        margin: 15mm;
    }

    .print-a4 {
        width: 100%;
        max-width: 180mm;
        margin: 0 auto;
    }

    .print-a4 .header {
        text-align: center;
        margin-bottom: 20px;
        border-bottom: 2px solid #000;
        padding-bottom: 10px;
    }

    .print-a4 .company-logo {
        max-height: 60px;
        margin-bottom: 10px;
    }

    .print-a4 .company-name {
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .print-a4 .company-info {
        font-size: 11px;
        line-height: 1.3;
    }

    .print-a4 .invoice-info {
        display: flex;
        justify-content: space-between;
        margin: 15px 0;
    }

    .print-a4 .invoice-details,
    .print-a4 .customer-details {
        width: 48%;
    }

    .print-a4 .items-table {
        margin: 20px 0;
    }

    .print-a4 .items-table th {
        background-color: #e0e0e0;
        font-size: 11px;
    }

    .print-a4 .items-table td {
        font-size: 10px;
    }

    .print-a4 .totals {
        margin-top: 15px;
        text-align: left;
    }

    .print-a4 .totals table {
        width: 50%;
        margin-right: auto;
        border: none;
    }

    .print-a4 .totals td {
        border: none;
        padding: 3px 10px;
    }

    .print-a4 .footer {
        margin-top: 30px;
        text-align: center;
        font-size: 10px;
        border-top: 1px solid #000;
        padding-top: 10px;
    }
}

/* A5 Print Format (148mm x 210mm) */
@media print and (max-width: 148mm) {
    @page {
        size: A5;
        margin: 10mm;
    }

    .print-a5 {
        width: 100%;
        max-width: 128mm;
        margin: 0 auto;
        font-size: 10px;
    }

    .print-a5 .header {
        text-align: center;
        margin-bottom: 15px;
        border-bottom: 1px solid #000;
        padding-bottom: 8px;
    }

    .print-a5 .company-logo {
        max-height: 40px;
        margin-bottom: 5px;
    }

    .print-a5 .company-name {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 3px;
    }

    .print-a5 .company-info {
        font-size: 9px;
        line-height: 1.2;
    }

    .print-a5 .invoice-info {
        margin: 10px 0;
    }

    .print-a5 .invoice-details,
    .print-a5 .customer-details {
        margin-bottom: 10px;
    }

    .print-a5 .items-table {
        margin: 15px 0;
    }

    .print-a5 .items-table th,
    .print-a5 .items-table td {
        font-size: 9px;
        padding: 3px;
    }

    .print-a5 .totals {
        margin-top: 10px;
    }

    .print-a5 .totals table {
        width: 100%;
        border: none;
    }

    .print-a5 .totals td {
        border: none;
        padding: 2px 5px;
        font-size: 9px;
    }

    .print-a5 .footer {
        margin-top: 15px;
        text-align: center;
        font-size: 8px;
        border-top: 1px solid #000;
        padding-top: 5px;
    }
}

/* POS Print Format (80mm width) */
@media print and (max-width: 80mm) {
    @page {
        size: 80mm auto;
        margin: 5mm;
    }

    .print-pos {
        width: 100%;
        max-width: 70mm;
        margin: 0 auto;
        font-size: 9px;
        font-family: 'Courier New', monospace;
    }

    .print-pos .header {
        text-align: center;
        margin-bottom: 10px;
        border-bottom: 1px dashed #000;
        padding-bottom: 5px;
    }

    .print-pos .company-logo {
        max-height: 30px;
        margin-bottom: 3px;
    }

    .print-pos .company-name {
        font-size: 12px;
        font-weight: bold;
        margin-bottom: 2px;
    }

    .print-pos .company-info {
        font-size: 8px;
        line-height: 1.1;
    }

    .print-pos .invoice-info {
        margin: 8px 0;
        text-align: center;
    }

    .print-pos .invoice-details {
        margin-bottom: 8px;
    }

    .print-pos .customer-details {
        margin-bottom: 8px;
        text-align: right;
    }

    .print-pos .items-table {
        margin: 10px 0;
        border: none;
    }

    .print-pos .items-table table {
        border: none;
    }

    .print-pos .items-table th,
    .print-pos .items-table td {
        border: none;
        border-bottom: 1px dashed #000;
        font-size: 8px;
        padding: 2px 1px;
    }

    .print-pos .items-table th {
        background: none;
        font-weight: bold;
    }

    .print-pos .item-row {
        border-bottom: 1px dotted #000;
    }

    .print-pos .totals {
        margin-top: 8px;
        border-top: 1px dashed #000;
        padding-top: 5px;
    }

    .print-pos .totals table {
        width: 100%;
        border: none;
    }

    .print-pos .totals td {
        border: none;
        padding: 1px 2px;
        font-size: 8px;
    }

    .print-pos .total-line {
        border-top: 1px solid #000;
        font-weight: bold;
    }

    .print-pos .footer {
        margin-top: 10px;
        text-align: center;
        font-size: 7px;
        border-top: 1px dashed #000;
        padding-top: 5px;
    }

    .print-pos .barcode {
        text-align: center;
        margin: 5px 0;
        font-family: 'Courier New', monospace;
        font-size: 10px;
    }
}

/* Print Button Styles */
.print-controls {
    margin: 20px 0;
    text-align: center;
}

.print-controls .btn {
    margin: 0 5px;
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.print-controls .btn-primary {
    background-color: #007bff;
    color: white;
}

.print-controls .btn-secondary {
    background-color: #6c757d;
    color: white;
}

.print-controls .btn:hover {
    opacity: 0.8;
}

/* Format Selection */
.format-selector {
    margin: 15px 0;
    text-align: center;
}

.format-selector label {
    margin: 0 10px;
    font-weight: normal;
}

.format-selector input[type="radio"] {
    margin-left: 5px;
}

/* Hide format selector when printing */
@media print {
    .print-controls,
    .format-selector {
        display: none !important;
    }
}
