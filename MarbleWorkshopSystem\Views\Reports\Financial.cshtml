@model MarbleWorkshopSystem.ViewModels.FinancialReportViewModel

@{
    ViewData["Title"] = "التقرير المالي";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        التقرير المالي
                    </h3>
                    <div>
                        <button type="button" class="btn btn-info me-2" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>
                            طباعة
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- فلاتر التاريخ -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="get" class="row g-3">
                                <div class="col-md-4">
                                    <label for="fromDate" class="form-label">من تاريخ</label>
                                    <input type="date" name="fromDate" id="fromDate" class="form-control" value="@Model.FromDate.ToString("yyyy-MM-dd")" />
                                </div>
                                <div class="col-md-4">
                                    <label for="toDate" class="form-label">إلى تاريخ</label>
                                    <input type="date" name="toDate" id="toDate" class="form-control" value="@Model.ToDate.ToString("yyyy-MM-dd")" />
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>
                                        تحديث التقرير
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- ملخص الفترة -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h5 class="mb-0">
                                    <i class="fas fa-calendar me-2"></i>
                                    فترة التقرير: من @Model.FromDate.ToString("dd/MM/yyyy") إلى @Model.ToDate.ToString("dd/MM/yyyy")
                                </h5>
                            </div>
                        </div>
                    </div>

                    <!-- البطاقات الإحصائية -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-arrow-up fa-2x mb-2"></i>
                                    <h4>@Model.TotalSales.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h4>
                                    <p class="mb-0">إجمالي المبيعات</p>
                                    <small>@Model.SalesCount فاتورة</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-arrow-down fa-2x mb-2"></i>
                                    <h4>@Model.TotalPurchases.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h4>
                                    <p class="mb-0">إجمالي المشتريات</p>
                                    <small>@Model.PurchasesCount فاتورة</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                                    <h4>@Model.TotalExpenses.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h4>
                                    <p class="mb-0">إجمالي المصروفات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card @(Model.NetProfit >= 0 ? "bg-primary" : "bg-secondary") text-white">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                                    <h4>@Model.NetProfit.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h4>
                                    <p class="mb-0">صافي الربح</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تفاصيل إضافية -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        متوسط العمليات
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>متوسط فاتورة المبيعات:</strong></td>
                                            <td class="text-end">@Model.AverageSale.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</td>
                                        </tr>
                                        <tr>
                                            <td><strong>متوسط فاتورة المشتريات:</strong></td>
                                            <td class="text-end">@Model.AveragePurchase.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</td>
                                        </tr>
                                        <tr>
                                            <td><strong>هامش الربح:</strong></td>
                                            <td class="text-end">
                                                @if (Model.TotalSales > 0)
                                                {
                                                    <span>@(((Model.TotalSales - Model.TotalPurchases - Model.TotalExpenses) / Model.TotalSales * 100).ToString("F2"))%</span>
                                                }
                                                else
                                                {
                                                    <span>0%</span>
                                                }
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="fas fa-list me-2"></i>
                                        المصروفات حسب الفئة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    @if (Model.ExpensesByCategory.Any())
                                    {
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>الفئة</th>
                                                    <th class="text-end">المبلغ</th>
                                                    <th class="text-center">العدد</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var expense in Model.ExpensesByCategory)
                                                {
                                                    <tr>
                                                        <td>@expense.Category</td>
                                                        <td class="text-end">@expense.Amount.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</td>
                                                        <td class="text-center">@expense.Count</td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    }
                                    else
                                    {
                                        <p class="text-muted text-center">لا توجد مصروفات في هذه الفترة</p>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">ملاحظات</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="mb-0">
                                        <li>يتم حساب صافي الربح بطرح إجمالي المشتريات والمصروفات من إجمالي المبيعات</li>
                                        <li>هامش الربح يُحسب كنسبة مئوية من إجمالي المبيعات</li>
                                        <li>جميع المبالغ تشمل الضرائب والخصومات</li>
                                        <li>التقرير يشمل فقط الفواتير المؤكدة</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // عرض رسائل النجاح والخطأ
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
                toastr.success('@TempData["SuccessMessage"]');
            </text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>
                toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
    </script>
}
