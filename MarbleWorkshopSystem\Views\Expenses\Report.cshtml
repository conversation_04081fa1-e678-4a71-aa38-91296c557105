@model IEnumerable<MarbleWorkshopSystem.Models.Expense>

@{
    ViewData["Title"] = "تقرير المصروفات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            تقرير المصروفات التفصيلي
                        </h4>
                        <div>
                            <button type="button" class="btn btn-light btn-sm" onclick="printReport()">
                                <i class="fas fa-print me-1"></i>
                                طباعة
                            </button>
                            <button type="button" class="btn btn-success btn-sm" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-1"></i>
                                تصدير Excel
                            </button>
                            <a asp-action="Index" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left me-1"></i>
                                العودة
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- فلاتر التقرير -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <form method="get" class="row g-3">
                                        <div class="col-md-3">
                                            <label class="form-label">من تاريخ:</label>
                                            <input type="date" name="fromDate" class="form-control" value="@ViewBag.FromDate" />
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">إلى تاريخ:</label>
                                            <input type="date" name="toDate" class="form-control" value="@ViewBag.ToDate" />
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">نوع المصروف:</label>
                                            <select name="category" class="form-select">
                                                <option value="">جميع الأنواع</option>
                                                <option value="رواتب" selected="@(ViewBag.Category == "رواتب")">رواتب</option>
                                                <option value="صيانة" selected="@(ViewBag.Category == "صيانة")">صيانة</option>
                                                <option value="مواد خام" selected="@(ViewBag.Category == "مواد خام")">مواد خام</option>
                                                <option value="كهرباء" selected="@(ViewBag.Category == "كهرباء")">كهرباء</option>
                                                <option value="مياه" selected="@(ViewBag.Category == "مياه")">مياه</option>
                                                <option value="وقود" selected="@(ViewBag.Category == "وقود")">وقود</option>
                                                <option value="أخرى" selected="@(ViewBag.Category == "أخرى")">أخرى</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">&nbsp;</label>
                                            <button type="submit" class="btn btn-primary d-block w-100">
                                                <i class="fas fa-search me-1"></i>
                                                تطبيق الفلتر
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Count()</h4>
                                    <small>إجمالي المصروفات</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h4>@Model.Sum(e => e.Amount).ToString("N2") ج.م</h4>
                                    <small>إجمالي المبلغ</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4>@(Model.Any() ? Model.Average(e => e.Amount).ToString("N2") : "0") ج.م</h4>
                                    <small>متوسط المصروف</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4>@(Model.Any() ? Model.GroupBy(e => e.Category).Count() : 0)</h4>
                                    <small>أنواع المصروفات</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول التقرير -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="expensesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>التاريخ</th>
                                    <th>النوع</th>
                                    <th>الوصف</th>
                                    <th>المبلغ</th>
                                    <th>طريقة الدفع</th>
                                    <th>الملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.Any())
                                {
                                    @foreach (var expense in Model.OrderByDescending(e => e.ExpenseDate))
                                    {
                                        <tr>
                                            <td>@expense.ExpenseDate.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                @{
                                                    var categoryClass = expense.Category switch
                                                    {
                                                        "رواتب" => "primary",
                                                        "صيانة" => "warning",
                                                        "مواد خام" => "info",
                                                        "كهرباء" => "success",
                                                        "مياه" => "info",
                                                        "وقود" => "secondary",
                                                        _ => "dark"
                                                    };
                                                }
                                                <span class="badge bg-@categoryClass">@expense.Category</span>
                                            </td>
                                            <td>@expense.Description</td>
                                            <td>
                                                <strong class="text-danger">@expense.Amount.ToString("N2") ج.م</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">نقدي</span>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(expense.Notes))
                                                {
                                                    <small>@expense.Notes</small>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="6" class="text-center text-muted py-4">
                                            <i class="fas fa-info-circle me-2"></i>
                                            لا توجد مصروفات في الفترة المحددة
                                        </td>
                                    </tr>
                                }
                            </tbody>
                            @if (Model.Any())
                            {
                                <tfoot class="table-dark">
                                    <tr>
                                        <th colspan="3">الإجمالي</th>
                                        <th>@Model.Sum(e => e.Amount).ToString("N2") ج.م</th>
                                        <th colspan="2"></th>
                                    </tr>
                                </tfoot>
                            }
                        </table>
                    </div>

                    @if (Model.Any())
                    {
                        <!-- تحليل المصروفات حسب النوع -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">
                                            <i class="fas fa-chart-pie me-2"></i>
                                            تحليل المصروفات حسب النوع
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>النوع</th>
                                                        <th>العدد</th>
                                                        <th>المبلغ</th>
                                                        <th>النسبة</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @{
                                                        var totalAmount = Model.Sum(e => e.Amount);
                                                    }
                                                    @foreach (var categoryGroup in Model.GroupBy(e => e.Category).OrderByDescending(g => g.Sum(e => e.Amount)))
                                                    {
                                                        var percentage = totalAmount > 0 ? (categoryGroup.Sum(e => e.Amount) / totalAmount * 100) : 0;
                                                        <tr>
                                                            <td>
                                                                @{
                                                                    var categoryClass = categoryGroup.Key switch
                                                                    {
                                                                        "رواتب" => "primary",
                                                                        "صيانة" => "warning",
                                                                        "مواد خام" => "info",
                                                                        "كهرباء" => "success",
                                                                        "مياه" => "info",
                                                                        "وقود" => "secondary",
                                                                        _ => "dark"
                                                                    };
                                                                }
                                                                <span class="badge bg-@categoryClass">@categoryGroup.Key</span>
                                                            </td>
                                                            <td>@categoryGroup.Count()</td>
                                                            <td>@categoryGroup.Sum(e => e.Amount).ToString("N2") ج.م</td>
                                                            <td>
                                                                <div class="progress" style="height: 20px;">
                                                                    <div class="progress-bar bg-@categoryClass" role="progressbar" 
                                                                         style="width: @percentage.ToString("N1")%" 
                                                                         aria-valuenow="@percentage" aria-valuemin="0" aria-valuemax="100">
                                                                        @percentage.ToString("N1")%
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- تحليل شهري -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">
                                            <i class="fas fa-calendar me-2"></i>
                                            التحليل الشهري
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>الشهر</th>
                                                        <th>العدد</th>
                                                        <th>المبلغ</th>
                                                        <th>المتوسط</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach (var monthGroup in Model.GroupBy(e => new { e.ExpenseDate.Year, e.ExpenseDate.Month }).OrderByDescending(g => g.Key.Year).ThenByDescending(g => g.Key.Month))
                                                    {
                                                        <tr>
                                                            <td>@monthGroup.Key.Month/@monthGroup.Key.Year</td>
                                                            <td>@monthGroup.Count()</td>
                                                            <td>@monthGroup.Sum(e => e.Amount).ToString("N2") ج.م</td>
                                                            <td>@monthGroup.Average(e => e.Amount).ToString("N2") ج.م</td>
                                                        </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function printReport() {
            window.print();
        }

        function exportToExcel() {
            // يمكن تطوير هذه الوظيفة لاحقاً لتصدير البيانات إلى Excel
            alert('سيتم تطوير وظيفة التصدير إلى Excel قريباً');
        }

        // تحسين عرض الجدول
        $(document).ready(function() {
            if ($.fn.DataTable) {
                $('#expensesTable').DataTable({
                    "language": {
                        "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
                    },
                    "order": [[ 0, "desc" ]],
                    "pageLength": 25
                });
            }
        });
    </script>
}
