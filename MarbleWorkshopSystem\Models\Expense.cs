using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MarbleWorkshopSystem.Models
{
    public class Expense
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "وصف المصروف مطلوب")]
        [StringLength(200, ErrorMessage = "وصف المصروف يجب أن يكون أقل من 200 حرف")]
        public string Description { get; set; } = string.Empty;

        [Required(ErrorMessage = "المبلغ مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "المبلغ يجب أن يكون أكبر من صفر")]
        [Column(TypeName = "decimal(18,2)")]
        public decimal Amount { get; set; }

        [Required(ErrorMessage = "فئة المصروف مطلوبة")]
        [StringLength(50, ErrorMessage = "فئة المصروف يجب أن تكون أقل من 50 حرف")]
        public string Category { get; set; } = string.Empty;

        [Required(ErrorMessage = "تاريخ المصروف مطلوب")]
        public DateTime ExpenseDate { get; set; } = DateTime.Now;

        [StringLength(500, ErrorMessage = "الملاحظات يجب أن تكون أقل من 500 حرف")]
        public string? Notes { get; set; }
    }
}
