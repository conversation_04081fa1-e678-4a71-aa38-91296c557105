@model MarbleWorkshopSystem.Models.CuttingOperation

@{
    ViewData["Title"] = "تسجيل عملية تقطيع";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-cut me-2"></i>
                        تسجيل عملية تقطيع جديدة
                    </h4>
                </div>

                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }

                    <form asp-action="Create" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <div class="row">
                            <!-- معلومات المنتج -->
                            <div class="col-md-6">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-cube me-2"></i>
                                            معلومات المنتج
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="ProductId" class="form-label">
                                                <i class="fas fa-box me-1"></i>
                                                المنتج <span class="text-danger">*</span>
                                            </label>
                                            <select asp-for="ProductId" class="form-select" id="productSelect" onchange="updateProductInfo()">
                                                <option value="">اختر المنتج</option>
                                                @foreach (var product in ViewBag.Products as List<MarbleWorkshopSystem.Models.Product>)
                                                {
                                                    <option value="@product.Id" 
                                                            data-name="@product.Name"
                                                            data-category="@product.Category"
                                                            data-price="@product.UnitPrice"
                                                            data-image="@product.ImagePath">
                                                        @product.Name - @product.Category
                                                    </option>
                                                }
                                            </select>
                                            <span asp-validation-for="ProductId" class="text-danger"></span>
                                        </div>

                                        <!-- معلومات المنتج المختار -->
                                        <div id="productInfo" class="card bg-light" style="display: none;">
                                            <div class="card-body p-3">
                                                <div class="row">
                                                    <div class="col-4">
                                                        <img id="productImage" src="" alt="" class="img-thumbnail" style="width: 100%; max-height: 80px; object-fit: cover;">
                                                    </div>
                                                    <div class="col-8">
                                                        <h6 id="productName" class="text-primary mb-1"></h6>
                                                        <p id="productCategory" class="text-muted mb-1"></p>
                                                        <p id="productPrice" class="text-success mb-0"></p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="CuttingDate" class="form-label">
                                                <i class="fas fa-calendar me-1"></i>
                                                تاريخ التقطيع <span class="text-danger">*</span>
                                            </label>
                                            <input asp-for="CuttingDate" type="date" class="form-control" />
                                            <span asp-validation-for="CuttingDate" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- بيانات التقطيع -->
                            <div class="col-md-6">
                                <div class="card border-warning">
                                    <div class="card-header bg-warning text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-calculator me-2"></i>
                                            بيانات التقطيع
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="OriginalQuantity" class="form-label">
                                                <i class="fas fa-cubes me-1"></i>
                                                الكمية الأصلية <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input asp-for="OriginalQuantity" class="form-control" 
                                                       placeholder="أدخل الكمية الأصلية" onchange="calculateWaste()" />
                                                <span class="input-group-text">وحدة</span>
                                            </div>
                                            <span asp-validation-for="OriginalQuantity" class="text-danger"></span>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="UsedQuantity" class="form-label">
                                                <i class="fas fa-check-circle me-1"></i>
                                                الكمية المستخدمة <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <input asp-for="UsedQuantity" class="form-control" 
                                                       placeholder="أدخل الكمية المستخدمة" onchange="calculateWaste()" />
                                                <span class="input-group-text">وحدة</span>
                                            </div>
                                            <span asp-validation-for="UsedQuantity" class="text-danger"></span>
                                        </div>

                                        <div class="mb-3">
                                            <label asp-for="WasteQuantity" class="form-label">
                                                <i class="fas fa-trash me-1"></i>
                                                كمية الهالك
                                            </label>
                                            <div class="input-group">
                                                <input asp-for="WasteQuantity" class="form-control" 
                                                       placeholder="سيتم حسابها تلقائياً" readonly />
                                                <span class="input-group-text">وحدة</span>
                                            </div>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                كمية الهالك = الكمية الأصلية - الكمية المستخدمة
                                            </div>
                                        </div>

                                        <!-- نسبة الهالك -->
                                        <div class="mb-3">
                                            <label class="form-label">
                                                <i class="fas fa-percentage me-1"></i>
                                                نسبة الهالك
                                            </label>
                                            <div class="input-group">
                                                <input type="text" id="wastePercentageDisplay" class="form-control" 
                                                       placeholder="سيتم حسابها تلقائياً" readonly />
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- الملاحظات -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card border-secondary">
                                    <div class="card-header bg-secondary text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-sticky-note me-2"></i>
                                            ملاحظات إضافية
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label asp-for="Notes" class="form-label">
                                                <i class="fas fa-comment me-1"></i>
                                                الملاحظات
                                            </label>
                                            <textarea asp-for="Notes" class="form-control" rows="3" 
                                                      placeholder="أدخل أي ملاحظات حول عملية التقطيع (اختياري)"></textarea>
                                            <span asp-validation-for="Notes" class="text-danger"></span>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                يمكنك إضافة ملاحظات حول سبب الهالك أو تفاصيل العملية
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ملخص العملية -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-info-circle me-2"></i>
                                            ملخص العملية
                                        </h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <ul class="mb-0">
                                                    <li><strong>تأثير على المخزون:</strong> سيتم خصم الكمية المستخدمة من المخزون</li>
                                                    <li><strong>تسجيل الهالك:</strong> سيتم إضافة كمية الهالك إلى سجل الهالك</li>
                                                </ul>
                                            </div>
                                            <div class="col-md-6">
                                                <ul class="mb-0">
                                                    <li><strong>تحديث تلقائي:</strong> سيتم تحديث المخزون فور حفظ العملية</li>
                                                    <li><strong>تتبع الهالك:</strong> ستظهر العملية في تقارير الهالك</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>
                                            تسجيل العملية
                                        </button>
                                        <button type="reset" class="btn btn-secondary" onclick="resetForm()">
                                            <i class="fas fa-undo me-1"></i>
                                            إعادة تعيين
                                        </button>
                                    </div>
                                    <div>
                                        <a asp-action="Index" class="btn btn-outline-primary">
                                            <i class="fas fa-list me-1"></i>
                                            العودة للقائمة
                                        </a>
                                        <a asp-action="Report" class="btn btn-info">
                                            <i class="fas fa-chart-line me-1"></i>
                                            تقرير الهالك
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        function updateProductInfo() {
            const select = document.getElementById('productSelect');
            const selectedOption = select.options[select.selectedIndex];
            const productInfo = document.getElementById('productInfo');
            
            if (selectedOption.value) {
                // عرض معلومات المنتج
                document.getElementById('productName').textContent = selectedOption.dataset.name;
                document.getElementById('productCategory').textContent = 'الفئة: ' + selectedOption.dataset.category;
                document.getElementById('productPrice').textContent = 'السعر: ' + parseFloat(selectedOption.dataset.price).toLocaleString('ar-EG', {style: 'currency', currency: 'EGP'});
                
                // عرض الصورة
                const image = document.getElementById('productImage');
                if (selectedOption.dataset.image) {
                    image.src = selectedOption.dataset.image;
                    image.alt = selectedOption.dataset.name;
                } else {
                    image.src = '/images/no-image.png';
                    image.alt = 'لا توجد صورة';
                }
                
                productInfo.style.display = 'block';
            } else {
                productInfo.style.display = 'none';
            }
        }
        
        function calculateWaste() {
            const originalQuantity = parseFloat(document.getElementById('OriginalQuantity').value) || 0;
            const usedQuantity = parseFloat(document.getElementById('UsedQuantity').value) || 0;
            
            if (originalQuantity > 0 && usedQuantity >= 0) {
                const wasteQuantity = originalQuantity - usedQuantity;
                const wastePercentage = (wasteQuantity / originalQuantity) * 100;
                
                // تحديث حقل كمية الهالك
                document.getElementById('WasteQuantity').value = wasteQuantity.toFixed(2);
                
                // تحديث نسبة الهالك
                document.getElementById('wastePercentageDisplay').value = wastePercentage.toFixed(2);
                
                // تلوين الحقول حسب نسبة الهالك
                const wastePercentageField = document.getElementById('wastePercentageDisplay');
                if (wastePercentage > 20) {
                    wastePercentageField.className = 'form-control bg-danger text-white';
                } else if (wastePercentage > 10) {
                    wastePercentageField.className = 'form-control bg-warning';
                } else {
                    wastePercentageField.className = 'form-control bg-success text-white';
                }
            }
        }
        
        function resetForm() {
            document.getElementById('productInfo').style.display = 'none';
            document.getElementById('WasteQuantity').value = '';
            document.getElementById('wastePercentageDisplay').value = '';
            document.getElementById('wastePercentageDisplay').className = 'form-control';
        }
        
        // التحقق من صحة البيانات
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            
            form.addEventListener('submit', function(e) {
                const originalQuantity = parseFloat(document.getElementById('OriginalQuantity').value) || 0;
                const usedQuantity = parseFloat(document.getElementById('UsedQuantity').value) || 0;
                
                if (usedQuantity > originalQuantity) {
                    e.preventDefault();
                    alert('الكمية المستخدمة لا يمكن أن تكون أكبر من الكمية الأصلية');
                    return false;
                }
                
                if (originalQuantity <= 0) {
                    e.preventDefault();
                    alert('يجب إدخال كمية أصلية صحيحة');
                    return false;
                }
            });
            
            // تعيين التاريخ الحالي كافتراضي
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('CuttingDate').value = today;
        });
    </script>
}
