using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using MarbleWorkshopSystem.Data;
using MarbleWorkshopSystem.Models;
using Microsoft.EntityFrameworkCore;
using System.Globalization;

namespace MarbleWorkshopSystem.Services
{
    public class SimpleReportService
    {
        private readonly ApplicationDbContext _context;

        public SimpleReportService(ApplicationDbContext context)
        {
            _context = context;
            
            // تكوين QuestPDF للاستخدام المجاني
            QuestPDF.Settings.License = LicenseType.Community;
        }

        // تقرير فاتورة المبيعات المبسط
        public async Task<byte[]> GenerateSalesInvoiceReportAsync(int invoiceId)
        {
            var invoice = await _context.SalesInvoices
                .Include(i => i.Customer)
                .Include(i => i.Items)
                .ThenInclude(item => item.Product)
                .FirstOrDefaultAsync(i => i.Id == invoiceId);

            if (invoice == null)
                throw new ArgumentException("الفاتورة غير موجودة");

            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(2, Unit.Centimetre);
                    page.PageColor(Colors.White);
                    page.DefaultTextStyle(x => x.FontSize(12));

                    page.Header()
                        .Height(120)
                        .Background(Colors.Blue.Lighten4)
                        .Padding(20)
                        .Row(row =>
                        {
                            row.RelativeItem().Column(column =>
                            {
                                column.Item().Text("ورشة الرخام المتقدمة")
                                    .FontSize(22)
                                    .Bold()
                                    .FontColor(Colors.Blue.Darken2);

                                column.Item().Text("Advanced Marble Workshop")
                                    .FontSize(12)
                                    .FontColor(Colors.Grey.Darken1);

                                column.Item().Text("📍 شارع الصناعة، المنطقة الصناعية | 📞 01234567890")
                                    .FontSize(10)
                                    .FontColor(Colors.Grey.Darken1);

                                column.Item().Text("📧 <EMAIL> | 🆔 الرقم الضريبي: 123456789")
                                    .FontSize(10)
                                    .FontColor(Colors.Grey.Darken1);
                            });

                            row.ConstantItem(200).Column(column =>
                            {
                                column.Item().AlignRight().Text("فاتورة مبيعات")
                                    .FontSize(20)
                                    .Bold()
                                    .FontColor(Colors.Blue.Darken2);

                                column.Item().AlignRight().Text("SALES INVOICE")
                                    .FontSize(12)
                                    .FontColor(Colors.Grey.Darken1);
                            });
                        });

                    page.Content()
                        .PaddingVertical(1, Unit.Centimetre)
                        .Column(column =>
                        {
                            // معلومات الفاتورة والعميل في صفين
                            column.Item().Row(row =>
                            {
                                // معلومات الفاتورة
                                row.RelativeItem().Column(col =>
                                {
                                    col.Item().Background(Colors.Blue.Lighten5)
                                        .Padding(15)
                                        .Column(invoiceCol =>
                                        {
                                            invoiceCol.Item().Text("معلومات الفاتورة")
                                                .FontSize(14)
                                                .Bold()
                                                .FontColor(Colors.Blue.Darken2);

                                            invoiceCol.Item().PaddingTop(5).Text($"رقم الفاتورة: {invoice.InvoiceNumber}")
                                                .Bold();
                                            invoiceCol.Item().Text($"تاريخ الإصدار: {invoice.InvoiceDate:dd/MM/yyyy}");
                                            invoiceCol.Item().Text($"حالة الفاتورة: {invoice.Status}")
                                                .FontColor(invoice.Status == "مؤكدة" ? Colors.Green.Darken1 : Colors.Orange.Darken1);
                                            invoiceCol.Item().Text($"تاريخ الطباعة: {DateTime.Now:dd/MM/yyyy HH:mm}")
                                                .FontSize(10)
                                                .FontColor(Colors.Grey.Darken1);
                                        });
                                });

                                // معلومات العميل
                                row.RelativeItem().Column(col =>
                                {
                                    col.Item().Background(Colors.Green.Lighten5)
                                        .Padding(15)
                                        .Column(customerCol =>
                                        {
                                            customerCol.Item().Text("معلومات العميل")
                                                .FontSize(14)
                                                .Bold()
                                                .FontColor(Colors.Green.Darken2);

                                            customerCol.Item().PaddingTop(5).Text($"اسم العميل: {invoice.Customer.Name}")
                                                .Bold();
                                            customerCol.Item().Text($"رقم الهاتف: {invoice.Customer.Phone ?? "غير محدد"}");
                                            customerCol.Item().Text($"العنوان: {invoice.Customer.Address ?? "غير محدد"}");
                                            if (!string.IsNullOrEmpty(invoice.Customer.Email))
                                            {
                                                customerCol.Item().Text($"البريد الإلكتروني: {invoice.Customer.Email}");
                                            }
                                        });
                                });
                            });

                            column.Item().PaddingTop(25);

                            // عنوان جدول الأصناف
                            column.Item().Text("تفاصيل الأصناف")
                                .FontSize(16)
                                .Bold()
                                .FontColor(Colors.Blue.Darken2);

                            column.Item().PaddingTop(10);

                            // جدول الأصناف
                            column.Item().Table(table =>
                            {
                                table.ColumnsDefinition(columns =>
                                {
                                    columns.ConstantColumn(30); // م
                                    columns.RelativeColumn(2.5f); // المنتج
                                    columns.RelativeColumn(1); // الطول
                                    columns.RelativeColumn(1); // العرض
                                    columns.RelativeColumn(1); // الكمية
                                    columns.RelativeColumn(1.2f); // سعر الوحدة
                                    columns.RelativeColumn(1.3f); // المجموع
                                    columns.RelativeColumn(1.5f); // ملاحظات
                                });

                                // رأس الجدول
                                table.Header(header =>
                                {
                                    header.Cell().Element(HeaderCellStyle).Text("م").Bold();
                                    header.Cell().Element(HeaderCellStyle).Text("المنتج / الوصف").Bold();
                                    header.Cell().Element(HeaderCellStyle).Text("الطول\n(متر)").Bold();
                                    header.Cell().Element(HeaderCellStyle).Text("العرض\n(متر)").Bold();
                                    header.Cell().Element(HeaderCellStyle).Text("المساحة\n(م²)").Bold();
                                    header.Cell().Element(HeaderCellStyle).Text("سعر المتر\n(جنيه)").Bold();
                                    header.Cell().Element(HeaderCellStyle).Text("إجمالي السعر\n(جنيه)").Bold();
                                    header.Cell().Element(HeaderCellStyle).Text("ملاحظات").Bold();

                                    static IContainer HeaderCellStyle(IContainer container)
                                    {
                                        return container
                                            .PaddingVertical(8)
                                            .PaddingHorizontal(5)
                                            .BorderBottom(2)
                                            .BorderColor(Colors.Blue.Darken2)
                                            .Background(Colors.Blue.Lighten4)
                                            .AlignCenter();
                                    }
                                });

                                // بيانات الجدول
                                var itemNumber = 1;
                                foreach (var item in invoice.Items)
                                {
                                    table.Cell().Element(DataCellStyle).Text(itemNumber.ToString()).AlignCenter();

                                    table.Cell().Element(DataCellStyle).Column(col =>
                                    {
                                        col.Item().Text(item.Product.Name).Bold();
                                        if (!string.IsNullOrEmpty(item.Product.Category))
                                        {
                                            col.Item().Text($"الفئة: {item.Product.Category}")
                                                .FontSize(9)
                                                .FontColor(Colors.Grey.Darken1);
                                        }
                                    });

                                    table.Cell().Element(DataCellStyle).Text(item.Length > 0 ? item.Length.ToString("F2") : "-").AlignCenter();
                                    table.Cell().Element(DataCellStyle).Text(item.Width > 0 ? item.Width.ToString("F2") : "-").AlignCenter();

                                    table.Cell().Element(DataCellStyle).Column(col =>
                                    {
                                        col.Item().Text(item.Quantity.ToString("F2")).Bold().AlignCenter();
                                        if (item.Length > 0 && item.Width > 0)
                                        {
                                            col.Item().Text($"({item.Length:F2} × {item.Width:F2})")
                                                .FontSize(8)
                                                .FontColor(Colors.Green.Darken1)
                                                .AlignCenter();
                                        }
                                    });

                                    table.Cell().Element(DataCellStyle).Text(item.UnitPrice.ToString("N2")).AlignCenter();
                                    table.Cell().Element(DataCellStyle).Text(item.TotalPrice.ToString("N2")).Bold().AlignCenter();
                                    table.Cell().Element(DataCellStyle).Text(item.Notes ?? "-").FontSize(9);

                                    itemNumber++;

                                    static IContainer DataCellStyle(IContainer container)
                                    {
                                        return container
                                            .BorderBottom(1)
                                            .BorderColor(Colors.Grey.Lighten2)
                                            .PaddingVertical(6)
                                            .PaddingHorizontal(4);
                                    }
                                }
                            });

                            column.Item().PaddingTop(20);

                            // ملخص الفاتورة
                            column.Item().Row(row =>
                            {
                                row.RelativeItem(2); // مساحة فارغة

                                row.RelativeItem().Background(Colors.Grey.Lighten5)
                                    .Border(1)
                                    .BorderColor(Colors.Grey.Medium)
                                    .Padding(15)
                                    .Column(totalsColumn =>
                                    {
                                        totalsColumn.Item().Text("ملخص الفاتورة")
                                            .FontSize(14)
                                            .Bold()
                                            .FontColor(Colors.Blue.Darken2)
                                            .AlignCenter();

                                        totalsColumn.Item().PaddingTop(10).Row(r =>
                                        {
                                            r.RelativeItem().Text("عدد الأصناف:");
                                            r.ConstantItem(80).Text($"{invoice.Items.Count} صنف").Bold().AlignRight();
                                        });

                                        totalsColumn.Item().Row(r =>
                                        {
                                            r.RelativeItem().Text("إجمالي المساحة:");
                                            r.ConstantItem(80).Text($"{invoice.Items.Sum(i => i.Quantity):F2} م²").Bold().AlignRight();
                                        });

                                        totalsColumn.Item().LineHorizontal(1).LineColor(Colors.Grey.Medium);

                                        totalsColumn.Item().Row(r =>
                                        {
                                            r.RelativeItem().Text("المجموع الفرعي:");
                                            r.ConstantItem(80).Text($"{invoice.SubTotal:N2} جنيه").AlignRight();
                                        });

                                        if (invoice.DiscountValue > 0)
                                        {
                                            totalsColumn.Item().Row(r =>
                                            {
                                                r.RelativeItem().Text($"الخصم ({invoice.DiscountValue}%):");
                                                r.ConstantItem(80).Text($"- {invoice.DiscountAmount:N2} جنيه")
                                                    .FontColor(Colors.Red.Medium).AlignRight();
                                            });
                                        }

                                        totalsColumn.Item().LineHorizontal(2).LineColor(Colors.Blue.Darken2);

                                        totalsColumn.Item().Row(r =>
                                        {
                                            r.RelativeItem().Text("المجموع النهائي:")
                                                .FontSize(14)
                                                .Bold()
                                                .FontColor(Colors.Blue.Darken2);
                                            r.ConstantItem(80).Text($"{invoice.TotalAmount:N2} جنيه")
                                                .FontSize(14)
                                                .Bold()
                                                .FontColor(Colors.Blue.Darken2)
                                                .AlignRight();
                                        });
                                    });
                            });

                            // الملاحظات
                            if (!string.IsNullOrEmpty(invoice.Notes))
                            {
                                column.Item().PaddingTop(20);
                                column.Item().Background(Colors.Yellow.Lighten4)
                                    .Border(1)
                                    .BorderColor(Colors.Orange.Medium)
                                    .Padding(10)
                                    .Column(notesCol =>
                                    {
                                        notesCol.Item().Text("ملاحظات هامة:")
                                            .FontSize(12)
                                            .Bold()
                                            .FontColor(Colors.Orange.Darken2);
                                        notesCol.Item().PaddingTop(5).Text(invoice.Notes)
                                            .FontSize(11);
                                    });
                            }

                            // قسم التوقيعات
                            column.Item().PaddingTop(30);
                            column.Item().Row(row =>
                            {
                                row.RelativeItem().Column(col =>
                                {
                                    col.Item().Text("توقيع العميل")
                                        .FontSize(12)
                                        .Bold()
                                        .AlignCenter();
                                    col.Item().PaddingTop(30).LineHorizontal(1)
                                        .LineColor(Colors.Black);
                                    col.Item().PaddingTop(5).Text("التاريخ: ___________")
                                        .FontSize(10)
                                        .AlignCenter();
                                });

                                row.ConstantItem(50); // مساحة فارغة

                                row.RelativeItem().Column(col =>
                                {
                                    col.Item().Text("توقيع المحاسب")
                                        .FontSize(12)
                                        .Bold()
                                        .AlignCenter();
                                    col.Item().PaddingTop(30).LineHorizontal(1)
                                        .LineColor(Colors.Black);
                                    col.Item().PaddingTop(5).Text("الاسم: ___________")
                                        .FontSize(10)
                                        .AlignCenter();
                                });

                                row.ConstantItem(50); // مساحة فارغة

                                row.RelativeItem().Column(col =>
                                {
                                    col.Item().Text("ختم الشركة")
                                        .FontSize(12)
                                        .Bold()
                                        .AlignCenter();
                                    col.Item().PaddingTop(30).Border(1)
                                        .BorderColor(Colors.Black)
                                        .Height(40)
                                        .Width(80)
                                        .AlignCenter();
                                });
                            });
                        });

                    page.Footer()
                        .Height(50)
                        .Background(Colors.Blue.Lighten5)
                        .Padding(10)
                        .Row(row =>
                        {
                            row.RelativeItem().Column(col =>
                            {
                                col.Item().Text("شكراً لتعاملكم معنا")
                                    .FontSize(12)
                                    .Bold()
                                    .FontColor(Colors.Blue.Darken2);
                                col.Item().Text("نتطلع لخدمتكم مرة أخرى")
                                    .FontSize(10)
                                    .FontColor(Colors.Grey.Darken1);
                            });

                            row.RelativeItem().Column(col =>
                            {
                                col.Item().AlignRight().Text($"تاريخ الطباعة: {DateTime.Now:dd/MM/yyyy}")
                                    .FontSize(10);
                                col.Item().AlignRight().Text($"وقت الطباعة: {DateTime.Now:HH:mm}")
                                    .FontSize(10);
                            });
                        });
                });
            });

            return document.GeneratePdf();
        }

        // تقرير فاتورة المشتريات المبسط
        public async Task<byte[]> GeneratePurchaseInvoiceReportAsync(int invoiceId)
        {
            var invoice = await _context.PurchaseInvoices
                .Include(i => i.Supplier)
                .Include(i => i.Items)
                .ThenInclude(item => item.Product)
                .FirstOrDefaultAsync(i => i.Id == invoiceId);

            if (invoice == null)
                throw new ArgumentException("الفاتورة غير موجودة");

            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(2, Unit.Centimetre);
                    page.PageColor(Colors.White);
                    page.DefaultTextStyle(x => x.FontSize(12));

                    page.Header()
                        .Height(120)
                        .Background(Colors.Green.Lighten4)
                        .Padding(20)
                        .Row(row =>
                        {
                            row.RelativeItem().Column(column =>
                            {
                                column.Item().Text("ورشة الرخام المتقدمة")
                                    .FontSize(22)
                                    .Bold()
                                    .FontColor(Colors.Green.Darken2);

                                column.Item().Text("Advanced Marble Workshop")
                                    .FontSize(12)
                                    .FontColor(Colors.Grey.Darken1);

                                column.Item().Text("📍 شارع الصناعة، المنطقة الصناعية | 📞 01234567890")
                                    .FontSize(10)
                                    .FontColor(Colors.Grey.Darken1);

                                column.Item().Text("📧 <EMAIL> | 🆔 الرقم الضريبي: 123456789")
                                    .FontSize(10)
                                    .FontColor(Colors.Grey.Darken1);
                            });

                            row.ConstantItem(200).Column(column =>
                            {
                                column.Item().AlignRight().Text("فاتورة مشتريات")
                                    .FontSize(20)
                                    .Bold()
                                    .FontColor(Colors.Green.Darken2);

                                column.Item().AlignRight().Text("PURCHASE INVOICE")
                                    .FontSize(12)
                                    .FontColor(Colors.Grey.Darken1);
                            });
                        });

                    page.Content()
                        .PaddingVertical(1, Unit.Centimetre)
                        .Column(column =>
                        {
                            // معلومات الفاتورة
                            column.Item().Text($"رقم الفاتورة: {invoice.InvoiceNumber}").Bold();
                            column.Item().Text($"التاريخ: {invoice.InvoiceDate:dd/MM/yyyy}");
                            column.Item().Text($"المورد: {invoice.Supplier.Name}").Bold();
                            column.Item().Text($"الهاتف: {invoice.Supplier.Phone}");
                            
                            column.Item().PaddingTop(20);

                            // جدول الأصناف
                            column.Item().Table(table =>
                            {
                                table.ColumnsDefinition(columns =>
                                {
                                    columns.RelativeColumn(3); // المنتج
                                    columns.RelativeColumn(1); // الطول
                                    columns.RelativeColumn(1); // العرض
                                    columns.RelativeColumn(1); // الكمية
                                    columns.RelativeColumn(1.5f); // سعر الوحدة
                                    columns.RelativeColumn(1.5f); // المجموع
                                });

                                // رأس الجدول
                                table.Header(header =>
                                {
                                    header.Cell().Element(CellStyle).Text("المنتج").Bold();
                                    header.Cell().Element(CellStyle).Text("الطول (م)").Bold();
                                    header.Cell().Element(CellStyle).Text("العرض (م)").Bold();
                                    header.Cell().Element(CellStyle).Text("الكمية (م²)").Bold();
                                    header.Cell().Element(CellStyle).Text("سعر الوحدة").Bold();
                                    header.Cell().Element(CellStyle).Text("المجموع").Bold();

                                    static IContainer CellStyle(IContainer container)
                                    {
                                        return container
                                            .PaddingVertical(5)
                                            .BorderBottom(1)
                                            .BorderColor(Colors.Black)
                                            .Background(Colors.Grey.Lighten4);
                                    }
                                });

                                // بيانات الجدول
                                foreach (var item in invoice.Items)
                                {
                                    table.Cell().Element(CellStyle).Text(item.Product.Name);
                                    table.Cell().Element(CellStyle).Text(item.Length > 0 ? item.Length.ToString("F2") : "-");
                                    table.Cell().Element(CellStyle).Text(item.Width > 0 ? item.Width.ToString("F2") : "-");
                                    table.Cell().Element(CellStyle).Text(item.Quantity.ToString("F2"));
                                    table.Cell().Element(CellStyle).Text(item.UnitPrice.ToString("F2") + " جنيه");
                                    table.Cell().Element(CellStyle).Text(item.TotalPrice.ToString("F2") + " جنيه");

                                    static IContainer CellStyle(IContainer container)
                                    {
                                        return container
                                            .BorderBottom(1)
                                            .BorderColor(Colors.Grey.Lighten2)
                                            .PaddingVertical(5);
                                    }
                                }
                            });

                            column.Item().PaddingTop(20);

                            // المجاميع
                            column.Item().AlignRight().Column(totalsColumn =>
                            {
                                totalsColumn.Item().Text($"المجموع الفرعي: {invoice.SubTotal:F2} جنيه");
                                
                                if (invoice.DiscountValue > 0)
                                {
                                    totalsColumn.Item().Text($"الخصم: {invoice.DiscountAmount:F2} جنيه");
                                }

                                totalsColumn.Item().Text($"المجموع النهائي: {invoice.TotalAmount:F2} جنيه").Bold();
                            });

                            // الملاحظات
                            if (!string.IsNullOrEmpty(invoice.Notes))
                            {
                                column.Item().PaddingTop(20);
                                column.Item().Text("ملاحظات:").Bold();
                                column.Item().Text(invoice.Notes);
                            }
                        });

                    page.Footer()
                        .Height(30)
                        .Padding(10)
                        .Text($"تاريخ الطباعة: {DateTime.Now:dd/MM/yyyy HH:mm} - شكراً لتعاملكم معنا");
                });
            });

            return document.GeneratePdf();
        }

        // تقرير المبيعات الشامل
        public async Task<byte[]> GenerateSalesReportAsync(DateTime fromDate, DateTime toDate, int? customerId = null)
        {
            var query = _context.SalesInvoices
                .Include(i => i.Customer)
                .Where(i => i.InvoiceDate >= fromDate && i.InvoiceDate <= toDate && i.Status == "مؤكدة");

            if (customerId.HasValue)
                query = query.Where(i => i.CustomerId == customerId.Value);

            var invoices = await query.OrderBy(i => i.InvoiceDate).ToListAsync();

            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(2, Unit.Centimetre);
                    page.PageColor(Colors.White);
                    page.DefaultTextStyle(x => x.FontSize(10));

                    page.Header()
                        .Height(60)
                        .Background(Colors.Blue.Lighten3)
                        .Padding(15)
                        .Text($"تقرير المبيعات من {fromDate:dd/MM/yyyy} إلى {toDate:dd/MM/yyyy}")
                        .FontSize(16)
                        .Bold();

                    page.Content()
                        .PaddingVertical(1, Unit.Centimetre)
                        .Column(column =>
                        {
                            if (invoices.Any())
                            {
                                column.Item().Table(table =>
                                {
                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn(1); // رقم الفاتورة
                                        columns.RelativeColumn(1); // التاريخ
                                        columns.RelativeColumn(2); // العميل
                                        columns.RelativeColumn(1); // المجموع
                                    });

                                    // رأس الجدول
                                    table.Header(header =>
                                    {
                                        header.Cell().Element(HeaderCellStyle).Text("رقم الفاتورة").Bold();
                                        header.Cell().Element(HeaderCellStyle).Text("التاريخ").Bold();
                                        header.Cell().Element(HeaderCellStyle).Text("العميل").Bold();
                                        header.Cell().Element(HeaderCellStyle).Text("المجموع").Bold();

                                        static IContainer HeaderCellStyle(IContainer container)
                                        {
                                            return container
                                                .PaddingVertical(5)
                                                .BorderBottom(1)
                                                .BorderColor(Colors.Black)
                                                .Background(Colors.Grey.Lighten4);
                                        }
                                    });

                                    // بيانات الجدول
                                    foreach (var invoice in invoices)
                                    {
                                        table.Cell().Element(CellStyle).Text(invoice.InvoiceNumber);
                                        table.Cell().Element(CellStyle).Text(invoice.InvoiceDate.ToString("dd/MM/yyyy"));
                                        table.Cell().Element(CellStyle).Text(invoice.Customer.Name);
                                        table.Cell().Element(CellStyle).Text(invoice.TotalAmount.ToString("F2") + " جنيه");

                                        static IContainer CellStyle(IContainer container)
                                        {
                                            return container
                                                .BorderBottom(1)
                                                .BorderColor(Colors.Grey.Lighten2)
                                                .PaddingVertical(3);
                                        }
                                    }

                                    // إجمالي التقرير
                                    table.Cell().Element(TotalCellStyle).Text("الإجمالي").Bold();
                                    table.Cell().Element(TotalCellStyle).Text("");
                                    table.Cell().Element(TotalCellStyle).Text("");
                                    table.Cell().Element(TotalCellStyle).Text(invoices.Sum(i => i.TotalAmount).ToString("F2") + " جنيه").Bold();

                                    static IContainer TotalCellStyle(IContainer container)
                                    {
                                        return container
                                            .BorderTop(2)
                                            .BorderColor(Colors.Black)
                                            .PaddingVertical(5)
                                            .Background(Colors.Blue.Lighten4);
                                    }
                                });
                            }
                            else
                            {
                                column.Item().Text("لا توجد مبيعات في الفترة المحددة")
                                    .FontSize(14);
                            }
                        });

                    page.Footer()
                        .Height(30)
                        .Padding(10)
                        .Text($"تاريخ الطباعة: {DateTime.Now:dd/MM/yyyy HH:mm}");
                });
            });

            return document.GeneratePdf();
        }

        // تقرير المشتريات الشامل
        public async Task<byte[]> GeneratePurchasesReportAsync(DateTime fromDate, DateTime toDate, int? supplierId = null)
        {
            var query = _context.PurchaseInvoices
                .Include(i => i.Supplier)
                .Where(i => i.InvoiceDate >= fromDate && i.InvoiceDate <= toDate && i.Status == "مؤكدة");

            if (supplierId.HasValue)
                query = query.Where(i => i.SupplierId == supplierId.Value);

            var invoices = await query.OrderBy(i => i.InvoiceDate).ToListAsync();

            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(2, Unit.Centimetre);
                    page.PageColor(Colors.White);
                    page.DefaultTextStyle(x => x.FontSize(10));

                    page.Header()
                        .Height(60)
                        .Background(Colors.Green.Lighten3)
                        .Padding(15)
                        .Text($"تقرير المشتريات من {fromDate:dd/MM/yyyy} إلى {toDate:dd/MM/yyyy}")
                        .FontSize(16)
                        .Bold();

                    page.Content()
                        .PaddingVertical(1, Unit.Centimetre)
                        .Column(column =>
                        {
                            if (invoices.Any())
                            {
                                column.Item().Table(table =>
                                {
                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn(1); // رقم الفاتورة
                                        columns.RelativeColumn(1); // التاريخ
                                        columns.RelativeColumn(2); // المورد
                                        columns.RelativeColumn(1); // المجموع
                                    });

                                    // رأس الجدول
                                    table.Header(header =>
                                    {
                                        header.Cell().Element(HeaderCellStyle).Text("رقم الفاتورة").Bold();
                                        header.Cell().Element(HeaderCellStyle).Text("التاريخ").Bold();
                                        header.Cell().Element(HeaderCellStyle).Text("المورد").Bold();
                                        header.Cell().Element(HeaderCellStyle).Text("المجموع").Bold();

                                        static IContainer HeaderCellStyle(IContainer container)
                                        {
                                            return container
                                                .PaddingVertical(5)
                                                .BorderBottom(1)
                                                .BorderColor(Colors.Black)
                                                .Background(Colors.Grey.Lighten4);
                                        }
                                    });

                                    // بيانات الجدول
                                    foreach (var invoice in invoices)
                                    {
                                        table.Cell().Element(CellStyle).Text(invoice.InvoiceNumber);
                                        table.Cell().Element(CellStyle).Text(invoice.InvoiceDate.ToString("dd/MM/yyyy"));
                                        table.Cell().Element(CellStyle).Text(invoice.Supplier.Name);
                                        table.Cell().Element(CellStyle).Text(invoice.TotalAmount.ToString("F2") + " جنيه");

                                        static IContainer CellStyle(IContainer container)
                                        {
                                            return container
                                                .BorderBottom(1)
                                                .BorderColor(Colors.Grey.Lighten2)
                                                .PaddingVertical(3);
                                        }
                                    }

                                    // إجمالي التقرير
                                    table.Cell().Element(TotalCellStyle).Text("الإجمالي").Bold();
                                    table.Cell().Element(TotalCellStyle).Text("");
                                    table.Cell().Element(TotalCellStyle).Text("");
                                    table.Cell().Element(TotalCellStyle).Text(invoices.Sum(i => i.TotalAmount).ToString("F2") + " جنيه").Bold();

                                    static IContainer TotalCellStyle(IContainer container)
                                    {
                                        return container
                                            .BorderTop(2)
                                            .BorderColor(Colors.Black)
                                            .PaddingVertical(5)
                                            .Background(Colors.Green.Lighten4);
                                    }
                                });
                            }
                            else
                            {
                                column.Item().Text("لا توجد مشتريات في الفترة المحددة")
                                    .FontSize(14);
                            }
                        });

                    page.Footer()
                        .Height(30)
                        .Padding(10)
                        .Text($"تاريخ الطباعة: {DateTime.Now:dd/MM/yyyy HH:mm}");
                });
            });

            return document.GeneratePdf();
        }

        // تقرير المخزون التفصيلي
        public async Task<byte[]> GenerateInventoryReportAsync()
        {
            var products = await _context.Products
                .OrderBy(p => p.Category)
                .ThenBy(p => p.Name)
                .ToListAsync();

            // جلب جميع عناصر المخزون مرة واحدة
            var allInventoryItems = await _context.Inventories.ToListAsync();

            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(2, Unit.Centimetre);
                    page.PageColor(Colors.White);
                    page.DefaultTextStyle(x => x.FontSize(10));

                    page.Header()
                        .Height(80)
                        .Background(Colors.Purple.Lighten4)
                        .Padding(15)
                        .Row(row =>
                        {
                            row.RelativeItem().Column(column =>
                            {
                                column.Item().Text("ورشة الرخام المتقدمة")
                                    .FontSize(18)
                                    .Bold()
                                    .FontColor(Colors.Purple.Darken2);

                                column.Item().Text("تقرير المخزون التفصيلي")
                                    .FontSize(14)
                                    .FontColor(Colors.Purple.Darken1);
                            });

                            row.ConstantItem(150).Column(column =>
                            {
                                column.Item().AlignRight().Text($"تاريخ التقرير: {DateTime.Now:dd/MM/yyyy}");
                                column.Item().AlignRight().Text($"عدد المنتجات: {products.Count}");
                            });
                        });

                    page.Content()
                        .PaddingVertical(1, Unit.Centimetre)
                        .Column(column =>
                        {
                            if (products.Any())
                            {
                                column.Item().Table(table =>
                                {
                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.ConstantColumn(30); // م
                                        columns.RelativeColumn(2); // المنتج
                                        columns.RelativeColumn(1); // الفئة
                                        columns.RelativeColumn(1); // الكمية الإجمالية
                                        columns.RelativeColumn(1); // الكمية المتاحة
                                        columns.RelativeColumn(1); // الهالك
                                        columns.RelativeColumn(1); // سعر الوحدة
                                        columns.RelativeColumn(1); // قيمة المخزون
                                    });

                                    // رأس الجدول
                                    table.Header(header =>
                                    {
                                        header.Cell().Element(HeaderCellStyle).Text("م").Bold();
                                        header.Cell().Element(HeaderCellStyle).Text("اسم المنتج").Bold();
                                        header.Cell().Element(HeaderCellStyle).Text("الفئة").Bold();
                                        header.Cell().Element(HeaderCellStyle).Text("الكمية\n(م²)").Bold();
                                        header.Cell().Element(HeaderCellStyle).Text("متاح\n(م²)").Bold();
                                        header.Cell().Element(HeaderCellStyle).Text("هالك\n(م²)").Bold();
                                        header.Cell().Element(HeaderCellStyle).Text("سعر الوحدة\n(جنيه)").Bold();
                                        header.Cell().Element(HeaderCellStyle).Text("قيمة المخزون\n(جنيه)").Bold();

                                        static IContainer HeaderCellStyle(IContainer container)
                                        {
                                            return container
                                                .PaddingVertical(8)
                                                .PaddingHorizontal(5)
                                                .BorderBottom(2)
                                                .BorderColor(Colors.Purple.Darken2)
                                                .Background(Colors.Purple.Lighten4)
                                                .AlignCenter();
                                        }
                                    });

                                    // بيانات الجدول
                                    var itemNumber = 1;
                                    decimal totalValue = 0;
                                    decimal totalAvailable = 0;
                                    decimal totalWaste = 0;

                                    foreach (var product in products)
                                    {
                                        // حساب الكميات من جدول المخزون
                                        var productInventory = allInventoryItems
                                            .FirstOrDefault(i => i.ProductId == product.Id);

                                        var totalQty = productInventory?.Quantity ?? 0;
                                        var availableQty = productInventory?.AvailableQuantity ?? 0;
                                        var wasteQty = productInventory?.WasteQuantity ?? 0;
                                        var inventoryValue = availableQty * product.UnitPrice;

                                        totalAvailable += availableQty;
                                        totalWaste += wasteQty;
                                        totalValue += inventoryValue;

                                        table.Cell().Element(DataCellStyle).Text(itemNumber.ToString()).AlignCenter();
                                        table.Cell().Element(DataCellStyle).Text(product.Name);
                                        table.Cell().Element(DataCellStyle).Text(product.Category ?? "-");
                                        table.Cell().Element(DataCellStyle).Text(totalQty.ToString("F2")).AlignCenter();
                                        table.Cell().Element(DataCellStyle).Text(availableQty.ToString("F2")).AlignCenter();
                                        table.Cell().Element(DataCellStyle).Text(wasteQty.ToString("F2")).AlignCenter();
                                        table.Cell().Element(DataCellStyle).Text(product.UnitPrice.ToString("N2")).AlignCenter();
                                        table.Cell().Element(DataCellStyle).Text(inventoryValue.ToString("N2")).AlignCenter();

                                        itemNumber++;

                                        static IContainer DataCellStyle(IContainer container)
                                        {
                                            return container
                                                .BorderBottom(1)
                                                .BorderColor(Colors.Grey.Lighten2)
                                                .PaddingVertical(5)
                                                .PaddingHorizontal(3);
                                        }
                                    }

                                    // إجمالي التقرير
                                    table.Cell().Element(TotalCellStyle).Text("الإجمالي").Bold();
                                    table.Cell().Element(TotalCellStyle).Text("");
                                    table.Cell().Element(TotalCellStyle).Text("");
                                    table.Cell().Element(TotalCellStyle).Text("");
                                    table.Cell().Element(TotalCellStyle).Text(totalAvailable.ToString("F2")).Bold().AlignCenter();
                                    table.Cell().Element(TotalCellStyle).Text(totalWaste.ToString("F2")).Bold().AlignCenter();
                                    table.Cell().Element(TotalCellStyle).Text("");
                                    table.Cell().Element(TotalCellStyle).Text(totalValue.ToString("N2")).Bold().AlignCenter();

                                    static IContainer TotalCellStyle(IContainer container)
                                    {
                                        return container
                                            .BorderTop(2)
                                            .BorderColor(Colors.Purple.Darken2)
                                            .PaddingVertical(8)
                                            .Background(Colors.Purple.Lighten4);
                                    }
                                });
                            }
                            else
                            {
                                column.Item().Text("لا توجد منتجات في المخزون")
                                    .FontSize(14)
                                    .AlignCenter();
                            }
                        });

                    page.Footer()
                        .Height(30)
                        .Padding(10)
                        .Text($"تاريخ الطباعة: {DateTime.Now:dd/MM/yyyy HH:mm} - تقرير المخزون");
                });
            });

            return document.GeneratePdf();
        }

        // تقرير اختبار بسيط
        public async Task<byte[]> GenerateTestReportAsync()
        {
            await Task.Delay(1); // لجعل الدالة async

            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(2, Unit.Centimetre);
                    page.PageColor(Colors.White);
                    page.DefaultTextStyle(x => x.FontSize(12));

                    page.Header()
                        .Height(80)
                        .Background(Colors.Blue.Lighten3)
                        .Padding(15)
                        .Text("تقرير اختبار PDF")
                        .FontSize(18)
                        .Bold()
                        .AlignCenter();

                    page.Content()
                        .PaddingVertical(1, Unit.Centimetre)
                        .Column(column =>
                        {
                            column.Item().Text("هذا تقرير اختبار لتأكد من عمل PDF بشكل صحيح")
                                .FontSize(14)
                                .AlignCenter();

                            column.Item().PaddingTop(20).Text($"تاريخ الإنشاء: {DateTime.Now:dd/MM/yyyy HH:mm}")
                                .FontSize(12)
                                .AlignCenter();

                            column.Item().PaddingTop(20).Table(table =>
                            {
                                table.ColumnsDefinition(columns =>
                                {
                                    columns.RelativeColumn(1);
                                    columns.RelativeColumn(1);
                                });

                                table.Header(header =>
                                {
                                    header.Cell().Background(Colors.Grey.Lighten2)
                                        .Padding(5).Text("العنصر").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten2)
                                        .Padding(5).Text("القيمة").Bold();
                                });

                                table.Cell().Padding(5).Text("اسم النظام");
                                table.Cell().Padding(5).Text("نظام إدارة ورشة الرخام");

                                table.Cell().Padding(5).Text("الإصدار");
                                table.Cell().Padding(5).Text("1.0.0");

                                table.Cell().Padding(5).Text("حالة PDF");
                                table.Cell().Padding(5).Text("يعمل بشكل صحيح ✓");
                            });
                        });

                    page.Footer()
                        .Height(50)
                        .Background(Colors.Grey.Lighten4)
                        .Padding(10)
                        .AlignCenter()
                        .Text("تم إنشاء هذا التقرير بواسطة نظام إدارة ورشة الرخام")
                        .FontSize(10);
                });
            });

            return document.GeneratePdf();
        }
    }
}
