@model MarbleWorkshopSystem.Models.Receipt

@{
    ViewData["Title"] = "إنشاء سند قبض جديد";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-receipt me-2"></i>
                        إنشاء سند قبض جديد
                    </h3>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="ReceiptNumber" class="form-label">رقم السند</label>
                                    <input asp-for="ReceiptNumber" class="form-control" readonly />
                                    <span asp-validation-for="ReceiptNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="ReceiptDate" class="form-label">تاريخ السند</label>
                                    <input asp-for="ReceiptDate" type="date" class="form-control" />
                                    <span asp-validation-for="ReceiptDate" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="CustomerId" class="form-label">العميل</label>
                                    <select asp-for="CustomerId" class="form-select" id="customerSelect">
                                        <option value="">اختر العميل</option>
                                        @foreach (var customer in ViewBag.Customers as IEnumerable<MarbleWorkshopSystem.Models.Customer>)
                                        {
                                            <option value="@customer.Id">@customer.Name</option>
                                        }
                                    </select>
                                    <span asp-validation-for="CustomerId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رصيد العميل الحالي</label>
                                    <div class="input-group">
                                        <input type="text" id="customerBalance" class="form-control" readonly placeholder="اختر العميل أولاً" />
                                        <span class="input-group-text">ج.م</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Amount" class="form-label">المبلغ</label>
                                    <div class="input-group">
                                        <input asp-for="Amount" type="number" step="0.01" class="form-control" />
                                        <span class="input-group-text">ج.م</span>
                                    </div>
                                    <span asp-validation-for="Amount" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="PaymentMethod" class="form-label">طريقة الدفع</label>
                                    <select asp-for="PaymentMethod" class="form-select">
                                        <option value="">اختر طريقة الدفع</option>
                                        <option value="نقدي">نقدي</option>
                                        <option value="شيك">شيك</option>
                                        <option value="تحويل بنكي">تحويل بنكي</option>
                                        <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                                    </select>
                                    <span asp-validation-for="PaymentMethod" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Notes" class="form-label">الملاحظات</label>
                            <textarea asp-for="Notes" class="form-control" rows="3" placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                            <span asp-validation-for="Notes" class="text-danger"></span>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                العودة للقائمة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                حفظ السند
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // تحديث رصيد العميل عند تغيير العميل
            $('#customerSelect').change(function() {
                var customerId = $(this).val();
                if (customerId) {
                    $.get('@Url.Action("GetCustomerBalance")', { customerId: customerId })
                        .done(function(data) {
                            $('#customerBalance').val(data.balance.toLocaleString('ar-EG') + ' ج.م');
                        })
                        .fail(function() {
                            $('#customerBalance').val('خطأ في تحميل الرصيد');
                        });
                } else {
                    $('#customerBalance').val('');
                }
            });
        });
    </script>
}
