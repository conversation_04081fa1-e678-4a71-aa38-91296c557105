@model IEnumerable<MarbleWorkshopSystem.Models.Supplier>

@{
    ViewData["Title"] = "تقرير الموردين";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-truck me-2"></i>
                        تقرير الموردين الشامل
                    </h4>
                </div>

                <div class="card-body">
                    <!-- إحصائيات عامة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h3>@Model.Count()</h3>
                                    <p class="mb-0">إجمالي الموردين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3>@Model.Count(s => s.IsActive)</h3>
                                    <p class="mb-0">الموردين النشطين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3>@Model.Sum(s => s.Balance).ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h3>
                                    <p class="mb-0">إجمالي الأرصدة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h3>@Model.Count(s => s.Balance > 0)</h3>
                                    <p class="mb-0">موردين برصيد موجب</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        العودة للتقارير
                                    </a>
                                    <a asp-controller="Suppliers" asp-action="Index" class="btn btn-primary">
                                        <i class="fas fa-truck me-1"></i>
                                        إدارة الموردين
                                    </a>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-success">
                                        <i class="fas fa-file-excel me-1"></i>
                                        تصدير Excel
                                    </button>
                                    <button type="button" class="btn btn-info" onclick="window.print()">
                                        <i class="fas fa-print me-1"></i>
                                        طباعة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تحليل الأرصدة -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-plus-circle me-2"></i>
                                        الموردين برصيد موجب
                                    </h6>
                                </div>
                                <div class="card-body">
                                    @{
                                        var positiveBalance = Model.Where(s => s.Balance > 0).ToList();
                                    }
                                    <div class="row">
                                        <div class="col-6">
                                            <h4 class="text-success">@positiveBalance.Count</h4>
                                            <small>عدد الموردين</small>
                                        </div>
                                        <div class="col-6">
                                            <h4 class="text-success">@positiveBalance.Sum(s => s.Balance).ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h4>
                                            <small>إجمالي الرصيد</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-minus-circle me-2"></i>
                                        الموردين برصيد سالب
                                    </h6>
                                </div>
                                <div class="card-body">
                                    @{
                                        var negativeBalance = Model.Where(s => s.Balance < 0).ToList();
                                    }
                                    <div class="row">
                                        <div class="col-6">
                                            <h4 class="text-danger">@negativeBalance.Count</h4>
                                            <small>عدد الموردين</small>
                                        </div>
                                        <div class="col-6">
                                            <h4 class="text-danger">@negativeBalance.Sum(s => s.Balance).ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h4>
                                            <small>إجمالي الرصيد</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول تفاصيل الموردين -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-table me-2"></i>
                                تفاصيل الموردين
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="suppliersTable">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>اسم المورد</th>
                                            <th>رقم الهاتف</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>العنوان</th>
                                            <th>الرصيد الحالي</th>
                                            <th>الرصيد الافتتاحي</th>
                                            <th>نوع الرصيد الافتتاحي</th>
                                            <th>الحالة</th>
                                            <th>تاريخ الإنشاء</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var supplier in Model.OrderBy(s => s.Name))
                                        {
                                            <tr class="@(supplier.IsActive ? "" : "table-secondary")">
                                                <td>
                                                    <strong>@supplier.Name</strong>
                                                </td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(supplier.Phone))
                                                    {
                                                        <span>@supplier.Phone</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">-</span>
                                                    }
                                                </td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(supplier.Email))
                                                    {
                                                        <span>@supplier.Email</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">-</span>
                                                    }
                                                </td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(supplier.Address))
                                                    {
                                                        <span>@supplier.Address</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">-</span>
                                                    }
                                                </td>
                                                <td>
                                                    <span class="badge @(supplier.Balance > 0 ? "bg-success" : supplier.Balance < 0 ? "bg-danger" : "bg-secondary") fs-6">
                                                        @supplier.Balance.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                                                    </span>
                                                </td>
                                                <td>
                                                    @if (supplier.OpeningBalance != 0)
                                                    {
                                                        <span class="badge bg-info fs-6">@supplier.OpeningBalance.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">-</span>
                                                    }
                                                </td>
                                                <td>
                                                    @if (supplier.OpeningBalance != 0)
                                                    {
                                                        <span class="badge @(supplier.OpeningBalanceType == MarbleWorkshopSystem.Models.BalanceType.Debit ? "bg-warning" : "bg-primary")">
                                                            @(supplier.OpeningBalanceType == MarbleWorkshopSystem.Models.BalanceType.Debit ? "مدين" : "دائن")
                                                        </span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">-</span>
                                                    }
                                                </td>
                                                <td>
                                                    @if (supplier.IsActive)
                                                    {
                                                        <span class="badge bg-success">
                                                            <i class="fas fa-check me-1"></i>نشط
                                                        </span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-secondary">
                                                            <i class="fas fa-pause me-1"></i>غير نشط
                                                        </span>
                                                    }
                                                </td>
                                                <td>
                                                    <small>@supplier.CreatedDate.ToString("dd/MM/yyyy")</small>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                    <tfoot class="table-light">
                                        <tr>
                                            <th>الإجمالي:</th>
                                            <th>@Model.Count() مورد</th>
                                            <th colspan="2">@Model.Count(s => s.IsActive) نشط</th>
                                            <th>@Model.Sum(s => s.Balance).ToString("C", new System.Globalization.CultureInfo("ar-EG"))</th>
                                            <th>@Model.Sum(s => s.OpeningBalance).ToString("C", new System.Globalization.CultureInfo("ar-EG"))</th>
                                            <th colspan="3">
                                                <span class="badge bg-primary">@Model.Count(s => s.OpeningBalance != 0) برصيد افتتاحي</span>
                                            </th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- ملخص إحصائي -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        إحصائيات الحالة
                                    </h6>
                                </div>
                                <div class="card-body">
                                    @{
                                        var activeSuppliers = Model.Count(s => s.IsActive);
                                        var inactiveSuppliers = Model.Count(s => !s.IsActive);
                                        var withOpeningBalance = Model.Count(s => s.OpeningBalance != 0);
                                    }
                                    <div class="row">
                                        <div class="col-4 text-center">
                                            <div class="text-success">
                                                <h4>@activeSuppliers</h4>
                                                <small>موردين نشطين</small>
                                            </div>
                                        </div>
                                        <div class="col-4 text-center">
                                            <div class="text-secondary">
                                                <h4>@inactiveSuppliers</h4>
                                                <small>موردين غير نشطين</small>
                                            </div>
                                        </div>
                                        <div class="col-4 text-center">
                                            <div class="text-info">
                                                <h4>@withOpeningBalance</h4>
                                                <small>برصيد افتتاحي</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-calculator me-2"></i>
                                        متوسطات مهمة
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <p class="mb-1"><strong>متوسط الرصيد للمورد:</strong></p>
                                            <h5 class="text-success">@((Model.Count() > 0 ? Model.Sum(s => s.Balance) / Model.Count() : 0).ToString("C", new System.Globalization.CultureInfo("ar-EG")))</h5>
                                        </div>
                                        <div class="col-6">
                                            <p class="mb-1"><strong>متوسط الرصيد الافتتاحي:</strong></p>
                                            <h5 class="text-info">@((withOpeningBalance > 0 ? Model.Where(s => s.OpeningBalance != 0).Sum(s => s.OpeningBalance) / withOpeningBalance : 0).ToString("C", new System.Globalization.CultureInfo("ar-EG")))</h5>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-6">
                                            <p class="mb-1"><strong>نسبة الموردين النشطين:</strong></p>
                                            <h6 class="text-primary">@(Model.Count() > 0 ? ((decimal)activeSuppliers / Model.Count() * 100).ToString("F1") : "0")%</h6>
                                        </div>
                                        <div class="col-6">
                                            <p class="mb-1"><strong>نسبة الموردين برصيد موجب:</strong></p>
                                            <h6 class="text-secondary">@(Model.Count() > 0 ? ((decimal)positiveBalance.Count / Model.Count() * 100).ToString("F1") : "0")%</h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات التقرير -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6><i class="fas fa-info-circle me-2"></i>معلومات التقرير:</h6>
                                            <ul class="mb-0">
                                                <li>تاريخ التقرير: @DateTime.Now.ToString("dd/MM/yyyy HH:mm")</li>
                                                <li>إجمالي الموردين: @Model.Count() مورد</li>
                                                <li>الموردين النشطين: @activeSuppliers مورد</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6><i class="fas fa-lightbulb me-2"></i>ملاحظات:</h6>
                                            <ul class="mb-0">
                                                <li>الرصيد الموجب: الشركة مدينة للمورد</li>
                                                <li>الرصيد السالب: المورد مدين للشركة</li>
                                                <li>الرصيد الافتتاحي: الرصيد عند بداية التعامل</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // تفعيل DataTables للجدول
            $('#suppliersTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
                },
                "pageLength": 25,
                "order": [[0, "asc"]], // ترتيب حسب اسم المورد
                "columnDefs": [
                    { "type": "currency", "targets": [4, 5] } // تحديد نوع العملة للأعمدة المالية
                ]
            });
        });
    </script>
}
