@model MarbleWorkshopSystem.ViewModels.CustomersReportViewModel

@{
    ViewData["Title"] = "تقرير العملاء";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        تقرير العملاء الشامل
                    </h4>
                </div>

                <div class="card-body">
                    <!-- إحصائيات عامة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3>@Model.TotalCustomers</h3>
                                    <p class="mb-0">إجمالي العملاء</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3>@Model.ActiveCustomers</h3>
                                    <p class="mb-0">العملاء النشطين</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3>@Model.TotalBalance.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h3>
                                    <p class="mb-0">إجمالي الأرصدة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h3>@Model.CustomerDetails.Sum(c => c.TotalSales).ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h3>
                                    <p class="mb-0">إجمالي المبيعات</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>
                                        العودة للتقارير
                                    </a>
                                    <a asp-controller="Customers" asp-action="Index" class="btn btn-primary">
                                        <i class="fas fa-users me-1"></i>
                                        إدارة العملاء
                                    </a>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-success">
                                        <i class="fas fa-file-excel me-1"></i>
                                        تصدير Excel
                                    </button>
                                    <button type="button" class="btn btn-info" onclick="window.print()">
                                        <i class="fas fa-print me-1"></i>
                                        طباعة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تحليل الأرصدة -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-plus-circle me-2"></i>
                                        العملاء برصيد موجب
                                    </h6>
                                </div>
                                <div class="card-body">
                                    @{
                                        var positiveBalance = Model.CustomerDetails.Where(c => c.Balance > 0).ToList();
                                    }
                                    <div class="row">
                                        <div class="col-6">
                                            <h4 class="text-success">@positiveBalance.Count</h4>
                                            <small>عدد العملاء</small>
                                        </div>
                                        <div class="col-6">
                                            <h4 class="text-success">@positiveBalance.Sum(c => c.Balance).ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h4>
                                            <small>إجمالي الرصيد</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-minus-circle me-2"></i>
                                        العملاء برصيد سالب
                                    </h6>
                                </div>
                                <div class="card-body">
                                    @{
                                        var negativeBalance = Model.CustomerDetails.Where(c => c.Balance < 0).ToList();
                                    }
                                    <div class="row">
                                        <div class="col-6">
                                            <h4 class="text-danger">@negativeBalance.Count</h4>
                                            <small>عدد العملاء</small>
                                        </div>
                                        <div class="col-6">
                                            <h4 class="text-danger">@negativeBalance.Sum(c => c.Balance).ToString("C", new System.Globalization.CultureInfo("ar-EG"))</h4>
                                            <small>إجمالي الرصيد</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أفضل العملاء -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-star me-2"></i>
                                        أفضل 10 عملاء (حسب المبيعات)
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        @foreach (var customer in Model.CustomerDetails.OrderByDescending(c => c.TotalSales).Take(10))
                                        {
                                            <div class="col-md-6 mb-2">
                                                <div class="card border-info">
                                                    <div class="card-body p-2">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <div>
                                                                <strong>@customer.CustomerName</strong>
                                                                @if (!string.IsNullOrEmpty(customer.Phone))
                                                                {
                                                                    <br><small class="text-muted">@customer.Phone</small>
                                                                }
                                                            </div>
                                                            <div class="text-end">
                                                                <span class="badge bg-success fs-6">@customer.TotalSales.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</span>
                                                                <br><small class="text-muted">@customer.InvoiceCount فاتورة</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول تفاصيل العملاء -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-table me-2"></i>
                                تفاصيل العملاء
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="customersTable">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>اسم العميل</th>
                                            <th>رقم الهاتف</th>
                                            <th>الرصيد الحالي</th>
                                            <th>إجمالي المبيعات</th>
                                            <th>عدد الفواتير</th>
                                            <th>آخر عملية شراء</th>
                                            <th>متوسط الفاتورة</th>
                                            <th>حالة العميل</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var customer in Model.CustomerDetails)
                                        {
                                            <tr>
                                                <td>
                                                    <strong>@customer.CustomerName</strong>
                                                </td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(customer.Phone))
                                                    {
                                                        <span>@customer.Phone</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">-</span>
                                                    }
                                                </td>
                                                <td>
                                                    <span class="badge @(customer.Balance > 0 ? "bg-success" : customer.Balance < 0 ? "bg-danger" : "bg-secondary") fs-6">
                                                        @customer.Balance.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                                                    </span>
                                                </td>
                                                <td>
                                                    <strong>@customer.TotalSales.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</strong>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info fs-6">@customer.InvoiceCount</span>
                                                </td>
                                                <td>
                                                    @if (customer.LastSaleDate.HasValue)
                                                    {
                                                        <small>@customer.LastSaleDate.Value.ToString("dd/MM/yyyy")</small>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">لا توجد مبيعات</span>
                                                    }
                                                </td>
                                                <td>
                                                    @if (customer.InvoiceCount > 0)
                                                    {
                                                        <span>@((customer.TotalSales / customer.InvoiceCount).ToString("C", new System.Globalization.CultureInfo("ar-EG")))</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">-</span>
                                                    }
                                                </td>
                                                <td>
                                                    @if (customer.TotalSales > 0)
                                                    {
                                                        if (customer.LastSaleDate.HasValue && customer.LastSaleDate.Value > DateTime.Now.AddMonths(-3))
                                                        {
                                                            <span class="badge bg-success">نشط</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="badge bg-warning">غير نشط</span>
                                                        }
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-secondary">جديد</span>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                    <tfoot class="table-light">
                                        <tr>
                                            <th>الإجمالي:</th>
                                            <th>@Model.TotalCustomers عميل</th>
                                            <th>@Model.TotalBalance.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</th>
                                            <th>@Model.CustomerDetails.Sum(c => c.TotalSales).ToString("C", new System.Globalization.CultureInfo("ar-EG"))</th>
                                            <th>@Model.CustomerDetails.Sum(c => c.InvoiceCount) فاتورة</th>
                                            <th colspan="3">
                                                <span class="badge bg-primary">@Model.ActiveCustomers عميل نشط</span>
                                            </th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- ملخص إحصائي -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        إحصائيات النشاط
                                    </h6>
                                </div>
                                <div class="card-body">
                                    @{
                                        var activeCustomers = Model.CustomerDetails.Where(c => c.LastSaleDate.HasValue && c.LastSaleDate.Value > DateTime.Now.AddMonths(-3)).Count();
                                        var inactiveCustomers = Model.CustomerDetails.Where(c => c.LastSaleDate.HasValue && c.LastSaleDate.Value <= DateTime.Now.AddMonths(-3)).Count();
                                        var newCustomers = Model.CustomerDetails.Where(c => !c.LastSaleDate.HasValue).Count();
                                    }
                                    <div class="row">
                                        <div class="col-4 text-center">
                                            <div class="text-success">
                                                <h4>@activeCustomers</h4>
                                                <small>عملاء نشطين</small>
                                            </div>
                                        </div>
                                        <div class="col-4 text-center">
                                            <div class="text-warning">
                                                <h4>@inactiveCustomers</h4>
                                                <small>عملاء غير نشطين</small>
                                            </div>
                                        </div>
                                        <div class="col-4 text-center">
                                            <div class="text-info">
                                                <h4>@newCustomers</h4>
                                                <small>عملاء جدد</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-calculator me-2"></i>
                                        متوسطات مهمة
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-6">
                                            <p class="mb-1"><strong>متوسط المبيعات للعميل:</strong></p>
                                            <h5 class="text-success">@((Model.TotalCustomers > 0 ? Model.CustomerDetails.Sum(c => c.TotalSales) / Model.TotalCustomers : 0).ToString("C", new System.Globalization.CultureInfo("ar-EG")))</h5>
                                        </div>
                                        <div class="col-6">
                                            <p class="mb-1"><strong>متوسط الرصيد للعميل:</strong></p>
                                            <h5 class="text-info">@((Model.TotalCustomers > 0 ? Model.TotalBalance / Model.TotalCustomers : 0).ToString("C", new System.Globalization.CultureInfo("ar-EG")))</h5>
                                        </div>
                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-6">
                                            <p class="mb-1"><strong>متوسط الفواتير للعميل:</strong></p>
                                            <h6 class="text-primary">@(Model.ActiveCustomers > 0 ? (Model.CustomerDetails.Sum(c => c.InvoiceCount) / (decimal)Model.ActiveCustomers).ToString("F1") : "0") فاتورة</h6>
                                        </div>
                                        <div class="col-6">
                                            <p class="mb-1"><strong>متوسط قيمة الفاتورة:</strong></p>
                                            <h6 class="text-secondary">@(Model.CustomerDetails.Sum(c => c.InvoiceCount) > 0 ? (Model.CustomerDetails.Sum(c => c.TotalSales) / Model.CustomerDetails.Sum(c => c.InvoiceCount)).ToString("C", new System.Globalization.CultureInfo("ar-EG")) : "0")</h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات التقرير -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6><i class="fas fa-info-circle me-2"></i>معلومات التقرير:</h6>
                                            <ul class="mb-0">
                                                <li>تاريخ التقرير: @DateTime.Now.ToString("dd/MM/yyyy HH:mm")</li>
                                                <li>إجمالي العملاء: @Model.TotalCustomers عميل</li>
                                                <li>العملاء النشطين: @Model.ActiveCustomers عميل</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6><i class="fas fa-lightbulb me-2"></i>ملاحظات:</h6>
                                            <ul class="mb-0">
                                                <li>العميل النشط: له مبيعات خلال آخر 3 أشهر</li>
                                                <li>الرصيد الموجب: العميل مدين للشركة</li>
                                                <li>الرصيد السالب: الشركة مدينة للعميل</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // تفعيل DataTables للجدول
            $('#customersTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
                },
                "pageLength": 25,
                "order": [[3, "desc"]], // ترتيب حسب إجمالي المبيعات
                "columnDefs": [
                    { "type": "currency", "targets": [2, 3, 6] } // تحديد نوع العملة للأعمدة المالية
                ]
            });
        });
    </script>
}
