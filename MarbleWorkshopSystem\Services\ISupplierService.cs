using MarbleWorkshopSystem.Models;

namespace MarbleWorkshopSystem.Services
{
    public interface ISupplierService
    {
        Task<IEnumerable<Supplier>> GetAllSuppliersAsync();
        Task<Supplier?> GetSupplierByIdAsync(int id);
        Task<IEnumerable<Supplier>> SearchSuppliersAsync(string searchTerm);
        Task<Supplier> CreateSupplierAsync(Supplier supplier);
        Task<Supplier> UpdateSupplierAsync(Supplier supplier);
        Task DeleteSupplierAsync(int id);
        Task<bool> SupplierExistsAsync(int id);
        Task<decimal> GetSupplierBalanceAsync(int supplierId);
        Task UpdateSupplierBalanceAsync(int supplierId, decimal amount);
        Task<IEnumerable<PurchaseInvoice>> GetSupplierInvoicesAsync(int supplierId);
        Task<IEnumerable<Payment>> GetSupplierPaymentsAsync(int supplierId);
        Task<decimal> GetSupplierTotalPurchasesAsync(int supplierId);
        Task<decimal> GetSupplierTotalPaymentsAsync(int supplierId);
    }
}
