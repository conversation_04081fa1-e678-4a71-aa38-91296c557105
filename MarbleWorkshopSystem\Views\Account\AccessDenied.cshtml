@{
    ViewData["Title"] = "الوصول مرفوض";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}

<div class="text-center">
    <div class="mb-4">
        <i class="fas fa-lock fa-4x text-warning mb-3"></i>
        <h2 class="text-danger">غير مصرح بالدخول</h2>
        <p class="text-muted">ليس لديك صلاحية للوصول إلى هذه الصفحة</p>
    </div>

    <div class="alert alert-warning" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>تنبيه:</strong> تحتاج إلى تسجيل الدخول أولاً للوصول إلى النظام
    </div>

    <div class="d-grid gap-2">
        @if (User.Identity?.IsAuthenticated == true)
        {
            <a asp-controller="Home" asp-action="Index" class="btn btn-login btn-lg text-white">
                <i class="fas fa-home me-2"></i>
                العودة للرئيسية
            </a>
        }
        else
        {
            <a asp-action="Login" class="btn btn-login btn-lg text-white">
                <i class="fas fa-sign-in-alt me-2"></i>
                تسجيل الدخول
            </a>
        }

        <a href="javascript:history.back()" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            العودة للصفحة السابقة
        </a>
    </div>
</div>

@section Scripts {
    <script>
        // إعادة توجيه تلقائي بعد 5 ثوان للمستخدمين غير المسجلين
        @if (User.Identity?.IsAuthenticated != true)
        {
            @:setTimeout(function() {
            @:    window.location.href = '@Url.Action("Login", "Account")';
            @:}, 5000);
            @:
            @:// عداد تنازلي
            @:let countdown = 5;
            @:const countdownElement = document.createElement('p');
            @:countdownElement.className = 'text-muted mt-3';
            @:countdownElement.innerHTML = 'سيتم إعادة توجيهك لصفحة الدخول خلال <span class="text-primary">' + countdown + '</span> ثانية';
            @:document.querySelector('.d-grid').appendChild(countdownElement);
            @:
            @:const timer = setInterval(function() {
            @:    countdown--;
            @:    countdownElement.innerHTML = 'سيتم إعادة توجيهك لصفحة الدخول خلال <span class="text-primary">' + countdown + '</span> ثانية';
            @:
            @:    if (countdown <= 0) {
            @:        clearInterval(timer);
            @:        countdownElement.innerHTML = '<span class="text-success">جاري إعادة التوجيه...</span>';
            @:    }
            @:}, 1000);
        }
    </script>
}
