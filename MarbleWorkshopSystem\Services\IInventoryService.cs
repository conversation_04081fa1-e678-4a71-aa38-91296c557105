using MarbleWorkshopSystem.Models;

namespace MarbleWorkshopSystem.Services
{
    public interface IInventoryService
    {
        Task<IEnumerable<Inventory>> GetAllInventoryAsync();
        Task<Inventory?> GetInventoryByIdAsync(int id);
        Task<Inventory?> GetInventoryByProductIdAsync(int productId);
        Task<IEnumerable<Inventory>> GetLowStockItemsAsync();
        Task<Inventory> UpdateInventoryAsync(Inventory inventory);
        Task AddStockAsync(int productId, decimal quantity);
        Task ReduceStockAsync(int productId, decimal quantity);
        Task AddWasteAsync(int productId, decimal wasteQuantity);
        Task<bool> HasSufficientStockAsync(int productId, decimal requiredQuantity);
        Task<decimal> GetAvailableStockAsync(int productId);
        Task<IEnumerable<Inventory>> GetInventoryAlertsAsync();
    }
}
