@model MarbleWorkshopSystem.Models.Receipt

@{
    ViewData["Title"] = "تفاصيل سند القبض";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-receipt me-2"></i>
                        تفاصيل سند القبض - @Model.ReceiptNumber
                    </h3>
                    <div>
                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning me-2">
                            <i class="fas fa-edit me-1"></i>
                            تعديل
                        </a>
                        <a asp-action="Print" asp-route-id="@Model.Id" class="btn btn-info me-2" target="_blank">
                            <i class="fas fa-print me-1"></i>
                            طباعة
                        </a>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            العودة
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        معلومات السند
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>رقم السند:</strong></td>
                                            <td class="text-primary">@Model.ReceiptNumber</td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاريخ السند:</strong></td>
                                            <td>@Model.ReceiptDate.ToString("dd/MM/yyyy")</td>
                                        </tr>
                                        <tr>
                                            <td><strong>المبلغ:</strong></td>
                                            <td>
                                                <span class="badge bg-success fs-6">
                                                    @Model.Amount.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>طريقة الدفع:</strong></td>
                                            <td>
                                                <span class="badge bg-info">@Model.PaymentMethod</span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-user me-2"></i>
                                        معلومات العميل
                                    </h5>
                                </div>
                                <div class="card-body">
                                    @if (Model.Customer != null)
                                    {
                                        <table class="table table-borderless">
                                            <tr>
                                                <td><strong>اسم العميل:</strong></td>
                                                <td>@Model.Customer.Name</td>
                                            </tr>
                                            <tr>
                                                <td><strong>رقم الهاتف:</strong></td>
                                                <td>@Model.Customer.Phone</td>
                                            </tr>
                                            <tr>
                                                <td><strong>العنوان:</strong></td>
                                                <td>@Model.Customer.Address</td>
                                            </tr>
                                            <tr>
                                                <td><strong>الرصيد الحالي:</strong></td>
                                                <td>
                                                    <span class="badge @(Model.Customer.Balance >= 0 ? "bg-success" : "bg-danger") fs-6">
                                                        @Model.Customer.Balance.ToString("C", new System.Globalization.CultureInfo("ar-EG"))
                                                    </span>
                                                </td>
                                            </tr>
                                        </table>
                                    }
                                    else
                                    {
                                        <p class="text-muted">لا توجد معلومات العميل</p>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Notes))
                    {
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header bg-warning text-dark">
                                        <h5 class="mb-0">
                                            <i class="fas fa-sticky-note me-2"></i>
                                            الملاحظات
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <p class="mb-0">@Model.Notes</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- معلومات إضافية -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-clock me-2"></i>
                                        معلومات النظام
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>تاريخ السند:</strong> @Model.ReceiptDate.ToString("dd/MM/yyyy")</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>رقم السند:</strong> @Model.ReceiptNumber</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // عرض رسائل النجاح والخطأ
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
                toastr.success('@TempData["SuccessMessage"]');
            </text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>
                toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
    </script>
}
