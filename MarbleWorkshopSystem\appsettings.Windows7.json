{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Server=.\\SQLEXPRESS;Database=MarbleWorkshopDB;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true;Encrypt=false;ConnectRetryCount=3;ConnectRetryInterval=10", "SqlServerConnection": "Server=.\\SQLEXPRESS;Database=MarbleWorkshopDB;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true;Encrypt=false", "LocalDbConnection": "Server=(localdb)\\mssqllocaldb;Database=MarbleWorkshopDB;Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true"}, "Kestrel": {"Endpoints": {"Http": {"Url": "http://localhost:5000"}}, "Limits": {"MaxConcurrentConnections": 50, "MaxConcurrentUpgradedConnections": 50, "MaxRequestBodySize": 5242880, "KeepAliveTimeout": "00:01:00", "RequestHeadersTimeout": "00:00:15"}}, "Application": {"Name": "نظام إدارة ورشة الرخام", "Version": "1.0.0", "Environment": "Windows7Compatible", "SupportedCultures": ["ar-EG", "en-US"], "DefaultCulture": "ar-EG", "TimeZone": "Egypt Standard Time"}, "Security": {"RequireHttps": false, "CookieSecure": "None", "SessionTimeout": 30, "MaxLoginAttempts": 5, "LockoutDuration": 15, "EnableAntiforgery": true, "SameSiteCookies": "Lax"}, "Features": {"EnableDetailedErrors": false, "EnableDeveloperExceptionPage": false, "EnableSwagger": false, "EnableResponseCompression": true, "EnableResponseCaching": true, "EnableSession": true, "EnableCors": false}, "Database": {"CommandTimeout": 30, "EnableSensitiveDataLogging": false, "EnableDetailedErrors": false, "MaxRetryCount": 3, "MaxRetryDelay": "00:00:30", "MigrationsAssembly": "MarbleWorkshopSystem"}, "FileStorage": {"MaxFileSize": 5242880, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".xls", ".xlsx"], "UploadPath": "wwwroot\\uploads", "TempPath": "wwwroot\\temp", "BackupPath": "Backups"}, "Performance": {"EnableOutputCaching": true, "CacheDuration": 300, "EnableCompression": true, "CompressionLevel": "Fastest", "EnableMinification": false, "EnableBundling": false}, "Windows7": {"DisableHttps": true, "DisableTLS12": false, "EnableLegacySupport": true, "UseCompatibilityMode": true, "DisableHSTS": true, "MaxConcurrentConnections": 50}, "Compatibility": {"DisableModernFeatures": true, "UseBasicAuthentication": false, "EnableIE11Support": true, "DisableWebSockets": true, "UsePolyfills": true}}