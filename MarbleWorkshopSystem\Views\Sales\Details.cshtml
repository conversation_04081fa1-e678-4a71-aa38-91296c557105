@model MarbleWorkshopSystem.Models.SalesInvoice

@{
    ViewData["Title"] = "تفاصيل فاتورة المبيعات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-file-invoice me-2"></i>
                        فاتورة مبيعات رقم: @Model.InvoiceNumber
                    </h3>
                    <div>
                        @if (Model.Status == "مسودة")
                        {
                            <form asp-action="Confirm" asp-route-id="@Model.Id" method="post" class="d-inline">
                                <button type="submit" class="btn btn-success me-2" onclick="return confirm('هل أنت متأكد من تأكيد الفاتورة؟')">
                                    <i class="fas fa-check me-1"></i>
                                    تأكيد الفاتورة
                                </button>
                            </form>
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning me-2">
                                <i class="fas fa-edit me-1"></i>
                                تعديل
                            </a>
                        }
                        @if (Model.Status == "مؤكدة")
                        {
                            <form asp-action="Cancel" asp-route-id="@Model.Id" method="post" class="d-inline">
                                <button type="submit" class="btn btn-danger me-2" onclick="return confirm('هل أنت متأكد من إلغاء الفاتورة؟')">
                                    <i class="fas fa-times me-1"></i>
                                    إلغاء الفاتورة
                                </button>
                            </form>
                        }
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-print me-1"></i>
                                طباعة
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" asp-action="Print" asp-route-id="@Model.Id" asp-route-format="A4" target="_blank">
                                    <i class="fas fa-file-alt me-2"></i>A4
                                </a></li>
                                <li><a class="dropdown-item" asp-action="Print" asp-route-id="@Model.Id" asp-route-format="A5" target="_blank">
                                    <i class="fas fa-file me-2"></i>A5
                                </a></li>
                                <li><a class="dropdown-item" asp-action="Print" asp-route-id="@Model.Id" asp-route-format="POS" target="_blank">
                                    <i class="fas fa-receipt me-2"></i>POS
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- معلومات الفاتورة -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">معلومات الفاتورة</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>رقم الفاتورة:</strong></td>
                                            <td>@Model.InvoiceNumber</td>
                                        </tr>
                                        <tr>
                                            <td><strong>التاريخ:</strong></td>
                                            <td>@Model.InvoiceDate.ToString("dd/MM/yyyy")</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الحالة:</strong></td>
                                            <td>
                                                @switch (Model.Status)
                                                {
                                                    case "مسودة":
                                                        <span class="badge bg-warning">مسودة</span>
                                                        break;
                                                    case "مؤكدة":
                                                        <span class="badge bg-success">مؤكدة</span>
                                                        break;
                                                    case "ملغية":
                                                        <span class="badge bg-danger">ملغية</span>
                                                        break;
                                                    default:
                                                        <span class="badge bg-secondary">@Model.Status</span>
                                                        break;
                                                }
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">معلومات العميل</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>اسم العميل:</strong></td>
                                            <td>@Model.Customer.Name</td>
                                        </tr>
                                        <tr>
                                            <td><strong>الهاتف:</strong></td>
                                            <td>@Model.Customer.Phone</td>
                                        </tr>
                                        <tr>
                                            <td><strong>العنوان:</strong></td>
                                            <td>@Model.Customer.Address</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أصناف الفاتورة -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">أصناف الفاتورة</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الطول (م)</th>
                                            <th>العرض (م)</th>
                                            <th>الكمية (م²)</th>
                                            <th>سعر الوحدة</th>
                                            <th>المجموع</th>
                                            <th>ملاحظات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var item in Model.Items)
                                        {
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        @if (!string.IsNullOrEmpty(item.Product.ImagePath))
                                                        {
                                                            <img src="@item.Product.ImagePath" alt="@item.Product.Name" 
                                                                 class="img-thumbnail me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                                        }
                                                        <div>
                                                            <strong>
                                                                <a href="@Url.Action("Details", "Products", new { id = item.Product.Id })"
                                                                   class="text-decoration-none" title="عرض تفاصيل المنتج">
                                                                    @item.Product.Name
                                                                </a>
                                                            </strong>
                                                            <br><small class="text-muted">@item.Product.Category</small>
                                                            @if (!string.IsNullOrEmpty(item.Product.Description))
                                                            {
                                                                <br><small class="text-info">@item.Product.Description</small>
                                                            }
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    @if (item.Length > 0)
                                                    {
                                                        <span class="text-info">@item.Length.ToString("F2")</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">-</span>
                                                    }
                                                </td>
                                                <td>
                                                    @if (item.Width > 0)
                                                    {
                                                        <span class="text-info">@item.Width.ToString("F2")</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">-</span>
                                                    }
                                                </td>
                                                <td>
                                                    <strong>@item.Quantity.ToString("F2")</strong>
                                                    @if (item.Length > 0 && item.Width > 0)
                                                    {
                                                        <br><small class="text-success">(@item.Length.ToString("F2") × @item.Width.ToString("F2"))</small>
                                                    }
                                                </td>
                                                <td>@item.UnitPrice.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</td>
                                                <td><strong>@item.TotalPrice.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</strong></td>
                                                <td>
                                                    @if (!string.IsNullOrEmpty(item.Notes))
                                                    {
                                                        <span>@item.Notes</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">-</span>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- ملخص الفاتورة -->
                    <div class="row">
                        <div class="col-md-6 offset-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">ملخص الفاتورة</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td>المجموع الفرعي:</td>
                                            <td class="text-end">@Model.SubTotal.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</td>
                                        </tr>
                                        @if (Model.DiscountValue > 0)
                                        {
                                            <tr>
                                                <td>
                                                    الخصم 
                                                    @if (Model.DiscountType == DiscountType.Percentage)
                                                    {
                                                        <span class="text-muted">(@Model.DiscountValue%)</span>
                                                    }
                                                    :
                                                </td>
                                                <td class="text-end text-warning">-@Model.DiscountAmount.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</td>
                                            </tr>
                                        }
                                        <tr class="table-active">
                                            <td><strong>المجموع النهائي:</strong></td>
                                            <td class="text-end"><strong>@Model.TotalAmount.ToString("C", new System.Globalization.CultureInfo("ar-EG"))</strong></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الملاحظات -->
                    @if (!string.IsNullOrEmpty(Model.Notes))
                    {
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">ملاحظات</h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="mb-0">@Model.Notes</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- أزرار الإجراءات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <a asp-action="Index" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>
                                    العودة للقائمة
                                </a>
                                <div>
                                    @if (Model.Status == "مسودة")
                                    {
                                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning me-2">
                                            <i class="fas fa-edit me-1"></i>
                                            تعديل الفاتورة
                                        </a>
                                    }
                                    <a asp-action="Print" asp-route-id="@Model.Id" class="btn btn-primary" target="_blank">
                                        <i class="fas fa-print me-1"></i>
                                        طباعة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // عرض رسائل النجاح والخطأ
        @if (TempData["SuccessMessage"] != null)
        {
            <text>
                toastr.success('@TempData["SuccessMessage"]');
            </text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
            <text>
                toastr.error('@TempData["ErrorMessage"]');
            </text>
        }
    </script>
}
